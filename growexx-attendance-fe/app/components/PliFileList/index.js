import React, { useState, useEffect } from 'react';
import { List, Button, notification } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import request from 'utils/request';
import { API_ENDPOINTS } from 'utils/endpoints';
import styled from 'styled-components';

const FileList = styled(List)`
  margin-top: 16px;
  .ant-list-item {
    padding: 8px;
    border-radius: 4px;
    &:hover {
      background: #f0f0f0;
    }
  }
`;

const PliFileList = ({ employeeId, onRefresh }) => {
  const [fileList, setFileList] = useState([]);
  const [loading, setLoading] = useState(false);

  const fetchFiles = async () => {
    setLoading(true);
    try {
      const files = await request(
        `${API_ENDPOINTS.PLI}/pli/files/${employeeId}`,
      );
      setFileList(files || []);
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to fetch files',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleView = file => {
    const supportedTypes = ['.jpg', '.jpeg', '.png', '.pdf', '.txt'];
    const fileExt = file.fileName
      .substring(file.fileName.lastIndexOf('.'))
      .toLowerCase();

    if (supportedTypes.includes(fileExt)) {
      window.open(file.fileUrl, '_blank');
    } else {
      const link = document.createElement('a');
      link.href = file.fileUrl;
      link.download = file.fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  useEffect(() => {
    if (employeeId) {
      fetchFiles();
    }
  }, [employeeId, onRefresh]);

  return fileList.length > 0 ? (
    <FileList
      loading={loading}
      size="small"
      dataSource={fileList}
      renderItem={file => (
        <List.Item
          actions={[
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleView(file)}
            >
              View
            </Button>,
          ]}
        >
          <List.Item.Meta
            title={file.fileName}
            description={new Date(file.uploadedAt).toLocaleString()}
          />
        </List.Item>
      )}
    />
  ) : null;
};

PliFileList.propTypes = {
  employeeId: PropTypes.string.isRequired,
  onRefresh: PropTypes.number,
};

export default PliFileList;
