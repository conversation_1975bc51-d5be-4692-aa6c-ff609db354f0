/**
 * TechRoadmapMentee Component
 * Displays a form for mentors to assign courses to mentees
 */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Form,
  Button,
  Card,
  Row,
  Col,
  Select,
  message,
  DatePicker,
} from 'antd';
import request from 'utils/request';
import { API_ENDPOINTS } from 'containers/constants';
import moment from 'moment';

const { Option } = Select;

const TechRoadmapMentee = ({ menteeId, mentorId, menteeRole, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [courses, setCourses] = useState([]);

  // Fetch courses based on mentee's role
  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);
        const response = await request(API_ENDPOINTS.TECH_ROADMAP_COURSE, {
          method: 'GET',
        });

        if (response && response.data) {
          // Filter courses by mentee's role if role is provided
          const filteredCourses = menteeRole
            ? response.data.filter(course => course.role === menteeRole)
            : response.data;

          setCourses(filteredCourses);
        }
      } catch (error) {
        message.error('Failed to fetch courses');
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [menteeRole]);

  const onFinish = async values => {
    try {
      setLoading(true);

      // Find the selected course to get its details
      const selectedCourse = courses.find(
        course => course._id === values.courseName,
      );

      if (!selectedCourse) {
        message.error('Course not found');
        return;
      }

      // Prepare data for submission
      const data = {
        courseName: selectedCourse.courseName,
        description: selectedCourse.description,
        duration: selectedCourse.duration,
        learningMedium: selectedCourse.learningMedium,
        dueDate: values.targetMonth
          ? values.targetMonth.format('YYYY-MM-DD')
          : null,
        menteeId,
        mentorId,
        completionStatus: 'Assigned',
      };

      const response = await request(API_ENDPOINTS.TECH_ROADMAP_ASSIGN, {
        method: 'POST',
        body: data,
      });

      if (response && response.status === 200) {
        message.success('Course assigned successfully');
        form.resetFields();
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      message.error(error.response?.message || 'Failed to assign course');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title="Assign a Course" style={{ maxWidth: 800, margin: '0 auto' }}>
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="courseName"
              label="Course Name"
              rules={[
                {
                  required: true,
                  message: 'Please select a course',
                },
              ]}
            >
              <Select placeholder="Select a course" loading={loading}>
                {courses.map(course => (
                  <Option key={course._id} value={course._id}>
                    {course.courseName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="targetMonth"
              label="Target Month"
              rules={[
                {
                  required: true,
                  message: 'Please select a target month',
                },
              ]}
            >
              <DatePicker
                picker="month"
                style={{ width: '100%' }}
                disabledDate={current =>
                  current && current < moment().startOf('month')
                }
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'right' }}>
            <Button
              type="default"
              style={{
                marginRight: 8,
                backgroundColor: '#6c5ce7',
                color: 'white',
                borderColor: '#6c5ce7',
              }}
              onClick={() => form.resetFields()}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{
                backgroundColor: '#6c5ce7',
                borderColor: '#6c5ce7',
              }}
            >
              Assign Course
            </Button>
          </Col>
        </Row>
      </Form>
    </Card>
  );
};

TechRoadmapMentee.propTypes = {
  menteeId: PropTypes.string.isRequired,
  mentorId: PropTypes.string.isRequired,
  menteeRole: PropTypes.string,
  onSuccess: PropTypes.func,
};

export default TechRoadmapMentee;
