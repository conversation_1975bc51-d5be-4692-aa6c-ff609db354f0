/**
 * TechRoadmapAssignments Component
 * Displays assigned courses for a mentee and allows mentors to assign new courses
 */
import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Card, Table, Button, Modal, message, Tag, Tooltip } from 'antd';
import {
  PlusOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  StarOutlined,
} from '@ant-design/icons';
import request from 'utils/request';
import { API_ENDPOINTS } from 'containers/constants';
import moment from 'moment';
import TechRoadmapMentee from '../TechRoadmapMentee';

const TechRoadmapAssignments = ({
  menteeId,
  mentorId,
  menteeRole,
  designation,
}) => {
  const [loading, setLoading] = useState(false);
  const [assignments, setAssignments] = useState([]);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const response = await request(
        `${API_ENDPOINTS.TECH_ROADMAP_MENTEE}/${menteeId}`,
        {
          method: 'GET',
        },
      );

      if (response && response.data) {
        setAssignments(response.data);
      }
    } catch (error) {
      message.error('Failed to fetch course assignments');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (menteeId) {
      fetchAssignments();
    }
  }, [menteeId]);

  const handleAssignCourse = () => {
    setIsModalVisible(true);
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
  };

  const getStatusTag = status => {
    switch (status) {
      case 'Completed':
        return (
          <Tag color="green" icon={<CheckCircleOutlined />}>
            Completed
          </Tag>
        );
      case 'Rated':
        return (
          <Tag color="blue" icon={<StarOutlined />}>
            Rated
          </Tag>
        );
      default:
        return (
          <Tag color="orange" icon={<ClockCircleOutlined />}>
            Assigned
          </Tag>
        );
    }
  };

  const columns = [
    {
      title: 'Course Name',
      dataIndex: 'courseName',
      key: 'courseName',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: text => (
        <Tooltip title={text}>
          <div
            style={{
              maxWidth: 200,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: 'Duration',
      dataIndex: 'duration',
      key: 'duration',
    },
    {
      title: 'Learning Medium',
      dataIndex: 'learningMedium',
      key: 'learningMedium',
    },
    {
      title: 'Due Date',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: date => (date ? moment(date).format('MMM YYYY') : 'N/A'),
    },
    {
      title: 'Status',
      dataIndex: 'completionStatus',
      key: 'completionStatus',
      render: status => getStatusTag(status),
    },
    {
      title: 'Rating',
      dataIndex: 'rating',
      key: 'rating',
      render: rating => (rating ? `${rating}/5` : 'N/A'),
    },
  ];

  return (
    <div style={{ marginTop: 24 }}>
      <Card
        title="Tech Roadmap Assignments"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAssignCourse}
            style={{
              backgroundColor: '#6c5ce7',
              borderColor: '#6c5ce7',
            }}
          >
            Assign Course
          </Button>
        }
      >
        <Table
          dataSource={assignments}
          columns={columns}
          rowKey="_id"
          loading={loading}
          pagination={{ pageSize: 5 }}
          locale={{ emptyText: 'No courses assigned yet' }}
        />
      </Card>

      <Modal
        title="Assign a New Course"
        visible={isModalVisible}
        onCancel={handleModalCancel}
        footer={null}
        width={800}
      >
        <TechRoadmapMentee
          menteeId={menteeId}
          mentorId={mentorId}
          menteeRole={designation || menteeRole}
          onSuccess={() => {
            setIsModalVisible(false);
            fetchAssignments();
          }}
        />
      </Modal>
    </div>
  );
};

TechRoadmapAssignments.propTypes = {
  menteeId: PropTypes.string.isRequired,
  mentorId: PropTypes.string.isRequired,
  menteeRole: PropTypes.string,
  designation: PropTypes.string,
};

export default TechRoadmapAssignments;
