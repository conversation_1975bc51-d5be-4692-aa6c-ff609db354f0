import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Button, notification, Spin } from 'antd';
import {
  Container,
  Label,
  Value,
  ButtonContainer,
  StyledButton,
} from './Styles/StyledFreezePli';
import request from '../../utils/request';
import { API_ENDPOINTS } from '../../containers/EmployeeProfile/constants';
import { EmployeeProfileContext } from '../../containers/EmployeeProfile/context';

const FreezePLI = ({ ids, mentorId, menteeId, calculatedPLIScore }) => {
  const { pliDataMap, profile, selectedMonth } =
    useContext(EmployeeProfileContext) || {};

  const [freezing, setFreezing] = useState(false);

  const formatDataForSubmission = () => {
    if (!pliDataMap || !profile) {
      return null;
    }

    // Convert month name to number
    const months = {
      january: 1,
      february: 2,
      march: 3,
      april: 4,
      may: 5,
      june: 6,
      july: 7,
      august: 8,
      september: 9,
      october: 10,
      november: 11,
      december: 12,
    };

    const month =
      selectedMonth && months[selectedMonth.toLowerCase()]
        ? months[selectedMonth.toLowerCase()]
        : new Date().getMonth() + 1;
    const year = new Date().getFullYear();

    // Format the data for submission
    const projectRatings = [];

    if (Object.keys(pliDataMap).length === 0) {
      return null;
    }

    Object.keys(pliDataMap).forEach(projectKey => {
      const [projectId, projectType] = projectKey.split('_');
      const projectData = pliDataMap[projectKey];
      console.log(`Project ID: ${projectId}, Project Type: ${projectType}`);
      // Create parameter scores array
      const parameterScores = [];

      // Group parameters by their parent parameter
      const allParams = new Set();
      const allSprints = new Set();

      // Collect all parameters and sprints
      Object.keys(projectData).forEach(sprintName => {
        if (
          sprintName !== 'parameterId' &&
          sprintName !== 'comments' &&
          sprintName !== 'projectWeightage'
        ) {
          allSprints.add(sprintName);
          Object.keys(projectData[sprintName]).forEach(paramName => {
            allParams.add(paramName);
          });
        }
      });

      // Get parameterId from context data or from ids prop
      let parameterId = null;

      if (projectData && projectData.parameterId) {
        ({ parameterId } = projectData); // Destructures and assigns to outer variable
      } else if (ids && ids.parameterIds && ids.parameterIds[projectKey]) {
        parameterId = ids.parameterIds[projectKey];
      }

      // Create a parameter score object
      const parameterScore = {
        parameterId,
        comments: projectData.comments || '',
        childScores: [],
      };

      // Add child scores for each parameter
      allParams.forEach(paramName => {
        const childScore = {
          childParameterId: paramName,
          sprintScores: [],
        };

        // Add sprint scores for this parameter
        allSprints.forEach(sprintName => {
          if (projectData[sprintName] && projectData[sprintName][paramName]) {
            const sprintScore = {
              sprintNumber: sprintName,
              score: projectData[sprintName][paramName].value,
              comment: '',
            };
            childScore.sprintScores.push(sprintScore);
          }
        });

        parameterScore.childScores.push(childScore);
      });

      parameterScores.push(parameterScore);

      // Add project rating
      const projectRating = {
        projectId,
        projectWeightage: parseInt(projectData.projectWeightage || 100, 10),
        parameterScores,
      };

      projectRatings.push(projectRating);
    });

    // Log menteeId and mentorId

    const formattedData = {
      menteeId,
      mentorId,
      month,
      year,
      projectRatings,
      isFreezePayroll: true, // Indicate this is a freeze operation
    };

    return formattedData;
  };

  const handleFreezePLI = async () => {
    try {
      setFreezing(true);

      const formattedData = formatDataForSubmission();

      if (!formattedData) {
        notification.error({
          message: 'Error',
          description: 'No data available to freeze',
        });
        setFreezing(false);
        return;
      }

      const response = await request(API_ENDPOINTS.UPDATE_SPRINT_SCORES, {
        method: 'POST',
        body: JSON.stringify(formattedData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response && response.status === 1) {
        notification.success({
          message: 'Success',
          description: 'PLI Score frozen successfully',
        });
      } else {
        throw new Error(
          response && response.message
            ? response.message
            : 'Failed to freeze PLI score',
        );
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.message || 'Failed to freeze PLI score',
      });
    } finally {
      setFreezing(false);
    }
  };

  return (
    <Container>
      <Row align="middle">
        <Col span={6}>
          <Label>Calculated PLI Score</Label>
        </Col>
        <Col span={4}>
          <Value>{calculatedPLIScore || 'N/A'}</Value>
        </Col>
        <Col span={5}>
          <Button
            type="primary"
            style={{ marginLeft: '16px' }}
            onClick={handleFreezePLI}
            disabled={freezing}
            data-testid="freeze-pli-button"
          >
            {freezing ? <Spin size="small" /> : 'Freeze PLI Score'}
          </Button>
        </Col>
      </Row>

      <ButtonContainer>
        <StyledButton type="default">Cancel</StyledButton>
        <StyledButton type="primary">Save as Draft</StyledButton>
      </ButtonContainer>
    </Container>
  );
};

FreezePLI.propTypes = {
  ids: PropTypes.shape({
    menteeId: PropTypes.string,
    mentorId: PropTypes.string,
    parameterIds: PropTypes.object,
  }),
  menteeId: PropTypes.string,
  mentorId: PropTypes.string,
  calculatedPLIScore: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

FreezePLI.defaultProps = {
  ids: null,
  menteeId: null,
  mentorId: null,
  calculatedPLIScore: null,
};

export default FreezePLI;
