import React, { useContext, useState } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, notification } from 'antd';
import {
  Container,
  Label,
  Value,
  ButtonContainer,
  StyledButton,
} from './Styles/StyledSubmit';
import request from '../../utils/request';
import { API_ENDPOINTS } from '../../containers/EmployeeProfile/constants';
import { EmployeeProfileContext } from '../../containers/EmployeeProfile/context';

const Submit = ({ ids, calculatedPLIScore }) => {
  const { pliDataMap, profile, selectedMonth } =
    useContext(EmployeeProfileContext) || {};
  const [submitting, setSubmitting] = useState(false);

  const formatDataForSubmission = () => {
    if (!pliDataMap || !profile) {
      console.log('Missing data:', { pliDataMap, profile });
      return null;
    }

    // Convert month name to number
    const months = {
      january: 1,
      february: 2,
      march: 3,
      april: 4,
      may: 5,
      june: 6,
      july: 7,
      august: 8,
      september: 9,
      october: 10,
      november: 11,
      december: 12,
    };
    const month =
      selectedMonth && months[selectedMonth.toLowerCase()]
        ? months[selectedMonth.toLowerCase()]
        : new Date().getMonth() + 1;
    const year = new Date().getFullYear();

    // Format the data for submission
    const projectRatings = [];

    Object.keys(pliDataMap).forEach(projectKey => {
      const [projectId, projectType] = projectKey.split('_');
      const projectData = pliDataMap[projectKey];

      console.log(`Processing project: ${projectKey}`, {
        projectId,
        projectType,
        projectData: JSON.stringify(projectData, null, 2),
      });

      // Create parameter scores array
      const parameterScores = [];

      // Group parameters by their parent parameter
      const allParams = new Set();
      const allSprints = new Set();

      // Collect all parameters and sprints
      Object.keys(projectData).forEach(sprintName => {
        if (
          sprintName !== 'parameterId' &&
          sprintName !== 'comments' &&
          sprintName !== 'projectWeightage'
        ) {
          allSprints.add(sprintName);
          Object.keys(projectData[sprintName]).forEach(paramName => {
            allParams.add(paramName);
          });
        }
      });

      const parameterId = ids.parameterIds[projectKey];

      // Create a parameter score object
      const parameterScore = {
        parameterId,
        parentParameter: 'Project',
        projectType,
        comments: projectData.comments || '',
        childScores: [],
      };
      // };

      // Add child scores for each parameter
      allParams.forEach(paramName => {
        const childScore = {
          childParameterId: paramName,
          sprintScores: [],
        };

        // Add sprint scores for this parameter
        allSprints.forEach(sprintName => {
          if (projectData[sprintName] && projectData[sprintName][paramName]) {
            const sprintScore = {
              sprintNumber: sprintName,
              score: projectData[sprintName][paramName].value,
              comment: '',
            };
            childScore.sprintScores.push(sprintScore);
          }
        });

        parameterScore.childScores.push(childScore);
      });

      parameterScores.push(parameterScore);

      // Add project rating
      const projectRating = {
        projectId,
        projectWeightage: parseInt(projectData.projectWeightage || 100, 10),
        parameterScores,
      };

      projectRatings.push(projectRating);
    });

    const formattedData = {
      menteeId: ids.menteeId,
      mentorId: ids.mentorId,
      month,
      year,
      projectRatings,
    };

    return formattedData;
  };

  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      const formattedData = formatDataForSubmission();

      if (!formattedData) {
        notification.error({
          message: 'Error',
          description: 'No data available to submit',
        });
        setSubmitting(false);
        return;
      }

      const response = await request(API_ENDPOINTS.UPDATE_SPRINT_SCORES, {
        method: 'POST',
        body: JSON.stringify(formattedData),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response && response.status === 1) {
        notification.success({
          message: 'Success',
          description: 'Sprint scores submitted successfully',
        });
      } else {
        throw new Error(
          response && response.message
            ? response.message
            : 'Failed to submit sprint scores',
        );
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error.message || 'Failed to submit sprint scores',
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Container>
      <Row gutter={[16, 16]}>
        <Col span={24}>
          <Label>Calculated PLI Score</Label>
          <Value>{calculatedPLIScore || 'N/A'}</Value>
        </Col>

        <Col span={24}>
          <ButtonContainer>
            <StyledButton
              type="primary"
              loading={submitting}
              onClick={handleSubmit}
            >
              Submit
            </StyledButton>
          </ButtonContainer>
        </Col>
      </Row>
    </Container>
  );
};

Submit.propTypes = {
  ids: PropTypes.shape({
    menteeId: PropTypes.string,
    mentorId: PropTypes.string,
    parameterIds: PropTypes.object,
  }),
  calculatedPLIScore: PropTypes.number,
};

export default Submit;
