import React from 'react';
import PropTypes from 'prop-types';
import { Row, Col } from 'antd';
import {
  Container,
  Label,
  Value,
  ButtonContainer,
  StyledButton,
} from './Styles/StyledRaiseQuery';

const RaiseQuery = ({ calculatedPLIScore }) => (
  <Container>
    <Row align="middle">
      <Col span={6}>
        <Label>Calculated PLI Score</Label>
      </Col>
      <Col span={4}>
        <Value>{calculatedPLIScore || 'N/A'}</Value>
      </Col>
    </Row>

    <ButtonContainer>
      <StyledButton type="default">Raise a Query</StyledButton>
      <StyledButton type="primary">Accept PLI</StyledButton>
    </ButtonContainer>
  </Container>
);

RaiseQuery.propTypes = {
  calculatedPLIScore: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
};

RaiseQuery.defaultProps = {
  calculatedPLIScore: null,
};

export default RaiseQuery;
