import styled from 'styled-components';
import { Card, Row, Button } from 'antd';

export const DetailCard = styled(Card)`
  .ant-card-body {
    padding: 24px;
    overflow-x: hidden;
  }
  margin-bottom: 24px;
  width: 100%;
`;

export const Label = styled.div`
  font-weight: 500;
  color: #666;
  padding: 12px;
  min-height: 40px;
  display: flex;
  align-items: center;
  word-break: break-word;
`;

export const Value = styled.div`
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  min-height: 40px;
  display: flex;
  align-items: center;
  word-break: break-word;
`;

export const DetailRow = styled(Row)`
  margin-bottom: 8px;
  margin-right: 0 !important;
  margin-left: 0 !important;
  &:last-child {
    margin-bottom: 0;
  }
  width: 100%;
`;

export const HeaderCell = styled.div`
  font-weight: 600;
  padding: 12px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  text-align: center;
  word-break: break-word;
`;

export const SaveButton = styled(Button)`
  margin-top: 16px;
  float: right;
`;

export const ContentWrapper = styled.div`
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
`;
