import React from 'react';
import { render } from 'react-testing-library';
import StatusCard from '../index';

describe('<StatusCard />', () => {
  const mockCardData = {
    countInformation: {
      totalEpicCount: 5,
      totalStoryCount: 10,
      storiesWithoutEpics: 2,
      epicWithoutSE: 3,
      bugsWithoutLinkedStories: 4,
      storyWithOriginalEstimateGreaterThan40: 1,
      subtasksCount: 6,
      storiesWithExtremeSubtaskCounts: 2,
      jiraStoryUrl: 'http://jira.example.com',
      totalSalesEstimate: 100,
      totalSpent: 80,
      totalDifference: 20,
      totalDevSpent: 60,
      totalDevToBeSpent: 20,
    },
    epicEffortsInformation: [
      {
        status: 'Done',
        totalSalesEstimates: 50,
        totalLoggedEfforts: 40,
        toBeLoggedEfforts: 10,
        difference: 0,
      },
    ],
  };

  it('should render loading state correctly', () => {
    const { container } = render(<StatusCard loading cardData={{}} />);
    expect(container.querySelector('.skeleton')).toBeTruthy();
  });

  it('should render right cards with correct data and links', () => {
    const { getByText, getAllByRole } = render(
      <StatusCard
        cardData={mockCardData}
        loading={false}
        selectedProjectName="TestProject"
      />,
    );

    // Verify card labels are rendered
    expect(getByText('Epic Count')).toBeTruthy();
    expect(getByText('Story Count')).toBeTruthy();
    expect(getByText('Stories Without Epic')).toBeTruthy();
    expect(getByText('Epic Without Sales Estimate')).toBeTruthy();
    expect(getByText('Bugs Without Linked Stories')).toBeTruthy();
    expect(getByText('Stories Estimated Over 40 Hours')).toBeTruthy();
    expect(
      getByText('Subtasks with Original Estimates Over 8 Hours'),
    ).toBeTruthy();
    expect(getByText('Excessive/Insufficient Subtasks')).toBeTruthy();

    // Verify links are present and have correct URLs
    const links = getAllByRole('link');
    expect(links).toHaveLength(7); // All cards except 'Excessive/Insufficient Subtasks' have links

    // Verify each link contains the correct project name and Jira URL
    links.forEach(link => {
      expect(link.href).toContain('project = "TestProject"');
      expect(link.href).toContain('jira.example.com');
    });
  });

  it('should not render cards with zero values', () => {
    const zeroValueCardData = {
      countInformation: {
        totalEpicCount: 0,
        totalStoryCount: 0,
        storiesWithoutEpics: 0,
        epicWithoutSE: 0,
        bugsWithoutLinkedStories: 0,
        storyWithOriginalEstimateGreaterThan40: 0,
        subtasksCount: 0,
        storiesWithExtremeSubtaskCounts: 0,
        jiraStoryUrl: 'http://jira.example.com',
      },
    };

    const { queryByText } = render(
      <StatusCard
        cardData={zeroValueCardData}
        loading={false}
        selectedProjectName="TestProject"
      />,
    );

    // Verify no cards are rendered when values are 0
    expect(queryByText('Epic Count')).toBeNull();
    expect(queryByText('Story Count')).toBeNull();
    expect(queryByText('Stories Without Epic')).toBeNull();
    expect(queryByText('Epic Without Sales Estimate')).toBeNull();
    expect(queryByText('Bugs Without Linked Stories')).toBeNull();
    expect(queryByText('Stories Estimated Over 40 Hours')).toBeNull();
    expect(
      queryByText('Subtasks with Original Estimates Over 8 Hours'),
    ).toBeNull();
    expect(queryByText('Excessive/Insufficient Subtasks')).toBeNull();
  });

  it('should render RAG colors correctly for total difference', () => {
    const testCases = [
      { difference: 20, expectedClass: 'green-deviation-bg' },
      { difference: -5, expectedClass: 'amber-deviation-bg' },
      { difference: -15, expectedClass: 'red-deviation-bg' },
    ];

    testCases.forEach(({ difference, expectedClass }) => {
      const testData = {
        ...mockCardData,
        countInformation: {
          ...mockCardData.countInformation,
          totalDifference: difference,
          totalSalesEstimate: 100, // This sets the threshold to 10
        },
      };

      const { container } = render(
        <StatusCard
          cardData={testData}
          loading={false}
          selectedProjectName="TestProject"
        />,
      );

      expect(container.querySelector(`.${expectedClass}`)).toBeTruthy();
    });
  });
});
