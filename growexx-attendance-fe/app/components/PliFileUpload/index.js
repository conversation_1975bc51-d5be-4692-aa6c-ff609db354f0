import React, { useState } from 'react';
import { Upload, Button, notification } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import request from 'utils/request';
import { API_ENDPOINTS } from 'utils/endpoints';

const ACCEPTED_FILE_TYPES = [
  '.jpg',
  '.jpeg',
  '.png',
  '.pdf',
  '.doc',
  '.docx',
  '.xls',
  '.xlsx',
  '.txt',
];

const PliFileUpload = ({ employeeId, onFileUploaded }) => {
  const [fileUploading, setFileUploading] = useState(false);

  const handleUpload = async options => {
    const { file } = options;
    setFileUploading(true);
    const formData = new FormData();
    formData.append('file', file);

    const payload = {
      method: 'POST',
      body: formData,
    };

    try {
      const response = await request(
        `${API_ENDPOINTS.PLI}/pli/upload/${employeeId}`,
        payload,
      );

      setFileUploading(false);

      if (response.success) {
        notification.success({
          message: 'Success',
          description: 'File uploaded successfully',
        });
        if (onFileUploaded) {
          onFileUploaded(response.file);
        }
      } else {
        notification.error({
          message: 'Upload Failed',
          description: response.message || 'Failed to upload file',
        });
      }
    } catch (error) {
      setFileUploading(false);
      notification.error({
        message: 'Upload Failed',
        description: error.message || 'Failed to upload file',
      });
    }
  };

  return (
    <Upload
      name="file"
      customRequest={handleUpload}
      accept={ACCEPTED_FILE_TYPES.join(',')}
      maxCount={1}
      disabled={fileUploading}
      showUploadList={false}
    >
      <Button type="primary" icon={<UploadOutlined />} loading={fileUploading}>
        Upload Document
      </Button>
    </Upload>
  );
};

PliFileUpload.propTypes = {
  employeeId: PropTypes.string.isRequired,
  onFileUploaded: PropTypes.func,
};

export default PliFileUpload;
