import styled from 'styled-components';
import { Card, Row, Input, Button } from 'antd';
import { EditOutlined } from '@ant-design/icons';

export const DetailCard = styled(Card)`
  .ant-card-body {
    padding: 24px;
  }
`;

export const Label = styled.div`
  font-weight: 500;
  color: #666;
  padding: 12px;
  min-height: 40px;
  display: flex;
  align-items: center;
`;

export const Value = styled.div`
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  min-height: 40px;
  display: flex;
  align-items: center;
`;

export const DetailRow = styled(Row)`
  margin-bottom: 8px;
  &:last-child {
    margin-bottom: 0;
  }
`;

export const HeaderCell = styled.div`
  font-weight: 600;
  padding: 12px;
  background: #fafafa;
  border: 1px solid #e8e8e8;
  text-align: center;
`;

export const TextAreaContainer = styled.div`
  margin-top: 24px;
  width: 100%;
  min-height: 160px;
  position: relative;
`;

export const StyledTextArea = styled(Input.TextArea)`
  width: 100%;
  min-height: 120px;
  margin-top: 8px;
  padding-right: 40px;
`;

export const EditIcon = styled(EditOutlined)`
  position: absolute;
  right: 12px;
  top: 20px;
  font-size: 16px;
  cursor: pointer;
  color: #1890ff;
`;

export const SaveButton = styled(Button)`
  margin-top: 16px;
  float: right;
`;
