/* eslint-disable indent */
/* eslint-disable react/no-array-index-key */
/**
 * SideBar/index.js
 *
 * This is the SideBar Component File.
 */
import React from 'react';
import { Layout, Menu } from 'antd';
import { <PERSON>, withRouter } from 'react-router-dom';
import PropTypes from 'prop-types';
import {
  KeyOutlined,
  TeamOutlined,
  CarryOutOutlined,
  Bar<PERSON><PERSON>Outlined,
} from '@ant-design/icons';
import GrowExxTriangleLogo from '../../images/Growexx-Triangle-White.png';
import GrowExxLogo from '../../images/GrowExx_Group_Logo.png';
import { GET_FILTERED_MENU_ITEM } from './Constants';
import { ROLES, ROUTES, SUPER_ADMIN_LIST } from '../../containers/constants';
import { getCurrentQuarterYear } from '../MyKRA/constants';

const { SubMenu } = Menu;
const { Sider } = Layout;

export const isSuperAdmin = user => {
  if (!user) return false;
  if (!user.email) return false;
  return SUPER_ADMIN_LIST.includes(user.email);
};
const SideBar = props => {
  const { quarter, year } = getCurrentQuarterYear();
  const checkIfUserIsManager = () => {
    const isManager =
      props.user.isReportingManager || props.user.isReviewManager;
    const isAdminOrBU =
      props.user.role !== ROLES.HR && props.user.role !== ROLES.BU;
    if (props.user && (isManager && isAdminOrBU)) {
      return true;
    }
    return false;
  };

  const showReviewLogsMenu = () => {
    if (
      props.user.role === ROLES.HR ||
      props.user.isReportingManager ||
      props.user.isReviewManager
    ) {
      return true;
    }
    return false;
  };

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={props.collapsed}
      id="components-layout-demo-custom-trigger"
    >
      <div className="logo">
        {!props.collapsed ? (
          <img src={GrowExxLogo} alt="logo" />
        ) : (
          <img src={GrowExxTriangleLogo} alt="logo" />
        )}
      </div>
      <Menu
        theme="dark"
        mode="inline"
        defaultSelectedKeys={props.location.pathname}
        selectedKeys={[props.location.pathname]}
      >
        {checkIfUserIsManager() && (
          <Menu.Item key={ROUTES.USERS} icon={<TeamOutlined />}>
            <Link to={ROUTES.USERS}>My Team</Link>
          </Menu.Item>
        )}
        {GET_FILTERED_MENU_ITEM(props.user && props.user.role).map(menu => (
          <Menu.Item key={menu.to} icon={menu.icon}>
            <Link to={menu.to}>{menu.tabName}</Link>
          </Menu.Item>
        ))}
        {showReviewLogsMenu() && (
          <Menu.Item key={ROUTES.REVIEW_LOGS} icon={<CarryOutOutlined />}>
            <Link to={ROUTES.REVIEW_LOGS}>Review Logs</Link>
          </Menu.Item>
        )}
        <SubMenu
          key="pli"
          title={
            <span>
              <BarChartOutlined />
              <span>PLI</span>
            </span>
          }
        >
          {isSuperAdmin(props.user) ? (
            <Menu.Item key={ROUTES.PLI_RATINGS}>
              <Link to={ROUTES.PLI_RATINGS}>PLI Overview</Link>
            </Menu.Item>
          ) : (
            <Menu.Item key={ROUTES.PLI}>
              <Link to={ROUTES.PLI}>PLI Dashboard</Link>
            </Menu.Item>
          )}

          {/* <Menu.Item key={ROUTES.PLI}>
            <Link to={ROUTES.PLI}>PLI Dashboard</Link>
          </Menu.Item> */}
          <Menu.Item key={ROUTES.PARAMETER_CONFIGURATION}>
            <Link to={ROUTES.PARAMETER_CONFIGURATION}>
              Parameter Configuration
            </Link>
          </Menu.Item>
        </SubMenu>
        {props.user && props.user.role === ROLES.HR && (
          <SubMenu
            key="kra"
            title={
              <span>
                <KeyOutlined />
                <span>KRA</span>
              </span>
            }
          >
            <Menu.Item key={ROUTES.CATEGORY_WEIGHTAGE_MASTER}>
              <Link
                to={`${
                  ROUTES.CATEGORY_WEIGHTAGE_MASTER
                }?quarter=${quarter}&year=${year}`}
              >
                Category Weightage Master
              </Link>
            </Menu.Item>
            <SubMenu
              key="kra-assignment"
              title={
                <span>
                  <span>Assignment</span>
                </span>
              }
            >
              {props.user &&
                (props.user.role === ROLES.HR ||
                  props.user.role === ROLES.BU) && (
                  <Menu.Item key={ROUTES.ASSIGN_BULK_KRA}>
                    <Link
                      to={`${
                        ROUTES.ASSIGN_BULK_KRA
                      }?quarter=${quarter}&year=${year}`}
                    >
                      Bulk KRA Assignment
                    </Link>
                  </Menu.Item>
                )}
              {props.user &&
                (props.user.role !== ROLES.HR &&
                  props.user.role !== ROLES.BU) && (
                  <Menu.Item key={ROUTES.SELF_KRA}>
                    <Link
                      to={`${ROUTES.SELF_KRA}?quarter=${quarter}&year=${year}`}
                    >
                      Self KRA
                    </Link>
                  </Menu.Item>
                )}
              <Menu.Item key={ROUTES.ASSIGNED_KRA}>
                <Link
                  to={`${ROUTES.ASSIGNED_KRA}?quarter=${quarter}&year=${year}`}
                >
                  Assigned KRA
                </Link>
              </Menu.Item>
              <Menu.Item key={ROUTES.STOCK_KRA}>
                <Link
                  to={`${ROUTES.STOCK_KRA}?quarter=${quarter}&year=${year}`}
                >
                  Stock KRA
                </Link>
              </Menu.Item>
            </SubMenu>
            {props.user && props.user.role !== ROLES.HR && (
              <SubMenu
                key="kra-assessment"
                title={
                  <span>
                    <span>Assessment</span>
                  </span>
                }
              >
                <Menu.Item key={ROUTES.SELF_ASSESSMENT}>
                  <Link
                    to={`${
                      ROUTES.SELF_ASSESSMENT
                    }?quarter=${quarter}&year=${year}`}
                  >
                    Self Comment
                  </Link>
                </Menu.Item>
                {props.user && !!props.user.isReportingManager && (
                  <Menu.Item key={ROUTES.PM_ASSESSMENT}>
                    <Link
                      to={`${
                        ROUTES.PM_ASSESSMENT
                      }?quarter=${quarter}&year=${year}`}
                    >
                      Reporting Manager
                    </Link>
                  </Menu.Item>
                )}
                {props.user && !!props.user.isReviewManager && (
                  <Menu.Item key={ROUTES.RM_ASSESSMENT}>
                    <Link
                      to={`${
                        ROUTES.RM_ASSESSMENT
                      }?quarter=${quarter}&year=${year}`}
                    >
                      Review Manager
                    </Link>
                  </Menu.Item>
                )}
              </SubMenu>
            )}
          </SubMenu>
        )}
      </Menu>
    </Sider>
  );
};

export default withRouter(SideBar);

SideBar.propTypes = {
  collapsed: PropTypes.bool,
  user: PropTypes.any,
  location: PropTypes.object,
};
