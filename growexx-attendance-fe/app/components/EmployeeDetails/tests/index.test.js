import React from 'react';
import { render } from 'react-testing-library';
import EmployeeDetails from '../index';

describe('EmployeeDetails', () => {
  const profile = {
    name: '<PERSON>',
    employeeId: '123',
    department: 'Engineering',
    reportingManager: '<PERSON>',
    managerId: '456',
    doj: '01-Jan-2020',
    pliDuration: '1 Month',
  };

  // Default props required by the component
  const defaultProps = {
    profile,
    selectedMonth: '',
    projectRows: [],
    loading: false,
    showPLIDetails: false,
    tempProjects: [],
    handleMonthChange: jest.fn(),
    handleProjectChange: jest.fn(),
    handleWeightageChange: jest.fn(),
    handleAddRow: jest.fn(),
    handleRemoveRow: jest.fn(),
    handleSync: jest.fn(),
    handleProjectTypeChange: jest.fn(),
  };

  it('renders all fields with valid data', () => {
    const { getByText } = render(<EmployeeDetails {...defaultProps} />);
    expect(getByText('<PERSON>')).toBeTruthy();
    expect(getByText('123')).toBeTruthy();
    expect(getByText('Engineering')).toBeTruthy();
    expect(getByText('Jane Smith')).toBeTruthy();
    expect(getByText('456')).toBeTruthy();
    expect(getByText('01-Jan-2020')).toBeTruthy();
    expect(getByText('1 Month')).toBeTruthy();
  });

  it('renders "N/A" for missing fields', () => {
    const { getAllByText } = render(
      <EmployeeDetails {...defaultProps} profile={{}} />,
    );
    // There are 7 fields, so 7 N/A
    expect(getAllByText('N/A').length).toBeGreaterThanOrEqual(7);
  });

  it('renders Employee ID and Manager ID as strings', () => {
    const { getByText } = render(
      <EmployeeDetails
        {...defaultProps}
        profile={{ ...profile, employeeId: '789', managerId: '101' }}
      />,
    );
    expect(getByText('789')).toBeTruthy();
    expect(getByText('101')).toBeTruthy();
  });

  it('renders the month dropdown', () => {
    const { container } = render(<EmployeeDetails {...defaultProps} />);
    const dropdown = container.querySelector('.ant-select');
    expect(dropdown).toBeTruthy();
  });
});
