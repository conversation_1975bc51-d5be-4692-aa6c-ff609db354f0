import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Row, Col, Spin } from 'antd';
import { SyncOutlined, MinusOutlined } from '@ant-design/icons';
import PLIDetails from '../PLIDetails';
import {
  DetailCard,
  Label,
  Value,
  DetailRow,
  RightHalf,
  MonthDropdown,
  ProjectRow,
  ProjectDropdown,
  WeightageInput,
  RemoveButton,
  SyncButton,
  PLIContainer,
  MainContainer,
} from './StyledEmployeeDetails';

const EmployeeDetails = ({
  profile,
  selectedMonth,
  projectRows,
  loading,
  showPLIDetails,
  tempProjects,
  handleMonthChange,
  handleProjectChange,
  handleWeightageChange,
  handleRemoveRow,
  handleSync,
  handleProjectTypeChange,
  isMenteeView,
  isEditable = true, // Default to editable if not specified
}) => {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  // States to track loading
  const [pliDetailsLoaded, setPliDetailsLoaded] = useState(false);

  useEffect(() => {
    // Wait for PLIDetails to be loaded before rendering Submit and RaiseQuery
    if (showPLIDetails && projectRows.every(row => row.project)) {
      setPliDetailsLoaded(true);
    }
  }, [showPLIDetails, projectRows]);

  return (
    <MainContainer>
      <Row>
        <Col span={12}>
          <DetailCard title="Employee Details">
            {[
              ['Name', profile.name],
              ['Employee ID', profile.employeeId],
              ['Department', profile.department],
              ['Reporting Manager', profile.reportingManager],
              ['Manager ID', profile.managerId],
              ['Date of Joining', profile.doj],
              ['PLI Duration', profile.pliDuration],
            ].map(([label, value]) => (
              <DetailRow key={label}>
                <Col span={8}>
                  <Label>{label}</Label>
                </Col>
                <Col span={16}>
                  <Value>{value || 'N/A'}</Value>
                </Col>
              </DetailRow>
            ))}
          </DetailCard>
        </Col>

        <Col span={12}>
          <RightHalf>
            <MonthDropdown
              placeholder="Select Month"
              value={selectedMonth}
              onChange={handleMonthChange}
              options={months.map(month => ({ label: month, value: month }))}
              disabled={!isEditable || isMenteeView}
            />

            {projectRows && projectRows.length > 0 && (
              <div style={{ marginTop: '24px' }}>
                <Row gutter={16}>
                  <Col span={9}>
                    <Label>Project</Label>
                  </Col>
                  <Col span={7}>
                    <Label>Project Type</Label>
                  </Col>
                  <Col span={6}>
                    <Label>Weightage (%)</Label>
                  </Col>
                  <Col span={2} />
                </Row>

                {projectRows.map(row => (
                  <ProjectRow key={row.id} gutter={16}>
                    <Col span={9}>
                      {row.isAdditional || !row.project ? (
                        <ProjectDropdown
                          placeholder="Select Project"
                          value={row.project}
                          onChange={(value, option) =>
                            handleProjectChange(value, row.id, option)
                          }
                          disabled={
                            !selectedMonth || !isEditable || isMenteeView
                          }
                          options={tempProjects.filter(
                            project =>
                              !projectRows.some(
                                r =>
                                  r.project === project.value &&
                                  r.id !== row.id,
                              ),
                          )}
                          data-row-id={row.id}
                        />
                      ) : (
                        <div
                          style={{
                            padding: '5px 11px',
                            border: '1px solid #d9d9d9',
                            borderRadius: '2px',
                            height: '32px',
                          }}
                        >
                          {row.projectName || ''}
                        </div>
                      )}
                    </Col>
                    <Col span={7}>
                      {row.isAdditional || !row.project ? (
                        <ProjectDropdown
                          placeholder="Select Type"
                          value={row.projectType}
                          onChange={value =>
                            handleProjectTypeChange(value, row.id)
                          }
                          disabled={
                            !selectedMonth || !isEditable || isMenteeView
                          }
                          options={[
                            { label: 'Fixed', value: 'Fixed' },
                            { label: 'Dedicated', value: 'Dedicated' },
                          ]}
                        />
                      ) : (
                        <div
                          style={{
                            padding: '5px 11px',
                            border: '1px solid #d9d9d9',
                            borderRadius: '2px',
                            height: '32px',
                          }}
                        >
                          {row.projectType || 'Fixed'}
                        </div>
                      )}
                    </Col>
                    <Col span={6}>
                      <WeightageInput
                        placeholder="Weightage"
                        min={0}
                        max={100}
                        value={row.weightage}
                        disabled={!selectedMonth || !isEditable || isMenteeView}
                        onChange={e =>
                          handleWeightageChange(e.target.value, row.id)
                        }
                      />
                    </Col>
                    <Col span={2}>
                      <div
                        style={{
                          display: 'flex',
                          gap: '8px',
                          justifyContent: 'flex-start',
                        }}
                      >
                        {/* Plus button is now hidden as per requirement */}
                        {projectRows.length > 1 && row.isAdditional && (
                          <RemoveButton
                            disabled={
                              !selectedMonth || !isEditable || isMenteeView
                            }
                            type="primary"
                            icon={<MinusOutlined />}
                            onClick={() => handleRemoveRow(row.id)}
                          />
                        )}
                      </div>
                    </Col>
                  </ProjectRow>
                ))}
              </div>
            )}
          </RightHalf>
        </Col>
      </Row>

      {isEditable && (
        <SyncButton
          type="primary"
          icon={<SyncOutlined />}
          onClick={handleSync}
          disabled={
            isMenteeView ||
            !selectedMonth ||
            !projectRows.some(row => row.project)
          }
        >
          Sync
        </SyncButton>
      )}

      <Spin spinning={loading}>
        {/* Render only when PLIDetails are ready */}
        {pliDetailsLoaded && showPLIDetails && (
          <>
            <PLIContainer>
              {projectRows
                .filter(row => row.project)
                .map((row, index) => {
                  const selectedProject = tempProjects.find(
                    p => p.value === row.project,
                  );
                  return (
                    <div
                      key={row.id}
                      style={{
                        marginBottom:
                          index !== projectRows.length - 1 ? '24px' : 0,
                      }}
                    >
                      <PLIDetails
                        profile={{
                          ...profile,
                          projectName:
                            (selectedProject && selectedProject.label) || '',
                          projectType:
                            (selectedProject && selectedProject.type) || '',
                        }}
                        onDataLoaded={() => {}}
                        isEditable={isEditable}
                      />
                    </div>
                  );
                })}
            </PLIContainer>
          </>
        )}
      </Spin>
    </MainContainer>
  );
};

EmployeeDetails.propTypes = {
  profile: PropTypes.shape({
    name: PropTypes.string,
    employeeId: PropTypes.string,
    department: PropTypes.string,
    reportingManager: PropTypes.string,
    managerId: PropTypes.string,
    doj: PropTypes.string,
    pliDuration: PropTypes.string,
  }),
  selectedMonth: PropTypes.string,
  isMenteeView: PropTypes.bool,
  projectRows: PropTypes.array,
  loading: PropTypes.bool,
  showPLIDetails: PropTypes.bool,
  tempProjects: PropTypes.array,
  handleMonthChange: PropTypes.func,
  handleProjectChange: PropTypes.func,
  handleProjectTypeChange: PropTypes.func,
  handleWeightageChange: PropTypes.func,
  handleRemoveRow: PropTypes.func,
  handleSync: PropTypes.func,
  isEditable: PropTypes.bool,
};

export default EmployeeDetails;
