import styled from 'styled-components';
import { Card, Row, Select, Input, Button } from 'antd';

export const DetailCard = styled(Card)`
  .ant-card-body {
    padding: 24px;
  }
`;

export const Label = styled.div`
  font-weight: 500;
  color: #666;
  padding: 12px;
  min-height: 40px;
  display: flex;
  align-items: center;
`;

export const Value = styled.div`
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  min-height: 40px;
  display: flex;
  align-items: center;
`;

export const DetailRow = styled(Row)`
  margin-bottom: 8px;
  &:last-child {
    margin-bottom: 0;
  }
`;

export const RightHalf = styled.div`
  padding: 24px;
  min-height: 490px;
  backgrournd-color: #000000;
`;

export const MonthDropdown = styled(Select)`
  width: 200px;
`;

export const ProjectRow = styled(Row)`
  margin-bottom: 16px;
  align-items: center;
`;

export const ProjectDropdown = styled(Select)`
  width: 100%;
`;

export const WeightageInput = styled(Input)`
  width: 50%;
`;

export const SyncButton = styled(Button)`
  margin-top: 16px;
  margin-left: 100%;
  right: 290px;
  min-width: 195px;
  padding: 0 20px;
  height: 40px;
  font-size: 16px;
`;

export const PLIContainer = styled.div`
  margin-top: 24px;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
`;

export const ReadOnlyValue = styled.div`
  padding: 4px 11px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  min-height: 32px;
  display: flex;
  align-items: center;
`;

export const MainContainer = styled.div`
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  padding: 24px;
`;

export const AddButton = styled(Button)`
  min-width: 30px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
`;

export const RemoveButton = styled(Button)`
  min-width: 30px;
  height: 40px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 4px;
`;
