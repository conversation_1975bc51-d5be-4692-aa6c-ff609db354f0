/* eslint-disable react/no-array-index-key */
/**
 *
 * App
 *
 * This component is the skeleton around the actual pages, and should only
 * contain code that should be seen on all pages. (e.g. navigation bar)
 */

import React from 'react';
import { Helmet } from 'react-helmet';
import styled from 'styled-components';
import { Switch, Route } from 'react-router-dom';
import { useInjectSaga } from 'utils/injectSaga';
import ConnectedJiraList from 'containers/ConnectedJiraList/Loadable';
import NotFoundPage from 'containers/NotFoundPage/Loadable';
import UnauthorizedPage from 'containers/UnauthorizedPage/Loadable';
import UserLogs from 'containers/UserLogs/Loadable';
import UserListing from 'containers/UserListing/Loadable';
import ProjectListing from 'containers/ProjectListing/Loadable';
import Login from 'containers/Auth/Login/Loadable';
import ForgotPassword from 'containers/Auth/ForgotPassword/Loadable';
import ResetPassword from 'containers/Auth/ResetPassword/Loadable';
import Logout from 'containers/Auth/Logout/Loadable';
import ChangePassword from 'containers/ChangePassword/Loadable';
import AssignKRA from 'containers/AssignKRA/Loadable';
import AssignBulkKRA from 'containers/AssignBulkKRA/Loadable';
import StockKRA from 'containers/StockKRA';
import AssignKRADetail from 'containers/AssignKRADetail/Loadable';
import IndividualAssessment from 'containers/IndividualAssessment';
import ViewIndividualAssessment from 'containers/ViewIndividualAssessment';
import PmAssessment from 'containers/PmAssessment';
import RmAssessment from 'containers/RmAssessment';
import RateSelfAssessment from 'containers/RateSelfAssessment';
import ViewSelfAssessment from 'containers/ViewSelfAssessment';
import SelfKRA from 'containers/SelfKRA';
import SelfComment from 'containers/SelfComment';
import CategoryWeightageMaster from 'containers/CategoryWeightageMaster/Loadable';
import ProjectTracker from 'containers/ProjectTracker/Loadable';
import EmployeeProfile from 'containers/EmployeeProfile';
import employeeProfileSaga from 'containers/EmployeeProfile/saga';
import ParameterConfiguration from 'containers/ParameterConfiguration/Loadable';
import PLIDetails from 'components/PLIDetails';
import { FAV_ICONS } from './constants';
import PrivateRoute from './PrivateRoute';
import DefaultRoute from './DefaultRoute';
import RoleMiddleWare from './RoleMiddleWare';
import AuthRoute from './AuthRoute';
import GlobalStyle from '../../global-styles';
import { PRODUCT_NAME, ROUTES } from '../constants';
import AssignedKRA from '../AssignedKRA/Loadable';
import ViewRmAssessment from '../ViewRmAssessment';
import StartAssessment from '../StartAssessment';
import { RagReport } from '../RagReport';
import { ReviewLogs } from '../ReviewLogs';
import BulkAction from '../BulkAction';
import MenteeListing from '../MenteeListing/index';
import PLIRating from '../PLIRatingOverview/Loadable';
import PLIRedirect from '../PLISection/PLIRedirect';
import TechRoadmap from '../TechRoadmap/Loadable';
import MenteeTechRoadmap from '../MenteeTechRoadmap/Loadable';
import CourseAssignment from '../CourseAssignment/Loadable';

const AppWrapper = styled.div`
  display: flex;
  min-height: calc(100vh - 220px);
  flex-direction: column;
`;

export default function App() {
  useInjectSaga({ key: 'employeeProfile', saga: employeeProfileSaga });

  return (
    <AppWrapper data-testid="AppRoutes">
      <Helmet
        titleTemplate={`%s - ${PRODUCT_NAME} Timesheet System`}
        defaultTitle={`${PRODUCT_NAME} Timesheet System`}
      >
        <meta
          name="description"
          content={`A ${PRODUCT_NAME} Timesheet System `}
        />
        {FAV_ICONS.map((favIcon, index) => (
          <link {...favIcon} key={index} />
        ))}
      </Helmet>
      <Switch>
        <DefaultRoute exact path={ROUTES.HOME} />
        <RoleMiddleWare
          exact
          path={ROUTES.CONNECTED_JIRA}
          component={ConnectedJiraList}
          showError
        />
        <PrivateRoute path={ROUTES.USERS} component={UserListing} />
        <PrivateRoute
          path={ROUTES.PROJECTS}
          component={ProjectListing}
          showError
        />
        <PrivateRoute
          path={ROUTES.ASSIGN_KRA}
          component={AssignKRA}
          showError
        />
        <RoleMiddleWare
          path={ROUTES.START_ASSESSMENT}
          component={StartAssessment}
          showError
        />
        <PrivateRoute
          path={ROUTES.ASSIGNED_KRA}
          component={AssignedKRA}
          showError
        />
        <PrivateRoute
          path={ROUTES.ASSIGN_BULK_KRA}
          component={AssignBulkKRA}
          showError
        />
        <PrivateRoute path={ROUTES.STOCK_KRA} component={StockKRA} showError />
        <PrivateRoute
          path={ROUTES.EDIT_KRA}
          component={AssignKRADetail}
          showError
        />
        <PrivateRoute
          path={ROUTES.ASSIGN_KRA_DETAIL}
          component={AssignKRADetail}
          showError
        />
        <PrivateRoute
          exact
          path={ROUTES.PM_ASSESSMENT}
          component={PmAssessment}
          showError
        />
        <PrivateRoute
          exact
          path={ROUTES.RM_ASSESSMENT}
          component={RmAssessment}
          showError
        />
        <PrivateRoute
          path={ROUTES.VIEW_KRA}
          component={ViewIndividualAssessment}
          showError
        />
        <PrivateRoute
          path={ROUTES.VIEW_KRA_RM}
          component={ViewRmAssessment}
          showError
        />
        <PrivateRoute
          path={ROUTES.RATE_KRA}
          component={IndividualAssessment}
          showError
        />
        <PrivateRoute
          path={ROUTES.SELF_VIEW_KRA}
          component={ViewSelfAssessment}
          showError
        />
        <PrivateRoute
          path={ROUTES.SELF_RATE_KRA}
          component={RateSelfAssessment}
          showError
        />
        <PrivateRoute path={ROUTES.SELF_KRA} component={SelfKRA} showError />
        <PrivateRoute
          path={ROUTES.SELF_ASSESSMENT}
          component={SelfComment}
          showError
        />
        <PrivateRoute
          path={ROUTES.CATEGORY_WEIGHTAGE_MASTER}
          component={CategoryWeightageMaster}
          showError
        />
        <RoleMiddleWare
          exact
          path={ROUTES.RAG_REPORT}
          component={RagReport}
          showError
        />
        <RoleMiddleWare
          exact
          path={ROUTES.PROJECT_TRACKER}
          component={ProjectTracker}
          showError
        />
        <PrivateRoute
          path={ROUTES.CHANGE_PASSWORD}
          component={ChangePassword}
        />
        <PrivateRoute path={ROUTES.LOGS} component={UserLogs} />
        <PrivateRoute path={ROUTES.REVIEW_LOGS} component={ReviewLogs} />
        <PrivateRoute path={ROUTES.LOGOUT} component={Logout} />
        <AuthRoute exact path={ROUTES.LOGIN} component={Login} />
        <Route
          exact
          path="/pli/employee-profile-redirect"
          component={PLIRedirect}
        />
        <Route exact path={ROUTES.MY_PLI_RATING} component={EmployeeProfile} />
        <Route exact path={ROUTES.BULk_ACTION} component={BulkAction} />
        <AuthRoute
          exact
          path={ROUTES.FORGOT_PASSWORD}
          component={ForgotPassword}
        />
        <AuthRoute
          exact
          path={ROUTES.RESET_PASSWORD}
          component={ResetPassword}
        />
        <Route exact path={ROUTES.UNAUTHORIZED} component={UnauthorizedPage} />
        <PrivateRoute exact path={ROUTES.PLI} component={MenteeListing} />
        <PrivateRoute
          path={ROUTES.EMPLOYEE_PROFILE}
          component={EmployeeProfile}
          showError
        />

        {/* Other specific routes */}
        <PrivateRoute
          path={ROUTES.PLI_RATINGS}
          component={PLIRating}
          showError
        />
        <PrivateRoute
          path={ROUTES.PARAMETER_CONFIGURATION}
          component={ParameterConfiguration}
        />
        <RoleMiddleWare
          path={ROUTES.TECH_ROADMAP}
          component={TechRoadmap}
          showError
        />
        <PrivateRoute
          path={ROUTES.COURSE_ASSIGNMENT}
          component={CourseAssignment}
          showError
        />
        <PrivateRoute
          path={ROUTES.MENTEE_TECH_ROADMAP}
          component={MenteeTechRoadmap}
          showError
        />
        <PrivateRoute
          path={ROUTES.PLIDETAILS}
          component={PLIDetails}
          showError
        />
        <Route path="" component={NotFoundPage} />
      </Switch>
      <GlobalStyle />
    </AppWrapper>
  );
}
