/* eslint-disable space-before-function-paren */
import React from 'react';
import { Route, Redirect } from 'react-router';
import PropTypes from 'prop-types';
import { userExists } from 'utils/Helper';
import queryString from 'query-string';
import { ROUTES } from '../constants';

const AuthRoute = mainProps => {
  const { component: Component, ...rest } = mainProps;

  return (
    <Route
      {...rest}
      render={props => {
        // Check if there's a redirect parameter in the URL
        const parsedQuery = queryString.parse(props.location.search);
        const redirectPath = parsedQuery.redirect || ROUTES.LOGS;

        return !userExists() ? (
          <Component {...props} />
        ) : (
          <Redirect
            to={{
              pathname: redirectPath,
            }}
          />
        );
      }}
    />
  );
};

AuthRoute.propTypes = {
  component: PropTypes.func,
  location: PropTypes.object,
};

export default AuthRoute;
