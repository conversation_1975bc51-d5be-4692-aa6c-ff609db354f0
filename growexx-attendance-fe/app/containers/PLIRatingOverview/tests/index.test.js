import React from 'react';
import configureStore from 'redux-mock-store';
import injectReducer from 'utils/injectReducer';
import injectSaga from 'utils/injectSaga';
import { makeSelectMentorMenteeData } from '../selectors';
import { fetchMentorMenteeData } from '../actions';

// Mock the HOCs BEFORE importing the component
jest.mock('utils/injectReducer', () => ({
  __esModule: true,
  default: () => Component => Component,
}));

jest.mock('utils/injectSaga', () => ({
  __esModule: true,
  default: () => Component => Component,
}));

// Mock the selectors
jest.mock('../selectors', () => ({
  makeSelectMentorMenteeData: () => () => [
    {
      id: 1,
      empId: 'EMP001',
      mentor: '<PERSON>',
      department: 'Engineering',
      mentees: [
        {
          id: 1,
          name: '<PERSON>',
          email: '<EMAIL>',
          empId: 'EMP002',
        },
      ],
    },
  ],
  makeSelectMentorMenteeLoading: () => () => false,
  makeSelectProcessingResults: () => () => null,
  makeSelectProcessingError: () => () => null,
  makeSelectResultsModalVisible: () => () => false,
}));

// Mock the actions
jest.mock('../actions', () => ({
  fetchMentorMenteeData: jest.fn(() => ({ type: 'FETCH_MENTOR_MENTEE_DATA' })),
  processBulkAssignments: jest.fn(data => ({
    type: 'PROCESS_BULK_ASSIGNMENTS',
    data,
  })),
  hideResultsModal: jest.fn(() => ({ type: 'HIDE_RESULTS_MODAL' })),
  showModal: jest.fn(record => ({ type: 'SHOW_MODAL', record })),
  hideModal: jest.fn(() => ({ type: 'HIDE_MODAL' })),
  overridePLIRating: jest.fn((menteeId, data) => ({
    type: 'OVERRIDE_PLI_RATING',
    menteeId,
    data,
  })),
  finalizePLIRating: jest.fn(menteeId => ({
    type: 'FINALIZE_PLI_RATING',
    menteeId,
  })),
}));

// Create a mock store
const mockStore = configureStore([]);

describe('PLIRating Component', () => {
  let store;

  beforeEach(() => {
    // Create a mock store with the necessary state
    store = mockStore({
      pliRating: {
        mentorMenteeData: [
          {
            id: 1,
            empId: 'EMP001',
            mentor: 'John Doe',
            department: 'Engineering',
            mentees: [
              {
                id: 1,
                name: 'Alice Smith',
                email: '<EMAIL>',
                empId: 'EMP002',
              },
            ],
          },
        ],
        loading: false,
        error: null,
        processingResults: null,
        resultsModalVisible: false,
      },
    });

    // Mock dispatch method
    store.dispatch = jest.fn();
  });

  // Test 1: Store has the correct initial state
  it('should have the correct initial state in the store', () => {
    const state = store.getState();
    expect(state.pliRating.mentorMenteeData).toBeTruthy();
    expect(state.pliRating.loading).toBe(false);
  });

  // Test 2: Mock selectors return expected values
  it('should have selectors that return expected values', () => {
    const mentorMenteeData = makeSelectMentorMenteeData()();

    expect(mentorMenteeData).toHaveLength(1);
    expect(mentorMenteeData[0].mentor).toBe('John Doe');
  });

  // Test 3: Actions are properly mocked
  it('should have properly mocked actions', () => {
    const action = fetchMentorMenteeData();

    expect(action).toEqual({ type: 'FETCH_MENTOR_MENTEE_DATA' });
  });

  // Test 4: Reducer is properly mocked
  it('should have a properly mocked reducer', () => {
    const mockComponent = () => <div>Test</div>;
    const wrappedComponent = injectReducer({
      key: 'test',
      reducer: () => ({}),
    })(mockComponent);

    expect(wrappedComponent).toBe(mockComponent);
  });

  // Test 5: Saga is properly mocked
  it('should have a properly mocked saga', () => {
    const mockComponent = () => <div>Test</div>;
    const wrappedComponent = injectSaga({ key: 'test', saga: () => {} })(
      mockComponent,
    );

    expect(wrappedComponent).toBe(mockComponent);
  });
});
