import React, { useState, useEffect, useMemo } from 'react';
import { connect } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { PageHeader, notification, Row, Col, Input } from 'antd';
import PropTypes from 'prop-types';
import { createStructuredSelector } from 'reselect';
import { compose } from 'redux';
import { useInjectSaga } from 'utils/injectSaga';
import { useInjectReducer } from 'utils/injectReducer';
import { fetchEmployeeProfile, fetchEmployeeProjects } from './actions';
import { makeSelectEmployeeProfile } from './selectors';
import { isSuperAdmin } from '../../components/SideBar';
import { getUserData } from '../../utils/Helper';
import EmployeeDetails from '../../components/EmployeeDetails';
import RaiseQuery from '../../components/PersonalPerformance/Raisequery';
import Submit from '../../components/PersonalPerformance/Submit';
import TechRoadMap from '../../components/PersonalPerformance/TechRoadMap';
import {
  DetailCard,
  Label,
  DetailRow,
  HeaderCell,
  TextAreaContainer,
  StyledTextArea,
  SaveButton,
} from '../../components/PLIDetails/StyledPLIDetails';
import request from '../../utils/request';
import { API_ENDPOINTS } from './constants';
import saga from './saga';
import reducer from './reducer';
import FreezePLI from '../../components/PersonalPerformance/FreezePli';
import { EmployeeProfileProvider } from './context';

const key = 'employeeProfile';

function monthNameToInt(monthName) {
  const months = {
    january: 1,
    february: 2,
    march: 3,
    april: 4,
    may: 5,
    june: 6,
    july: 7,
    august: 8,
    september: 9,
    october: 10,
    november: 11,
    december: 12,
  };
  if (!monthName) return null;
  return months[monthName.toLowerCase()] || null;
}

function intToMonthName(monthNum) {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  return months[monthNum - 1] || 'January';
}

const EmployeeProfile = ({ fetchProfile, match, profileState }) => {
  useInjectReducer({ key, reducer });
  useInjectSaga({ key, saga });

  const [selectedMonth, setSelectedMonth] = useState(null);
  const [projectRows, setProjectRows] = useState([
    {
      id: 1,
      project: null,
      projectType: null,
      weightage: '',
      projectName: null,
    },
  ]);
  const [loading, setLoading] = useState(false);
  const [showPLIDetails, setShowPLIDetails] = useState(false);
  const [pliDataMap, setPliDataMap] = useState({});
  const [commentsMap, setCommentsMap] = useState({});
  const [editingMap, setEditingMap] = useState({});
  const [projects, setProjects] = useState([]);
  const [ids, setIds] = useState({
    menteeId: null,
    mentorId: null,
    parameterIds: {},
  });

  const [existingPLIData, setExistingPLIData] = useState(null);
  const { profile, ratedProjects, additionalProjects } = profileState || {};

  console.log(existingPLIData);

  // mentee view page
  const location = useLocation();
  const query = new URLSearchParams(location.search);

  const pliratingId = query.get('pliRatingId'); // "1239284"
  const empId = query.get('menteeId');
  const view = query.get('view');
  const isMenteeView = view === 'mentee-view';

  const user = getUserData();
  const isSuperAdminUser = isSuperAdmin(user);

  // const isEditable = useMemo(
  //   () => (isSubmitPath || isSuperAdminUser) && !isQueryPath,
  //   [isSubmitPath, isQueryPath, isSuperAdminUser],
  // );

  const tempProjects = useMemo(() => {
    const rated = (ratedProjects || []).map(p => ({
      label: p.projectName,
      value: p.id,
      type: 'Rated',
      projectName: p.projectName,
    }));
    const additional = (additionalProjects || []).map(p => ({
      label: p.projectName,
      value: p.id,
      type: 'Additional',
      projectName: p.projectName,
    }));
    return [...rated, ...additional];
  }, [ratedProjects, additionalProjects]);

  useEffect(() => {
    const { employeeId } = match.params;

    // If we have a direct URL with pliRatingId and menteeId (empId),
    // fetch the profile using the menteeId
    if (pliratingId && view && empId) {
      fetchProfile(empId);
    } else {
      // Otherwise use the employeeId from route params
      fetchProfile(employeeId);
    }
    // Only include dependencies that should trigger a re-fetch
    // Don't include profile to avoid infinite loops
  }, [fetchProfile, match.params, pliratingId, view, empId]);

  useEffect(() => {
    // Only fetch PLI rating if we have the necessary data
    if (pliratingId && view) {
      fetchPLIRatingById(pliratingId);
    } else if (profile && profile.empId && isSuperAdminUser) {
      fetchExistingPLIData(profile.empId);
    }
    // Don't include the result of fetchPLIRatingById in dependencies
  }, [pliratingId, view, profile, isSuperAdminUser]);

  const fetchPLIRatingById = async id => {
    try {
      setLoading(true);
      const response = await request(
        `${API_ENDPOINTS.PLI_RATING_BY_ID}?pliRatingId=${id}`,
        { method: 'GET' },
      );

      if (response && response.status === 1 && response.data) {
        const pliData = response.data;

        setExistingPLIData(pliData);

        const monthName = intToMonthName(pliData.month);
        setSelectedMonth(monthName);

        const newProjectRows = pliData.projectRatings.map(
          (projectRating, index) => {
            const paramScores = projectRating.parameterScores || [];
            const firstParam = paramScores.length > 0 ? paramScores[0] : null;
            const parameter =
              firstParam && firstParam.parameter ? firstParam.parameter : null;
            const paramType =
              parameter && parameter.type ? parameter.type : 'Fixed';

            const projectName =
              projectRating.project && projectRating.project.name
                ? projectRating.project.name
                : '';

            return {
              id: index + 1,
              project: projectRating.project.name,
              projectType: paramType,
              weightage: projectRating.projectWeightage || 100,
              projectName,
            };
          },
        );

        setProjectRows(newProjectRows);

        const newPliDataMap = {};

        pliData.projectRatings.forEach(projectRating => {
          const paramScores = projectRating.parameterScores || [];
          const firstParam = paramScores.length > 0 ? paramScores[0] : null;
          const paramType =
            firstParam && firstParam.parameter && firstParam.parameter.type
              ? firstParam.parameter.type
              : 'Fixed';
          const projectKey = ''
            .concat(projectRating.project.name, '_')
            .concat(paramType);

          const sprintData = {};
          projectRating.parameterScores.forEach(function(parameterScore) {
            if (
              parameterScore.childScores &&
              parameterScore.childScores.length > 0
            ) {
              parameterScore.childScores.forEach(childScore => {
                const paramName = childScore.childParameter;

                childScore.sprintScores.forEach(sprintScore => {
                  const sprintName = sprintScore.sprintNumber;

                  if (!sprintData[sprintName]) {
                    sprintData[sprintName] = {};
                  }

                  sprintData[sprintName][paramName] = {
                    value: sprintScore.score,
                    weightage: childScore.childParameterWeightage,
                    calculation: childScore.calculation,
                    weightageAverage: childScore.weightageAverage,
                  };
                });
              });
            }
          });

          newPliDataMap[projectKey] = sprintData;
        });

        setPliDataMap(newPliDataMap);
        setShowPLIDetails(true);

        const newCommentsMap = {};
        pliData.projectRatings.forEach(projectRating => {
          const paramScores = projectRating.parameterScores || [];
          const firstParam = paramScores.length > 0 ? paramScores[0] : null;
          const paramType =
            firstParam && firstParam.parameter && firstParam.parameter.type
              ? firstParam.parameter.type
              : 'Fixed';
          const projectKey = ''
            .concat(projectRating.project.name, '_')
            .concat(paramType);

          newCommentsMap[projectKey] =
            firstParam && firstParam.comments ? firstParam.comments : '';
        });

        setCommentsMap(newCommentsMap);

        setIds({
          empId:
            pliData.mentee && pliData.mentee.employeeId
              ? pliData.mentee.employeeId
              : null,
          mentorId:
            pliData.mentor && pliData.mentor.employeeId
              ? pliData.mentor.employeeId
              : null,
          parameterIds: pliData.projectRatings.reduce((acc, projectRating) => {
            const paramScores = projectRating.parameterScores || [];
            const firstParam = paramScores.length > 0 ? paramScores[0] : null;
            const paramType =
              firstParam && firstParam.parameter && firstParam.parameter.type
                ? firstParam.parameter.type
                : 'Fixed';
            const projectKey = ''
              .concat(projectRating.project.name, '_')
              .concat(paramType);

            acc[projectKey] =
              firstParam && firstParam._id ? firstParam._id : null;
            return acc;
          }, {}),
        });
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to fetch PLI rating data',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchExistingPLIData = async employeeId => {
    try {
      setLoading(true);
      const response = await request(
        `${API_ENDPOINTS.PLI_RATING_BY_EMPLOYEE}?employeeId=${employeeId}`,
        { method: 'GET' },
      );

      if (
        response &&
        response.status === 1 &&
        response.data &&
        response.data.data &&
        response.data.data.length > 0
      ) {
        const pliData = response.data.data[0];

        setExistingPLIData(pliData);

        const monthName = intToMonthName(pliData.month);
        setSelectedMonth(monthName);

        const newProjectRows = pliData.projectRatings.map(
          (projectRating, index) => {
            const paramScores = projectRating.parameterScores || [];
            const firstParam = paramScores.length > 0 ? paramScores[0] : null;
            const parameter =
              firstParam && firstParam.parameter ? firstParam.parameter : null;
            const paramType =
              parameter && parameter.type ? parameter.type : 'Fixed';

            const projectName =
              projectRating.project && projectRating.project.name
                ? projectRating.project.name
                : '';

            return {
              id: index + 1,
              project: projectRating.project.name,
              projectType: paramType,
              weightage: projectRating.projectWeightage || 100,
              projectName,
            };
          },
        );

        setProjectRows(newProjectRows);

        const newPliDataMap = {};

        pliData.projectRatings.forEach(projectRating => {
          const paramScores = projectRating.parameterScores || [];
          const firstParam = paramScores.length > 0 ? paramScores[0] : null;
          const paramType =
            firstParam && firstParam.parameter && firstParam.parameter.type
              ? firstParam.parameter.type
              : 'Fixed';
          const projectKey = ''
            .concat(projectRating.project.name, '_')
            .concat(paramType);

          const sprintData = {};
          projectRating.parameterScores.forEach(function(parameterScore) {
            if (
              parameterScore.childScores &&
              parameterScore.childScores.length > 0
            ) {
              parameterScore.childScores.forEach(childScore => {
                const paramName = childScore.childParameter;

                childScore.sprintScores.forEach(sprintScore => {
                  const sprintName = sprintScore.sprintNumber;

                  if (!sprintData[sprintName]) {
                    sprintData[sprintName] = {};
                  }

                  sprintData[sprintName][paramName] = {
                    value: sprintScore.score,
                    weightage: childScore.childParameterWeightage,
                    calculation: childScore.calculation,
                    weightageAverage: childScore.weightageAverage,
                  };
                });
              });
            }
          });

          newPliDataMap[projectKey] = sprintData;
        });

        setPliDataMap(newPliDataMap);
        setShowPLIDetails(true);

        const newCommentsMap = {};
        pliData.projectRatings.forEach(projectRating => {
          const paramScores = projectRating.parameterScores || [];
          const firstParam = paramScores.length > 0 ? paramScores[0] : null;
          const paramType =
            firstParam && firstParam.parameter && firstParam.parameter.type
              ? firstParam.parameter.type
              : 'Fixed';
          const projectKey = ''.concat(projectRating.id, '_').concat(paramType);

          newCommentsMap[projectKey] =
            firstParam && firstParam.comments ? firstParam.comments : '';
        });

        setCommentsMap(newCommentsMap);

        setIds({
          menteeId:
            pliData.mentee && pliData.mentee && pliData.mentee.id
              ? pliData.mentee.id
              : null,
          mentorId:
            pliData.mentor && pliData.mentor && pliData.mentor.id
              ? pliData.mentor.id
              : null,
          parameterIds: pliData.projectRatings.reduce((acc, projectRating) => {
            const paramScores = projectRating.parameterScores || [];
            const firstParam = paramScores.length > 0 ? paramScores[0] : null;
            const paramType =
              firstParam && firstParam.parameter && firstParam.parameter.type
                ? firstParam.parameter.type
                : 'Fixed';
            const projectKey = ''
              .concat(projectRating.id, '_')
              .concat(paramType);

            acc[projectKey] =
              firstParam && firstParam.id ? firstParam.id : null;
            return acc;
          }, {}),
        });
      }
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to fetch existing PLI data',
      });
    } finally {
      setLoading(false);
    }
  };

  const mappedProfile = {
    name: profile && profile.name,
    employeeId:
      profile && (profile.empId !== undefined ? String(profile.empId) : ''),
    department: profile && profile.department,
    reportingManager: profile && profile.reportingManager,
    managerId:
      profile &&
      (profile.reportingManagerId !== undefined
        ? String(profile.reportingManagerId)
        : ''),
    doj: profile && profile.doj,
    pliDuration: profile && profile.pliDuration,
    role: profile && profile.role,
  };

  const handleMonthChange = monthInWords => {
    setSelectedMonth(monthInWords);
    const monthNumber = monthNameToInt(monthInWords);
    // Reset states
    setPliDataMap({});
    setShowPLIDetails(false);

    // Fetch projects and automatically add rated projects
    const menteeId = profile && profile.empId;
    if (!menteeId) return;

    const url = `${API_ENDPOINTS.PLI_PROJECTS_BY_LABEL}?menteeLabel=${
      profile.label
    }&month=${monthNumber}&year=${new Date().getFullYear()}`;

    setLoading(true);
    request(url, { method: 'GET' })
      .then(json => {
        if (json.success && json.data) {
          // Store all projects
          setProjects([
            ...json.data.ratedProjects,
            ...json.data.additionalProjects,
          ]);

          // Automatically add rated projects to projectRows
          if (json.data.ratedProjects && json.data.ratedProjects.length > 0) {
            const ratedProjectRows = json.data.ratedProjects.map(
              (project, index) => ({
                id: index + 1,
                project: project._id,
                projectName: project.projectName,
                projectType: project.projectType || 'Fixed', // Default to Fixed if null
                weightage: '', // User will set this
              }),
            );
            setProjectRows(ratedProjectRows);
          } else {
            // If no rated projects, set empty project rows
            setProjectRows([]);
          }
        } else {
          setProjects([]);
          setProjectRows([
            {
              id: 1,
              project: null,
              projectType: null,
              weightage: '',
              projectName: null,
            },
          ]);
        }
        setLoading(false);
      })
      .catch(() => {
        notification.error({
          message: 'Error',
          description: 'Failed to fetch projects data',
        });
        setLoading(false);
      });
  };

  // // This function is now integrated into handleMonthChange
  // const fetchProjects = monthValue => {
  //   // This is kept for compatibility with other parts of the code
  //   // but the actual fetching is now done in handleMonthChange
  // };

  const handleProjectChange = (value, rowId, option = {}) => {
    const projectSelected = tempProjects.find(proj => proj.value === value);
    let projectName = projectSelected ? projectSelected.projectName : null;
    if (option && option.label) {
      projectName = option.label;
    }

    setProjectRows(prev =>
      prev.map(row => {
        if (row.id === rowId) {
          return {
            ...row,
            project: value,
            projectName,
            projectType: projectSelected ? projectSelected.type : 'Fixed',
            isAdditional: true, // Mark as additional since it was selected from dropdown
          };
        }
        return row;
      }),
    );
  };

  const handleProjectTypeChange = (value, rowId) => {
    setProjectRows(prev =>
      prev.map(row => {
        if (row.id === rowId) {
          return {
            ...row,
            projectType: value,
          };
        }
        return row;
      }),
    );
  };

  const handleWeightageChange = (value, rowId) => {
    const trimmed = String(value).trim();
    const newValue = Number(trimmed);

    const otherRowsTotal = projectRows
      .filter(row => row.id !== rowId)
      .reduce((sum, row) => sum + (Number(row.weightage) || 0), 0);

    if (otherRowsTotal + newValue > 100) {
      notification.error({
        message: 'Invalid Weightage',
        description: 'Total weightage cannot exceed 100%',
      });
      return;
    }

    setProjectRows(prev =>
      prev.map(row => (row.id === rowId ? { ...row, weightage: value } : row)),
    );
  };

  const handleAddRow = () => {
    setProjectRows(prev => [
      ...prev,
      {
        id: prev.length > 0 ? Math.max(...prev.map(r => r.id)) + 1 : 1,
        project: null,
        projectType: null,
        weightage: '',
        projectName: null,
        isAdditional: true, // Flag to indicate this is an additional project row
      },
    ]);
  };

  const handleRemoveRow = rowId => {
    if (projectRows.length <= 1) {
      notification.info({
        message: 'Cannot Remove',
        description: 'At least one project row must remain.',
      });
      return;
    }

    setProjectRows(prev => prev.filter(row => row.id !== rowId));
  };

  const handleSync = async () => {
    const hasInvalid = projectRows.some(row => {
      const trimmed = String(row.weightage).trim();
      const value = Number(trimmed);
      return !trimmed || Number.isNaN(value) || value <= 0;
    });

    if (hasInvalid) {
      notification.error({
        message: 'Invalid Input',
        description: 'All projects must have valid weightage > 0.',
      });
      return;
    }

    const total = projectRows.reduce(
      (sum, row) => sum + (Number(row.weightage) || 0),
      0,
    );

    if (total !== 100) {
      notification.warning({
        message: 'Weightage Mismatch',
        description: `Total weightage must equal 100%. Current: ${total}%`,
      });
      return;
    }

    setLoading(true);
    const newMap = {};
    const monthNumber = monthNameToInt(selectedMonth);
    const projectsToProcess = projectRows.filter(row => row.project);

    await Promise.all(
      projectsToProcess.map(async row => {
        try {
          const url = `${
            API_ENDPOINTS.PROJECT_SPRINT_DATA
          }?project=${encodeURIComponent(
            row.projectName,
          )}&month=${monthNumber}&year=${new Date().getFullYear()}&employeeName=${
            profile.label
          }&projectType=${row.projectType || 'Fixed'}`;

          const json = await request(url, {
            method: 'GET',
          });

          const projectKey1 = `${row.project}_${row.projectType || 'Fixed'}`;

          setIds(prevIds => ({
            menteeId: json.data.menteeId || prevIds.menteeId,
            mentorId: json.data.mentorId || prevIds.mentorId,
            parameterIds: {
              ...prevIds.parameterIds,
              [projectKey1]: json.data.parameterId,
            },
          }));

          const sprintData = {};
          if (json && json.data && Array.isArray(json.data.data)) {
            const allSprints = new Set();
            json.data.data.forEach(item => {
              if (
                item.pliParameter &&
                Array.isArray(item.pliParameter.sprintwiseScores)
              ) {
                item.pliParameter.sprintwiseScores.forEach(sprint => {
                  if (sprint.sprintname) {
                    allSprints.add(sprint.sprintname);
                  }
                });
              }
            });

            allSprints.forEach(sprintName => {
              sprintData[sprintName] = {};
            });

            json.data.data.forEach(item => {
              if (item.pliParameter) {
                const { name: paramName, weightage } = item.pliParameter;
                if (paramName) {
                  if (Array.isArray(item.pliParameter.sprintwiseScores)) {
                    const scores = item.pliParameter.sprintwiseScores
                      .map(sprint => sprint.score)
                      .filter(score => score !== undefined && score !== null);

                    let averageScore = 0;

                    if (scores.length > 0) {
                      const totalScore = scores.reduce(
                        (sum, score) => sum + Number(score),
                        0,
                      );
                      averageScore = totalScore / scores.length;
                    } else {
                      averageScore = 0;
                    }

                    const calculatedAverage = Number(averageScore.toFixed(2));

                    item.pliParameter.sprintwiseScores.forEach(sprint => {
                      const sprintName = sprint.sprintname;
                      if (sprintName && sprintData[sprintName]) {
                        sprintData[sprintName][paramName] = {
                          value:
                            sprint.score !== undefined ? sprint.score : 'N/A',
                          weightage,
                          calculation: calculatedAverage,
                          weightageAverage: (
                            calculatedAverage * (weightage || 0)
                          ).toFixed(2),
                        };
                      }
                    });
                  }
                }
              }
            });
          }

          const projectKey = `${row.project}_${row.projectType || 'Fixed'}`;

          newMap[projectKey] = sprintData;
        } catch (err) {
          notification.error({
            message: 'Fetch Error',
            description: `Failed to fetch PLI data for ${row.projectName}`,
          });
        }
      }),
    );

    setPliDataMap(newMap);
    setShowPLIDetails(true);
    setLoading(false);
  };

  const getProjectTitle = projectId => {
    const project = projectOptions.find(p => p.value === projectId);

    return project ? project.label : 'Unknown Project';
  };

  const projectOptions = projects.map(p => {
    const { _id: id, projectName, type } = p;
    return {
      label: projectName,
      value: id,
      type: type || '',
      projectName,
    };
  });

  return (
    <div>
      <PageHeader title="Employee Profile" />
      <EmployeeDetails
        profile={mappedProfile}
        selectedMonth={selectedMonth}
        projectRows={projectRows}
        loading={loading}
        showPLIDetails={showPLIDetails}
        tempProjects={projectOptions}
        handleMonthChange={handleMonthChange}
        handleProjectChange={handleProjectChange}
        handleProjectTypeChange={handleProjectTypeChange}
        handleWeightageChange={handleWeightageChange}
        handleAddRow={handleAddRow}
        handleRemoveRow={handleRemoveRow}
        handleSync={handleSync}
        getProjectType={key1 => getProjectTitle(key1).split(' - ')[1]}
        isMenteeView={isMenteeView}
      />
      {showPLIDetails && (
        <>
          {Object.entries(pliDataMap).map(([projectKey, pliData]) => {
            const sprints = Object.keys(pliData);

            const [projectId, projectType] = projectKey.split('_');
            const projectTitle = getProjectTitle(projectId);

            return (
              <Row key={projectKey} style={{ marginBottom: '20px' }}>
                <Col span={24}>
                  <DetailCard title={`${projectTitle} - ${projectType}`}>
                    <DetailRow>
                      <Col span={6}>
                        <Label>Parameters</Label>
                      </Col>
                      {sprints.map(s => (
                        <Col key={s} span={3}>
                          <HeaderCell>{s}</HeaderCell>
                        </Col>
                      ))}
                      <Col span={3}>
                        <HeaderCell>Calculation</HeaderCell>
                      </Col>
                      <Col span={3}>
                        <HeaderCell>Weightage Avg</HeaderCell>
                      </Col>
                    </DetailRow>
                    {Object.keys(pliData[sprints[0]] || {}).map(param => (
                      <DetailRow key={param}>
                        <Col span={6}>
                          <Label>{param}</Label>
                        </Col>
                        {sprints.map(s => (
                          <Col key={`${s}-${param}`} span={3}>
                            <Input
                              style={{ width: '100%' }}
                              value={
                                pliData[s] &&
                                pliData[s][param] &&
                                pliData[s][param].value !== undefined
                                  ? pliData[s][param].value
                                  : ''
                              }
                              onChange={e => {
                                const newPliDataMap = JSON.parse(
                                  JSON.stringify(pliDataMap),
                                );
                                if (!newPliDataMap[projectKey][s][param]) {
                                  newPliDataMap[projectKey][s][param] = {};
                                }
                                newPliDataMap[projectKey][s][param].value =
                                  e.target.value;
                                setPliDataMap(newPliDataMap);
                              }}
                              disabled={isMenteeView}
                              placeholder="Enter value"
                            />
                          </Col>
                        ))}
                        <Col span={3}>
                          <Input
                            style={{ width: '100%' }}
                            value={
                              pliData[sprints[0]] &&
                              pliData[sprints[0]][param] &&
                              pliData[sprints[0]][param].calculation !==
                                undefined
                                ? pliData[sprints[0]][param].calculation
                                : ''
                            }
                            onChange={e => {
                              const newPliDataMap = JSON.parse(
                                JSON.stringify(pliDataMap),
                              );
                              if (
                                !newPliDataMap[projectKey][sprints[0]][param]
                              ) {
                                newPliDataMap[projectKey][sprints[0]][
                                  param
                                ] = {};
                              }
                              newPliDataMap[projectKey][sprints[0]][
                                param
                              ].calculation = e.target.value;
                              setPliDataMap(newPliDataMap);
                            }}
                            disabled={isMenteeView}
                            placeholder="Enter calculation"
                          />
                        </Col>
                        <Col span={3}>
                          <Input
                            style={{ width: '100%' }}
                            value={
                              pliData[sprints[0]] &&
                              pliData[sprints[0]][param] &&
                              pliData[sprints[0]][param].weightageAverage !==
                                undefined
                                ? pliData[sprints[0]][param].weightageAverage
                                : ''
                            }
                            onChange={e => {
                              const newPliDataMap = JSON.parse(
                                JSON.stringify(pliDataMap),
                              );
                              if (
                                !newPliDataMap[projectKey][sprints[0]][param]
                              ) {
                                newPliDataMap[projectKey][sprints[0]][
                                  param
                                ] = {};
                              }
                              newPliDataMap[projectKey][sprints[0]][
                                param
                              ].weightageAverage = e.target.value;
                              setPliDataMap(newPliDataMap);
                            }}
                            disabled={isMenteeView}
                            placeholder="Enter weightage avg"
                          />
                        </Col>
                      </DetailRow>
                    ))}
                    <TextAreaContainer>
                      <Label>Comments</Label>

                      <StyledTextArea
                        value={commentsMap[projectKey] || ''}
                        onChange={e =>
                          setCommentsMap(prev => ({
                            ...prev,
                            [projectKey]: e.target.value,
                          }))
                        }
                        disabled={!editingMap[projectKey]}
                        placeholder="Enter your comments for this project..."
                      />
                    </TextAreaContainer>
                    <SaveButton
                      type="primary"
                      onClick={() => {
                        notification.success({
                          message: `Comment Saved for ${projectTitle}`,
                        });
                        setEditingMap(prev => ({
                          ...prev,
                          [projectKey]: false,
                        }));
                      }}
                      disabled={isMenteeView}
                    >
                      Save
                    </SaveButton>
                  </DetailCard>
                </Col>
              </Row>
            );
          })}

          {Object.keys(pliDataMap).length > 0 && (
            <Row style={{ marginTop: '20px', marginBottom: '20px' }}>
              <Col span={24} style={{ textAlign: 'center' }} />
            </Row>
          )}
        </>
      )}
      <EmployeeProfileProvider
        value={{
          pliDataMap,
          profile,
          selectedMonth,
          commentsMap,
        }}
      >
        {showPLIDetails && <TechRoadMap />}
        {showPLIDetails && !isSuperAdminUser && !isMenteeView && (
          <Submit ids={ids} />
        )}
        {showPLIDetails && isSuperAdminUser && (
          <FreezePLI
            ids={ids}
            mentorId={profile.mentorId}
            menteeId={profile.menteeId}
          />
        )}
        {showPLIDetails && isMenteeView && <RaiseQuery />}
      </EmployeeProfileProvider>
    </div>
  );
};

EmployeeProfile.propTypes = {
  fetchProfile: PropTypes.func.isRequired,
  match: PropTypes.object.isRequired,
  profileState: PropTypes.shape({
    loading: PropTypes.bool,
    error: PropTypes.object,
    profile: PropTypes.object,
    projectsLoading: PropTypes.bool,
    projectsError: PropTypes.object,
    ratedProjects: PropTypes.array,
    additionalProjects: PropTypes.array,
  }),
};

const mapStateToProps = createStructuredSelector({
  profileState: makeSelectEmployeeProfile(),
});

const mapDispatchToProps = dispatch => ({
  fetchProfile: employeeId => dispatch(fetchEmployeeProfile(employeeId)),
  fetchProjects: (employeeId, month) =>
    dispatch(fetchEmployeeProjects(employeeId, month)),
});

export default compose(
  connect(
    mapStateToProps,
    mapDispatchToProps,
  ),
)(EmployeeProfile);
