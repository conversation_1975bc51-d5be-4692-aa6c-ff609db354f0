import { API_URL } from '../constants';

export const FET<PERSON>_EMPLOYEE_PROFILE = 'FETCH_EMPLOYEE_PROFILE';
export const FETCH_EMPLOYEE_PROFILE_SUCCESS = 'FETCH_EMPLOYEE_PROFILE_SUCCESS';
export const FETCH_EMPLOYEE_PROFILE_ERROR = 'FETCH_EMPLOYEE_PROFILE_ERROR';

export const FETCH_EMPLOYEE_PROJECTS = 'FETCH_EMPLOYEE_PROJECTS';
export const FETCH_EMPLOYEE_PROJECTS_SUCCESS =
  'FETCH_EMPLOYEE_PROJECTS_SUCCESS';
export const FETCH_EMPLOYEE_PROJECTS_ERROR = 'FETCH_EMPLOYEE_PROJECTS_ERROR';

// API endpoints for employee profile
export const API_ENDPOINTS = {
  EMPLOYEE_PROFILE: `${API_URL}/pli/employee-profile`,
  <PERSON>MPLOYEE_PROJECTS: `${API_URL}/pli/employee-projects`,
  PLI_PROJECTS_BY_LABEL: `${API_URL}/api/pli-projects-by-label`,
  PROJECT_SPRINT_DATA: `${API_URL}/project-sprint-data`,
  UPDATE_SPRINT_SCORES: `${API_URL}/api/pli-rating/sprint-scores`,
  PLI_RATING_BY_EMPLOYEE: `${API_URL}/api/pli-rating/by-employee-id`,
  PLI_RATING_BY_ID: `${API_URL}/api/pli-rating/by-id`,
  PLI_FILES: `${API_URL}/pli`,
};
