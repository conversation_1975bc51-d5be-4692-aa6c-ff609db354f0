import {
  FET<PERSON>_EMPLOYEE_PROFILE,
  FETCH_EMPLOYEE_PROFILE_SUCCESS,
  FETCH_EMPLOYEE_PROFILE_ERROR,
  FETCH_EMPLOYEE_PROJECTS,
  FETCH_EMPLOYEE_PROJECTS_SUCCESS,
  FETCH_EMPLOYEE_PROJECTS_ERROR,
} from './actions';

const initialState = {
  loading: false,
  error: null,
  profile: {},
  projectsLoading: false,
  projectsError: null,
  ratedProjects: [],
  additionalProjects: [],
};

const employeeProfileReducer = (state = initialState, action) => {
  switch (action.type) {
    case FETCH_EMPLOYEE_PROFILE:
      return {
        ...state,
        loading: true,
        error: null,
      };
    case FETCH_EMPLOYEE_PROFILE_SUCCESS:
      return {
        ...state,
        loading: false,
        profile: action.profile,
      };
    case FETCH_EMPLOYEE_PROFILE_ERROR:
      return {
        ...state,
        loading: false,
        error: action.error,
      };
    case FETCH_EMPLOYEE_PROJECTS:
      return {
        ...state,
        projectsLoading: true,
        projectsError: null,
      };
    case FETCH_EMPLOYEE_PROJECTS_SUCCESS:
      return {
        ...state,
        projectsLoading: false,
        ratedProjects: action.projects.ratedProjects || [],
        additionalProjects: action.projects.additionalProjects || [],
      };
    case FETCH_EMPLOYEE_PROJECTS_ERROR:
      return {
        ...state,
        projectsLoading: false,
        projectsError: action.error,
      };
    default:
      return state;
  }
};

export default employeeProfileReducer;
