export const FETCH_EMPLOYEE_PROFILE = 'FET<PERSON>_EMPLOYEE_PROFILE';
export const FETCH_EMPLOYEE_PROFILE_SUCCESS = 'FETCH_EMPLOYEE_PROFILE_SUCCESS';
export const FETCH_EMPLOYEE_PROFILE_ERROR = 'FETCH_EMPLOYEE_PROFILE_ERROR';

export const FETCH_EMPLOYEE_PROJECTS = 'FETCH_EMPLOYEE_PROJECTS';
export const FETCH_EMPLOYEE_PROJECTS_SUCCESS =
  'FETCH_EMPLOYEE_PROJECTS_SUCCESS';
export const FETCH_EMPLOYEE_PROJECTS_ERROR = 'FETCH_EMPLOYEE_PROJECTS_ERROR';

export const fetchEmployeeProfile = employeeId => ({
  type: FETCH_EMPLOYEE_PROFILE,
  employeeId,
});

export const fetchEmployeeProfileSuccess = profile => ({
  type: FETCH_EMPLOYEE_PROFILE_SUCCESS,
  profile,
});

export const fetchEmployeeProfileError = error => ({
  type: FETCH_EMPLOYEE_PROFILE_ERROR,
  error,
});

export const fetchEmployeeProjects = (employeeId, month) => ({
  type: FETCH_EMPLOYEE_PROJECTS,
  employeeId,
  month,
});

export const fetchEmployeeProjectsSuccess = projects => ({
  type: FETCH_EMPLOYEE_PROJECTS_SUCCESS,
  projects,
});

export const fetchEmployeeProjectsError = error => ({
  type: FETCH_EMPLOYEE_PROJECTS_ERROR,
  error,
});
