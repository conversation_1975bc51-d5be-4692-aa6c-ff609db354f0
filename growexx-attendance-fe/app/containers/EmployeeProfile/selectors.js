import { createSelector } from 'reselect';

const selectEmployeeProfileDomain = state => state.employeeProfile || {};

export const makeSelectEmployeeProfile = () =>
  createSelector(
    selectEmployeeProfileDomain,
    substate => ({
      loading: substate.loading,
      error: substate.error,
      profile: substate.profile,
      projectsLoading: substate.projectsLoading,
      projectsError: substate.projectsError,
      ratedProjects: substate.ratedProjects,
      additionalProjects: substate.additionalProjects,
    }),
  );
