import { takeLatest, call, put } from 'redux-saga/effects';
import {
  FETCH_EMPLOYEE_PROFILE,
  FETCH_EMPLOYEE_PROJECTS,
  API_ENDPOINTS,
} from './constants';
import {
  fetchEmployeeProfileSuccess,
  fetchEmployeeProfileError,
  fetchEmployeeProjectsSuccess,
  fetchEmployeeProjectsError,
} from './actions';
import request from '../../utils/request';

/**
 * Worker saga to fetch employee profile data
 * @param {Object} action - Redux action containing employeeId
 */
function* fetchEmployeeProfileSaga(action) {
  try {
    const { employeeId } = action;
    const requestURL = `${API_ENDPOINTS.EMPLOYEE_PROFILE}/${employeeId}`;

    // Let request.js handle the Authorization header
    const response = yield call(request, requestURL, {
      method: 'GET',
    });

    // Pass the actual employee data to the reducer
    yield put(fetchEmployeeProfileSuccess(response.data));
  } catch (error) {
    yield put(fetchEmployeeProfileError(error));
  }
}

/**
 * Worker saga to fetch employee projects
 * @param {Object} action - Redux action containing employeeId and month
 */
function* fetchEmployeeProjectsSaga(action) {
  try {
    const { employeeId, month } = action;
    const requestURL = `${
      API_ENDPOINTS.EMPLOYEE_PROJECTS
    }/${employeeId}?month=${month}`;

    const response = yield call(request, requestURL, {
      method: 'GET',
    });

    if (response.success) {
      yield put(fetchEmployeeProjectsSuccess(response.data));
    } else {
      throw new Error('Failed to fetch projects');
    }
  } catch (error) {
    yield put(fetchEmployeeProjectsError(error));
  }
}

/**
 * Root saga for employee profile
 */
export default function* employeeProfileSaga() {
  yield takeLatest(FETCH_EMPLOYEE_PROFILE, fetchEmployeeProfileSaga);
  yield takeLatest(FETCH_EMPLOYEE_PROJECTS, fetchEmployeeProjectsSaga);
}
