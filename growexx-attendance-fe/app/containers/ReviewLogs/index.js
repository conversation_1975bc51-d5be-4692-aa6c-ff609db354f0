/* eslint-disable no-underscore-dangle */
/**
 *
 * ReviewLogs
 *
 */
import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Helmet } from 'react-helmet';
import { FormattedMessage } from 'react-intl';
import request from 'utils/request';
import { get, merge } from 'lodash';
import { withRouter } from 'react-router-dom';
import { RightOutlined, LeftOutlined } from '@ant-design/icons';
import Skeleton from 'react-loading-skeleton';
import 'react-loading-skeleton/dist/skeleton.css';

import {
  notification,
  Space,
  Modal,
  Row,
  Col,
  Select,
  Typography,
  Button,
  Alert,
} from 'antd';

import moment from 'moment';
import messages from './messages';
import {
  PageHeaderWrapper,
  StyledReviewLogsContainer,
  StyledLogCellContainer,
  LegendRow,
  LegendItem,
  StyledLegendColor,
} from './StyledContainer';
import { API_ENDPOINTS, GENERIC_MOMENT_DATE_FORMAT, ROLES } from '../constants';
import { getUserData } from '../../utils/Helper';
import LoadingIndicator from '../../components/LoadingIndicator';
import LogTag from '../../components/LogTag';

const { Text } = Typography;

moment.locale('en', {
  week: {
    dow: 1,
  },
});

const logStatusGreen = 'rgb(173, 238, 184)';
const logStatusYellow = 'rgba(252, 227, 176, 1)';
const logStatusRed = 'rgba(247, 216, 217, 1)';

const LEGENDS = [
  {
    label: 'Pending Logs',
    value: logStatusYellow,
  },
  {
    label: 'Approved Logs',
    value: logStatusGreen,
  },
  {
    label: 'Rejected Logs',
    value: logStatusRed,
  },
];

export class ReviewLogs extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showNotification: false,
      notificationMessage: '',
      name: get(props, 'location.state.name', ''),
      projectId: get(props, 'location.state.projectId', ''),

      logsList: [],
      showConfirmationModal: false,
      projectList: [],
      isLoading: false,
      startDate: null,
      endDate: null,
      currentDate: null,
      selectedLogs: [],
      action: null,
      showRevokeAction: false,
      reviewDatePassed: false,
      anyUpdating: false,
      isAnyPendingForTeam: false,
    };
  }

  componentDidMount() {
    this.getServerTime();
  }

  getServerTime = () => {
    const URL = `${API_ENDPOINTS.GET_SERVER_TIME}`;

    this.setState(
      {
        isLoading: true,
      },
      () => {
        request(URL, {
          method: 'GET',
        })
          .then(res => {
            this.getProjectList(new Date(res.data));
          })
          .catch(error => {
            notification.error({
              message: <FormattedMessage {...messages.failedToLoad} />,
              description: error.message,
            });
          });
      },
    );
  };

  getProjectList = date => {
    const startDate = moment(date)
      .startOf('week')
      .subtract(1, 'week');
    const endDate = moment(date)
      .endOf('week')
      .subtract(1, 'week');
    const sDate = moment(startDate).format(GENERIC_MOMENT_DATE_FORMAT);
    const eDate = moment(endDate).format(GENERIC_MOMENT_DATE_FORMAT);

    const URL = `${
      API_ENDPOINTS.REVIEW_LOGS_PROJECTS_LIST
    }?isGetLogsForReviewLogs=1&startDate=${sDate}&endDate=${eDate}`;

    request(URL, {
      method: 'GET',
    })
      .then(res => {
        const list = get(res, 'data', []);
        if (!list || list.length === 0) {
          this.props.history.push('/logs');
          return;
        }
        const tempList = [];
        for (let i = 0; i < list.length; i += 1) {
          tempList[i] = {
            value: list[i]._id,
            label: list[i].projectName,
          };
          list[i].key = i;
        }
        const projectId = tempList[0].value || null;

        this.setState(
          {
            currentDate: date,
            startDate,
            endDate,
            isLoading: true,
            projectList: tempList,
            projectId,
          },
          this.getUserLogs(projectId, startDate, endDate),
        );
      })
      .catch(error => {
        notification.error({
          message: <FormattedMessage {...messages.failedToLoad} />,
          description: error.message,
        });
      });
  };

  getUserLogs = (projectId, startDate, endDate) => {
    if (projectId) {
      const sDate = moment(startDate).format(GENERIC_MOMENT_DATE_FORMAT);
      const eDate = moment(endDate).format(GENERIC_MOMENT_DATE_FORMAT);
      const URL = `${
        API_ENDPOINTS.ATTENDANCE_BY_PROJECT
      }?projectId=${projectId}&startDate=${sDate}&endDate=${eDate}`;

      request(URL, {
        method: 'GET',
      })
        .then(res => {
          const formattedLogsData = this.formatUserLogs(res?.data?.[0]);
          this.setState({
            isLoading: false,
            logsList: formattedLogsData,
            isAnyPendingForTeam:
              formattedLogsData.filter(t => t?.isAnyPendingForWeek).length > 0,
            reviewDatePassed: this.isReviewDatePassed(),
            showRevokeAction:
              res?.data?.[0]?.reviewManager._id === getUserData()._id,
          });
        })
        .catch(error => {
          notification.error({
            message: <FormattedMessage {...messages.failedToLoad} />,
            description: error.message,
          });
        });
    }
  };

  isRoleHR = () => getUserData().role === ROLES?.HR;

  getWeekDates = () => {
    const { startDate } = this.state;
    const weekDates = [
      {
        shortDate: moment(startDate).format('DD'),
        date: moment(startDate).format(GENERIC_MOMENT_DATE_FORMAT),
        dispDate: moment(startDate).format('DD MMM yyyy'),
        day: moment(startDate).format('ddd'),
        logs: [],
      },
    ];
    for (let i = 0; i < 6; i += 1) {
      const newDate = moment(startDate.add(1, 'days'));
      weekDates.push({
        shortDate: moment(newDate).format('DD'),
        date: moment(newDate).format(GENERIC_MOMENT_DATE_FORMAT),
        dispDate: moment(newDate).format('DD MMM yyyy'),
        day: moment(newDate).format('ddd'),
        holiday: ['Saturday', 'Sunday'].includes(
          moment(newDate).format('dddd'),
        ),
        logs: [],
      });
    }
    moment(startDate.subtract(6, 'days'));
    return weekDates;
  };

  prepareLogsByweekData(data) {
    const tempList = [];
    if (data?.team?.[0]?.manDays > 0) {
      for (let i = 0; i < data.team.length; i += 1) {
        const tempWeekly = this.getWeekDates();
        for (let j = 0; j < data?.team[i].logsByDate.length; j += 1) {
          tempWeekly.map(item => {
            if (item?.date === data?.team[i].logsByDate[j].date) {
              return merge(item, data.team[i].logsByDate[j]);
            }
            return item;
          });
        }
        tempList[i] = {
          id: data.team[i].empId,
          name: `${data?.team[i].firstName} ${data?.team[i].lastName}`,
          weeklyLogs: tempWeekly,
        };
      }
    }
    return tempList;
  }

  formatUserLogs = data => {
    const tempList = this.prepareLogsByweekData(data);
    tempList.forEach(member => {
      member.weeklyLogs.forEach(day => {
        day.logs.forEach(log => {
          const lastIndex = log.jiraIssueUrl.indexOf('/browse/');
          return merge(log, {
            ticket: log.jiraIssueUrl.substring(lastIndex + 8),
          });
        });
        const isAnyPendingForDay =
          day.logs.filter(l => l.logStatus === 0).length > 0;
        return merge(day, { isAnyPendingForDay });
      });
      const isAnyPendingForWeek =
        member.weeklyLogs.filter(m => m.isAnyPendingForDay).length > 0;
      return merge(member, { isAnyPendingForWeek });
    });
    return tempList;
  };

  isReviewDatePassed = () => {
    const reviewDate = moment(this.state.currentDate).startOf('month');
    if (moment(this.state.currentDate).format('D') < 8) {
      reviewDate.subtract(1, 'month');
    }
    return moment(this.state.endDate) < reviewDate;
  };

  handleLogClick = logData => {
    if (logData.jiraIssueUrl) {
      window.open(logData.jiraIssueUrl, '_blank');
    }
  };

  showConfirmationModal = () => {
    this.setState({
      showConfirmationModal: true,
    });
  };

  toggleModal = () => {
    const { showConfirmationModal } = this.state;
    this.setState({
      showConfirmationModal: !showConfirmationModal,
      selectedLogs: [],
      action: null,
    });
  };

  changeLogStatus = (event, log, action) => {
    event.stopPropagation();
    this.performAction([log._id], action);
  };

  changeLogStatusForDay = (logs, action) => {
    if (this.state.anyUpdating) return;
    const selectedLogs = [];
    logs.forEach(log => {
      if (log.logStatus === 0) {
        selectedLogs.push(log._id);
      }
    });
    this.performAction(selectedLogs, action);
  };

  changeLogStatusForWeek = (week, action) => {
    if (this.state.anyUpdating) return;
    const selectedLogs = [];
    week.forEach(day => {
      day.logs.forEach(log => {
        if (log.logStatus === 0) {
          selectedLogs.push(log._id);
        }
      });
    });
    this.performAction(selectedLogs, action);
  };

  confirmationHandler() {
    const { selectedLogs, action } = this.state;
    this.performAction(selectedLogs, action);
  }

  changeLogStatusForTeam = status => {
    const { logsList } = this.state;
    const selectedLogs = [];
    logsList.forEach(dev => {
      dev.weeklyLogs.forEach(day => {
        day.logs.forEach(log => {
          if (log.logStatus === 0) {
            selectedLogs.push(log._id);
          }
        });
      });
    });
    this.setState({
      showConfirmationModal: true,
      selectedLogs,
      action: status,
    });
  };

  setUpdatingFlag(logsList, selectedLogs) {
    logsList.forEach(member => {
      member.weeklyLogs.forEach(day => {
        day.logs.forEach(log => {
          const tempLog = {};
          if (selectedLogs.includes(log._id)) {
            tempLog.isUpdating = true;
          }
          return merge(log, tempLog);
        });
      });
    });
    return logsList;
  }

  performAction = (logIds, logStatus) => {
    const { projectId, startDate, endDate } = this.state;

    this.setState(
      prevState => ({
        isLoading: true,
        anyUpdating: true,
        logsList: this.setUpdatingFlag(prevState.logsList, logIds),
      }),
      () => {
        const URL = API_ENDPOINTS.UPDATE_LOG_STATUS;
        const body = {
          projectId,
          logIds,
          logStatus,
          startDate: moment(startDate).format(GENERIC_MOMENT_DATE_FORMAT),
          endDate: moment(endDate).format(GENERIC_MOMENT_DATE_FORMAT),
        };

        request(URL, {
          method: 'PUT',
          body,
        })
          .then(res => {
            this.setState(
              {
                anyUpdating: false,
                notificationMessage: get(res, 'data.message', ''),
                showNotification: true,
                showConfirmationModal: false,
                selectedLogs: [],
                action: null,
              },
              this.getUserLogs(projectId, startDate, endDate),
            );
          })
          .catch(async err => {
            const errMsg = (await err.response.json()).message;
            notification.error({
              description: errMsg,
            });
            if (errMsg === 'User does not have access to update logStatus') {
              this.getServerTime();
              return;
            }
            this.setState(
              {
                isLoading: false,
                anyUpdating: false,
                showConfirmationModal: false,
                selectedLogs: [],
                action: null,
              },
              this.getUserLogs(projectId, startDate, endDate),
            );
          });
      },
    );
  };

  confirmationModal = () => {
    const { showConfirmationModal, action, isLoading } = this.state;
    return (
      <Modal
        title="Confirmation"
        open={showConfirmationModal}
        onOk={() => this.confirmationHandler()}
        onCancel={this.toggleModal}
        okText={<FormattedMessage {...messages.yesLabel} />}
        okButtonProps={{
          disabled: isLoading,
        }}
        cancelButtonProps={{
          disabled: isLoading,
        }}
      >
        Are you sure, you want to
        {action === 1 ? ' approve all ' : ' reject all '}
        pending logs?
      </Modal>
    );
  };

  filterByProject = value => {
    const { startDate, endDate } = this.state;
    this.setState(
      {
        isLoading: true,
        projectId: value,
      },
      this.getUserLogs(value, startDate, endDate),
    );
  };

  isActionPerformedForLog = log =>
    !this.state.reviewDatePassed &&
    (log.logStatus === 1 || log.logStatus === 2);

  isShowRevokeToLog = log => {
    if (
      (this.isRoleHR() || this.state.showRevokeAction) &&
      this.isActionPerformedForLog(log)
    ) {
      return true;
    }
    return false;
  };

  handlePrevClick = () => {
    const { projectId, startDate, endDate } = this.state;
    this.setState(
      prevState => ({
        startDate: moment(prevState.startDate).subtract(1, 'week'),
        endDate: moment(prevState.endDate).subtract(1, 'week'),
        isLoading: true,
      }),
      this.getUserLogs(
        projectId,
        moment(startDate).subtract(1, 'week'),
        moment(endDate).subtract(1, 'week'),
      ),
    );
  };

  handleNextClick = () => {
    const { projectId, startDate, endDate } = this.state;
    this.setState(
      prevState => ({
        startDate: moment(prevState.startDate).add(1, 'week'),
        endDate: moment(prevState.endDate).add(1, 'week'),
        isLoading: true,
      }),
      this.getUserLogs(
        projectId,
        moment(startDate).add(1, 'week'),
        moment(endDate).add(1, 'week'),
      ),
    );
  };

  prepareHeader = () => {
    const weekDays = this.getWeekDates();
    const header = [
      {
        heading: 'Name',
      },
    ];
    weekDays.forEach(day => {
      header.push({
        heading: day.day,
        date: day.dispDate,
      });
    });
    return header;
  };

  legendRenderer = () => (
    <LegendRow>
      {LEGENDS.map(legend => (
        <LegendItem className="m-2" key={legend.label}>
          <StyledLegendColor color={legend.value} />
          <span>{legend.label}</span>
        </LegendItem>
      ))}
    </LegendRow>
  );

  render() {
    const {
      name,
      showNotification,
      notificationMessage,
      startDate,
      endDate,
      currentDate,
      isLoading,
      projectList,
      projectId,
      logsList,
      anyUpdating,
      reviewDatePassed,
      isAnyPendingForTeam,
    } = this.state;
    if (!currentDate) {
      return <LoadingIndicator />;
    }

    const dispStartDate = moment(startDate).format('MMM DD');
    const dispEndDate = moment(endDate).format('MMM DD');

    const isDisableNextButton =
      moment(currentDate)
        .startOf('week')
        .format('MMM DD YYYY') === moment(startDate).format('MMM DD YYYY');

    const isDisablePrevButton =
      moment('2024-11-01')
        .startOf('week')
        .format('MMM DD YYYY') === moment(startDate).format('MMM DD YYYY');

    const tabaleHeader = this.prepareHeader();

    return (
      <StyledReviewLogsContainer>
        <Helmet>
          <title>Review Logs</title>
          <meta name="description" content="Review Logs" />
        </Helmet>
        {showNotification && notificationMessage && (
          <Alert message={notificationMessage} type="warning" showIcon />
        )}
        <PageHeaderWrapper
          title={
            <Space>
              <span>
                Review Logs
                {name && <span> for {name}</span>}
              </span>
            </Space>
          }
          className="pageHeader"
          extra={[
            <Row gutter={5} key="1">
              <Col className="actionButtons">
                <Button
                  title="Prev"
                  disabled={isDisablePrevButton || isLoading}
                  onClick={() => this.handlePrevClick()}
                >
                  <LeftOutlined />
                </Button>
                {dispStartDate} - {dispEndDate}
                <Button
                  title="Next"
                  disabled={isDisableNextButton || isLoading}
                  onClick={() => this.handleNextClick()}
                >
                  <RightOutlined />
                </Button>
              </Col>
              <Col>
                {projectId && (
                  <Select
                    key="1"
                    style={{ width: '150px' }}
                    placeholder="Select Project"
                    options={projectList}
                    onChange={this.filterByProject}
                    data-testid="select-project"
                    showSearch
                    disabled={isLoading}
                    filterOption={(input, option) =>
                      option &&
                      option.label.toLowerCase().indexOf(input.toLowerCase()) >=
                        0
                    }
                    defaultValue={projectId}
                    value={projectId}
                  />
                )}
              </Col>
              <Col>
                <Button
                  title="Reject All"
                  className="rejectAction"
                  disabled={
                    isLoading ||
                    anyUpdating ||
                    !isAnyPendingForTeam ||
                    reviewDatePassed
                  }
                  onClick={() => this.changeLogStatusForTeam(2)}
                >
                  Reject All
                </Button>
              </Col>
              <Col>
                <Button
                  title="Approve All"
                  className="approveAction"
                  disabled={
                    isLoading ||
                    anyUpdating ||
                    !isAnyPendingForTeam ||
                    reviewDatePassed
                  }
                  onClick={() => this.changeLogStatusForTeam(1)}
                >
                  Approve All
                </Button>
              </Col>
            </Row>,
          ]}
        >
          {logsList.length > 0 && (
            <div className="u-w-100 logTable">
              <div className="reviewLogsHeader">
                {tabaleHeader.map(header => (
                  <div key={header.heading} className="headerCell">
                    {header.heading}
                    <br />
                    {header.date && header.date}
                  </div>
                ))}
              </div>
              {logsList.map(logData => (
                <div key={logData.id} className="reviewLogsRow">
                  <div className="logCell">
                    <div className="memberName">{logData.name}</div>
                    {!reviewDatePassed && logData.isAnyPendingForWeek && (
                      <div className="logActionButtons">
                        <Text
                          underline
                          className="rejectAction"
                          disabled={anyUpdating || isLoading}
                          onClick={() =>
                            this.changeLogStatusForWeek(logData.weeklyLogs, 2)
                          }
                        >
                          Reject
                        </Text>
                        <Text
                          underline
                          className="approveAction"
                          disabled={anyUpdating || isLoading}
                          onClick={() =>
                            this.changeLogStatusForWeek(logData.weeklyLogs, 1)
                          }
                        >
                          Approve
                        </Text>
                      </div>
                    )}
                  </div>
                  {logData.weeklyLogs.map(week => (
                    <div
                      key={week.date}
                      className={`logCell ${week.holiday ? 'isHoliday' : ''}`}
                    >
                      <StyledLogCellContainer>
                        {week.logs.map(log => (
                          <li key={log._id}>
                            {(log.isUpdating || isLoading) && (
                              <Skeleton style={{ height: '20px' }} />
                            )}
                            {!log.isUpdating && !isLoading && (
                              <LogTag
                                log={log}
                                reviewDatePassed={reviewDatePassed}
                                isShowRevokeToLog={this.isShowRevokeToLog(log)}
                                changeLogStatus={this.changeLogStatus}
                                handleLogClick={this.handleLogClick}
                              />
                            )}
                          </li>
                        ))}
                      </StyledLogCellContainer>
                      {!reviewDatePassed && week.isAnyPendingForDay && (
                        <div className="logActionButtons">
                          <Text
                            underline
                            className="rejectAction"
                            disabled={anyUpdating || isLoading}
                            onClick={() =>
                              this.changeLogStatusForDay(week.logs, 2)
                            }
                          >
                            Reject
                          </Text>
                          <Text
                            underline
                            className="approveAction"
                            disabled={anyUpdating || isLoading}
                            onClick={() =>
                              this.changeLogStatusForDay(week.logs, 1)
                            }
                          >
                            Approve
                          </Text>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
          {!isLoading && logsList.length === 0 && (
            <h3 className="u-text-center u-mt-5">No logs found</h3>
          )}
          {isLoading && logsList.length === 0 && <LoadingIndicator />}
        </PageHeaderWrapper>
        {this.confirmationModal()}
        {this.legendRenderer()}
      </StyledReviewLogsContainer>
    );
  }
}

ReviewLogs.propTypes = {
  history: PropTypes.object,
  projectId: PropTypes.string,
  period: PropTypes.array,
  location: PropTypes.object,
};

export default withRouter(ReviewLogs);
