/**

* MenteeRoadmapForm

* Form for mentees to view and select courses

*/

import React, { useState, useEffect } from 'react';

import PropTypes from 'prop-types';

import {
  Form,
  Input,
  Button,
  Card,
  Row,
  Col,
  Select,
  message,
  DatePicker,
} from 'antd';

import request from 'utils/request';

import { API_ENDPOINTS } from 'containers/constants';

import moment from 'moment';

const { Option } = Select;

const { TextArea } = Input;

const MenteeRoadmapForm = ({
  menteeId,
  mentorId,
  menteeRole,
  menteeName,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();

  const [loading, setLoading] = useState(false);

  const [courses, setCourses] = useState([]);

  const [selectedCourse, setSelectedCourse] = useState(null);

  // const [currentUser, setCurrentUser] = useState(null);

  // Fetch current user details to get the mentor ID if not provided

  // useEffect(() => {
  //   const fetchCurrentUser = async () => {
  //     try {
  //       const response = await request(API_ENDPOINTS.USER_DETAILS_API, {
  //         method: 'GET',
  //       });

  //       if (response && response.data) {
  //         setCurrentUser(response.data);
  //       }
  //     } catch (error) {
  //       message.error('Error fetching current user');
  //     }
  //   };

  //   fetchCurrentUser();
  // }, []);

  // Debug props

  useEffect(() => {
    if (menteeId === mentorId) {
      message.warning('Mentee ID and Mentor ID are the same!');
    }
  }, [menteeId, mentorId, menteeRole, menteeName]);

  // Fetch available courses that match the mentee's role

  useEffect(() => {
    const fetchCourses = async () => {
      try {
        setLoading(true);

        const response = await request(API_ENDPOINTS.TECH_ROADMAP_COURSE, {
          method: 'GET',
        });

        if (response && response.data) {
          let filteredCourses;

          if (menteeRole) {
            filteredCourses = response.data.filter(
              course =>
                course.role &&
                menteeRole &&
                course.role.toLowerCase() === menteeRole.toLowerCase(),
            );
          } else {
            filteredCourses = response.data;
          }

          setCourses(filteredCourses);
        }
      } catch (error) {
        message.error('Failed to fetch courses');
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [menteeRole]);

  // Handle course selection change

  const handleCourseChange = courseId => {
    const selected = courses.find(course => course._id === courseId);

    if (selected) {
      setSelectedCourse(selected);

      // Extract the duration number from the string (e.g., '3 Months' -> 3)
      const durationMatch = selected.duration?.match(/^(\d+)/);
      const durationMonths = durationMatch ? parseInt(durationMatch[1], 10) : 0;

      // Calculate target month based on course duration
      // Default to current month + duration months
      const targetMonth = moment().add(durationMonths, 'months');

      form.setFieldsValue({
        description: selected.description,
        duration: selected.duration,
        learningMedium: selected.learningMedium,
        targetMonth, // Set the target month automatically
      });
    }
  };

  const onFinish = async values => {
    try {
      setLoading(true);

      if (!selectedCourse) {
        message.error('Please select a course');

        return;
      }

      if (menteeId === mentorId) {
        message.error(
          'Mentee ID and Mentor ID cannot be the same. Please try again or contact support.',
        );

        return;
      }

      if (!mentorId) {
        message.error(
          'Mentor ID is missing. Please try again or contact support.',
        );

        return;
      }

      if (!menteeId) {
        message.error(
          'Mentee ID is missing. Please try again or contact support.',
        );

        return;
      }

      const data = {
        courseName: selectedCourse.courseName,

        description: selectedCourse.description,

        duration: selectedCourse.duration,

        learningMedium: selectedCourse.learningMedium,

        link: selectedCourse.link || '',

        dueDate: values.targetMonth
          ? values.targetMonth.format('YYYY-MM-DD')
          : null,

        menteeId,

        mentorId,

        completionStatus: 'Assigned',
      };

      const response = await request(API_ENDPOINTS.TECH_ROADMAP_ASSIGN, {
        method: 'POST',

        body: JSON.stringify(data),
      });

      if (response && response.status === 200) {
        message.success('Course selected successfully');

        form.resetFields();

        setSelectedCourse(null);

        // Call onSuccess callback if provided
        if (onSuccess && typeof onSuccess === 'function') {
          onSuccess();
        }
      }
    } catch (error) {
      message.error(
        error.response && error.response.message
          ? error.response.message
          : 'Failed to select course',
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Card
        title={`Assign a new course to ${menteeName || 'Mentee'}`}
        style={{ maxWidth: 800, margin: '0 auto' }}
      >
        <Form form={form} layout="vertical" onFinish={onFinish}>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="courseName"
                label="Course Name"
                rules={[
                  {
                    required: true,

                    message: 'Please select a course',
                  },
                ]}
              >
                <Select
                  placeholder="Select a course"
                  loading={loading}
                  onChange={handleCourseChange}
                >
                  {courses.map(course => (
                    <Option key={course._id} value={course._id}>
                      {course.courseName}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="description"
                label="Description"
                rules={[
                  {
                    required: true,

                    message: 'Please enter description',
                  },
                ]}
              >
                <TextArea
                  placeholder="Description"
                  rows={4}
                  disabled={selectedCourse}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="duration"
                label="Course duration"
                rules={[
                  {
                    required: true,

                    message: 'Please enter course duration',
                  },
                ]}
              >
                <Input
                  placeholder="Course duration"
                  defaultValue="3 Months"
                  disabled={selectedCourse}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="learningMedium"
                label="Learning Medium"
                rules={[
                  {
                    required: true,

                    message: 'Please enter learning medium',
                  },
                ]}
              >
                <Input
                  placeholder="Learning Medium"
                  disabled={selectedCourse}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="targetMonth"
                label="Target Month"
                rules={[
                  {
                    required: true,

                    message: 'Please select a target month',
                  },
                ]}
              >
                <DatePicker
                  picker="month"
                  style={{ width: '100%' }}
                  disabledDate={current => {
                    // Always disable past months
                    if (current && current < moment().startOf('month')) {
                      return true;
                    }

                    // If a course is selected, enforce minimum target month based on duration
                    if (selectedCourse) {
                      const durationMatch = selectedCourse.duration?.match(
                        /^(\d+)/,
                      );
                      const durationMonths = durationMatch
                        ? parseInt(durationMatch[1], 10)
                        : 0;
                      const minimumTargetMonth = moment()
                        .add(durationMonths, 'months')
                        .startOf('month');

                      // Disable dates less than minimum target month
                      return current && current < minimumTargetMonth;
                    }

                    return false;
                  }}
                  format="MMM YYYY"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'right' }}>
              <Button
                type="default"
                style={{
                  marginRight: 8,

                  color: '#fff',

                  borderColor: '#4d186e',

                  background: '#4d186e',

                  textShadow: '0 -1px 0 rgba(0, 0, 0, 0.12)',

                  boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)',

                  minWidth: '120px',

                  height: '40px',

                  borderRadius: '4px',

                  fontWeight: '500',
                }}
                onClick={onCancel}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                style={{
                  color: '#fff',

                  borderColor: '#4d186e',

                  background: '#4d186e',

                  textShadow: '0 -1px 0 rgba(0, 0, 0, 0.12)',

                  boxShadow: '0 2px 0 rgba(0, 0, 0, 0.045)',

                  minWidth: '120px',

                  height: '40px',

                  borderRadius: '4px',

                  fontWeight: '500',
                }}
              >
                Submit
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

MenteeRoadmapForm.propTypes = {
  menteeId: PropTypes.string.isRequired,

  mentorId: PropTypes.string.isRequired,

  menteeRole: PropTypes.string,

  menteeName: PropTypes.string,

  onSuccess: PropTypes.func,

  onCancel: PropTypes.func,
};

MenteeRoadmapForm.defaultProps = {
  menteeRole: '',

  menteeName: '',

  onSuccess: null,

  onCancel: null,
};

export default MenteeRoadmapForm;
