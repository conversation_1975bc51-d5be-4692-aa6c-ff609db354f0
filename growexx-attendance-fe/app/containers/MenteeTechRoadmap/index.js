/**
 * MenteeTechRoadmap Container
 * Displays and manages tech roadmap assignments for a mentee
 */
import React, { useState, useEffect } from 'react';
import { Tabs, Divider } from 'antd';
import TechRoadmapAssignments from 'components/TechRoadmapAssignments';
import { useParams } from 'react-router-dom';
import request from 'utils/request';
import { API_ENDPOINTS } from 'containers/constants';
import MenteeRoadmapForm from './MenteeRoadmapForm';

const { TabPane } = Tabs;

const MenteeTechRoadmap = () => {
  const { employeeId } = useParams();
  const [menteeDetails, setMenteeDetails] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMenteeDetails = async () => {
      try {
        setLoading(true);
        // Fetch mentee details using the employeeId from URL params
        const response = await request(
          `${API_ENDPOINTS.GET_MENTEE}/${employeeId}`,
          {
            method: 'GET',
          },
        );

        if (response && response.data) {
          // console.log('Mentee details fetched:', response.data);

          // Make sure we have the correct mentee data structure
          const menteeData = response.data;

          // Store the complete response data including menteeId and mentorId
          setMenteeDetails(menteeData);

          // Log the specific IDs for debugging
          // console.log('Mentee ID from API:', menteeData.menteeId);
          // console.log('Mentor ID from API:', menteeData.mentorId);
        }
      } catch (error) {
        // console.error('Failed to fetch mentee details:', error);
      } finally {
        setLoading(false);
      }
    };

    if (employeeId) {
      fetchMenteeDetails();
    }
  }, [employeeId]);

  if (loading || !menteeDetails) {
    return <div>Loading mentee details...</div>;
  }

  // Debug the mentee details
  // console.log('Rendering with mentee details:', menteeDetails);
  // console.log('menteeId:', menteeDetails.menteeId);
  // console.log('mentorId:', menteeDetails.mentorId);

  return (
    <div>
      <Tabs defaultActiveKey="techRoadmap">
        <TabPane tab="PLI Details" key="pliDetails">
          {/* Existing PLI details component would go here */}
        </TabPane>
        <TabPane tab="Tech Roadmap" key="techRoadmap">
          <div>
            <TechRoadmapAssignments
              menteeId={menteeDetails.menteeId}
              mentorId={menteeDetails.mentorId}
              menteeRole={menteeDetails.role}
              designation={menteeDetails.designation}
            />
            <Divider>Assign a new course</Divider>
            <MenteeRoadmapForm
              menteeId={menteeDetails.menteeId}
              mentorId={menteeDetails.mentorId}
              menteeRole={menteeDetails.designation}
              menteeName={menteeDetails.name}
            />
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
};

MenteeTechRoadmap.propTypes = {};

export default MenteeTechRoadmap;
