/**
 * PLISection/index.js
 *
 * This is the PLI Section Component File.
 */
import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import { Redirect } from 'react-router-dom';
import { ROLES, ROUTES } from '../constants';
import Unauthorized from '../UnauthorizedPage';

const PLISection = ({ user }) => {
  // Check if user has access to PLI section
  const hasPliAccess = () =>
    user &&
    (user.role === ROLES.HR ||
      user.role === ROLES.BU ||
      user.role === ROLES.PM ||
      user.role === ROLES.RM ||
      user.role === ROLES.ADMIN);

  if (!hasPliAccess()) {
    return <Unauthorized />;
  }

  // Redirect to PLI page
  return <Redirect to={ROUTES.PLI} />;
};

PLISection.propTypes = {
  user: PropTypes.shape({
    role: PropTypes.string,
  }),
};

const mapStateToProps = state => ({
  user: state.global.user,
});

export default connect(mapStateToProps)(PLISection);
