/*
 *
 * UserListing reducer
 *
 */
import produce from 'immer';
import { UPDATE_FIELD } from './constants';

export const initialState = {
  // Project Name
  firstName: '',
  // Jira URL
  lastName: '',
  // Email
  email: '',
  // Date Of Joining
  dateOfJoining: '',
  // Password
  password: '',
  // EmployeeId
  employeeId: '',
  // Label
  label: [],

  // Add Leave
  leaveType: '',
  leaveRange: [],
  fromLeaveDuration: '',
  toLeaveDuration: '',

  // Designation
  designation: '',
  level: '',
  businessUnit: '',
};

/* eslint-disable default-case, no-param-reassign */
const UserListingReducer = (state = initialState, action) =>
  produce(state, draft => {
    switch (action.type) {
      case UPDATE_FIELD:
        draft[action.key] = action.payload;
        break;
    }
  });

export default UserListingReducer;
