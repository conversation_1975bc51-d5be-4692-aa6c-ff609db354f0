/* eslint-disable jsx-a11y/label-has-associated-control */
import React, { PureComponent } from 'react';
import { withRouter } from 'react-router-dom';
import { compose } from 'redux';
import { Field, reduxForm } from 'redux-form';
import { Modal, Form, notification, Select, Upload, Button } from 'antd';
import { FormattedMessage } from 'react-intl';
import PropTypes from 'prop-types';
import { ARangePicker } from 'utils/Fields';
import { get } from 'lodash';
import request from 'utils/request';
import useInjectReducer from 'utils/injectReducer';
import AsyncSelect from 'react-select/async';
import { UploadOutlined } from '@ant-design/icons';
import reducer from './reducer';

import messages from './messages';
import {
  USER_LIST_FORM_KEY,
  REPORT_TYPE,
  DropDownStyle,
  ACCEPTED_FILE_TYPES,
} from './constants';
import {
  DEFAULT_LIMIT,
  API_ENDPOINTS,
  DEFAULT_PAGE,
  GENERIC_MOMENT_DATE_FORMAT,
} from '../constants';

const { Option } = Select;

const DOWNLOAD_FORM_OPTIONS = {
  labelCol: { span: 8 },
  wrapperCol: { span: 16 },
};
class DownloadLogs extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      reportType: REPORT_TYPE.ATTENDANCE.value,
      userId: '',
      selectedProjects: [],
      projects: [],
      fileUploading: false,
      showDownloadModal: false,
      downloadLoading: false,
    };
  }

  /**
   * getReportTypeLabel
   * @param {*} reportType
   * @returns report type selected for file name
   */
  getReportTypeLabel = reportType => {
    const reportTypes = Object.keys(REPORT_TYPE);
    const matchingReport = reportTypes.find(
      key => REPORT_TYPE[key].value === reportType,
    );
    return REPORT_TYPE[matchingReport].label;
  };

  downloadLogs = () => {
    const {
      stateValues: { logRange },
      updateState,
    } = this.props;
    const { reportType, userId, selectedProjects } = this.state;
    const startDate = logRange[0].format(GENERIC_MOMENT_DATE_FORMAT);
    const endDate = logRange[1].format(GENERIC_MOMENT_DATE_FORMAT);
    updateState({
      downloadLoading: true,
    });
    let apiEndPoint;
    if (reportType === REPORT_TYPE.ATTENDANCE.value) {
      apiEndPoint = API_ENDPOINTS.LOGS_DOWNLOAD;
    } else if (reportType === REPORT_TYPE.PERSON_DAY.value) {
      apiEndPoint = API_ENDPOINTS.PERSON_DAT_LOGS_DOWNLOAD;
    } else if (reportType === REPORT_TYPE.ATTENDANCE_LOGS.value) {
      apiEndPoint = API_ENDPOINTS.ATTENDANCE_LOGS_DOWNLOAD;
    } else if (reportType === REPORT_TYPE.BILLING_SHEET.value) {
      apiEndPoint = API_ENDPOINTS.BILLING_SHEET_DOWNLOAD;
    } else {
      apiEndPoint = API_ENDPOINTS.PROJECT_DOWNLOAD;
    }

    let requestUrl = `${apiEndPoint}?startDate=${startDate}&endDate=${endDate}`;

    if (userId) {
      requestUrl += `&userId=${userId}`;
    }

    if (selectedProjects.length) {
      requestUrl += `&projectId=${selectedProjects.toString()}`;
    }

    request(requestUrl, {
      method: 'GET',
      blob: true,
    })
      .then(res => {
        const url = URL.createObjectURL(res);
        const a = document.createElement('a');
        a.href = url;
        a.style.display = 'none';
        a.download = `${this.getReportTypeLabel(
          reportType,
        )}-(${startDate} to ${endDate}).csv`;
        a.target = '_blank';
        document.body.append(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
        notification.success({
          message: <FormattedMessage {...messages.downloadSuccess} />,
          description: res.message,
        });
        updateState({
          showDownloadModal: false,
          downloadLoading: false,
        });
      })
      .catch(error => {
        notification.error({
          message: <FormattedMessage {...messages.failedToLoad} />,
          description: error.message,
        });
        updateState({
          downloadLoading: false,
        });
      });
  };

  onReportTypeChange = reportType => {
    this.setState({ reportType, selectedProjects: [] });
  };

  onLoadUser = (search, callback) => {
    // eslint-disable-next-line no-new
    new Promise(resolve => {
      const data = { method: 'GET' };
      const requestURL = `${
        API_ENDPOINTS.USERS
      }?pageSize=${DEFAULT_LIMIT}&skip=${DEFAULT_PAGE}&name=${search}`;
      request(requestURL, data).then(response => {
        const userList = get(response, 'data.docs', []).map(user => ({
          // eslint-disable-next-line no-underscore-dangle
          value: user._id,
          label: `${user.firstName} ${user.lastName}`,
        }));
        callback(userList);
        resolve(userList);
      });
    });
  };

  getProjectsList = () => {
    // eslint-disable-next-line no-new
    new Promise(resolve => {
      const requestData = { method: 'GET' };
      const requestURL = `${API_ENDPOINTS.ACTIVE_PROJECT_LIST}`;
      request(requestURL, requestData).then(response => {
        const { data } = response || {};
        this.setState({ projects: data });
        resolve(response);
      });
    });
  };

  componentDidMount = () => {
    this.getProjectsList();
  };

  onEmployeeChange = user => {
    this.setState({
      // eslint-disable-next-line no-underscore-dangle
      userId: user ? user.value : '',
    });
  };

  onProjectChange = selectedProjects => {
    this.setState({
      selectedProjects,
    });
  };

  downloadAllMentees = () => {
    const { updateState } = this.props;
    updateState({ downloadLoading: true });

    // Use the correct API endpoint for all mentees
    const URL = API_ENDPOINTS.ALL_MENTEES_DOWNLOAD; // Define this in your constants

    request(URL, {
      method: 'GET',
      blob: true,
    })
      .then(res => {
        const url = URL.createObjectURL(res);
        const a = document.createElement('a');
        a.href = url;
        a.style.display = 'none';
        a.download = `all-mentees.csv`;
        a.target = '_blank';
        document.body.append(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
        notification.success({
          message: 'Downloaded successfully',
        });
        updateState({
          showDownloadModal: false,
          downloadLoading: false,
        });
      })
      .catch(error => {
        notification.error({
          message: 'Failed to download',
          description: error.message,
        });
        updateState({
          downloadLoading: false,
        });
      });
  };

  toggleDownloadModal = () => {
    this.setState(prevState => ({
      showDownloadModal: !prevState.showDownloadModal,
    }));
  };

  handleDownloadAllMentees = async () => {
    this.setState({ downloadLoading: true });
    try {
      // Adjust the endpoint to fetch ALL mentees (no pagination)
      const response = await request(`${API_ENDPOINTS.USER_LIST}?all=true`, {
        method: 'GET',
      });
      const mentees = response.data.docs || [];

      // Prepare CSV
      const csvRows = [
        ['Employee Id', 'Name', 'Email', 'Label'], // header
        ...mentees.map(m => [
          m.employeeId,
          `${m.firstName} ${m.lastName}`,
          m.email,
          Array.isArray(m.label) ? m.label.join(', ') : m.label,
        ]),
      ];
      const csvContent = csvRows
        .map(row =>
          row
            .map(String)
            .map(v => `"${v.replace(/"/g, '""')}"`)
            .join(','),
        )
        .join('\n');

      // Download
      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'mentees.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);

      this.setState({ downloadLoading: false, showDownloadModal: false });
    } catch (error) {
      notification.error({
        message: 'Download failed',
        description: error.message,
      });
      this.setState({ downloadLoading: false });
    }
  };

  render() {
    const layout = {
      labelCol: {
        span: 7,
        xs: 24,
        sm: 7,
      },
      wrapperCol: {
        span: 14,
        xs: 24,
        sm: 14,
      },
    };
    const {
      stateValues: { isLoading, showDownloadModal, logRange, downloadLoading },
      updateState,
      toggleModal,
    } = this.props;
    const { reportType, projects } = this.state;
    const { fileUploading } = this.state;
    const that = this;
    const TEST_IDS = '';
    const props = {
      name: 'file',
      customRequest: async options => {
        that.setState({
          fileUploading: true,
        });
        const fm = new FormData();
        fm.append('file', options.file);
        const payload = {
          method: 'POST',
          body: fm,
          headers: {},
        };
        request(API_ENDPOINTS.PROJECT_HEADER_RESET, payload)
          .then(() => {
            that.setState({
              fileUploading: false,
            });
            notification.success({
              message: <FormattedMessage {...messages.uploadLeaveSuccess} />,
            });
          })
          .catch(() => {
            that.setState({
              fileUploading: false,
            });
            notification.error({
              message: (
                <FormattedMessage {...messages.uploadProjectHeaderFailure} />
              ),
            });
          });
      },
      accept: `${ACCEPTED_FILE_TYPES.map(fileType => fileType)}`,
      maxCount: '1',
      itemRender: () => <></>,
      disabled: fileUploading,
    };
    return (
      <Modal
        title="Download logs"
        visible={showDownloadModal}
        onOk={this.downloadLogs}
        confirmLoading={isLoading}
        onCancel={toggleModal}
        okButtonProps={{
          disabled: !logRange,
          loading: downloadLoading,
          'data-testid': TEST_IDS.DOWNLOAD_BTN,
        }}
        okText={<FormattedMessage {...messages.downloadBtn} />}
        cancelButtonProps={{
          'data-testid': TEST_IDS.DOWNLOAD_LOGS_CANCEL_BTN,
        }}
      >
        <Button
          type="primary"
          onClick={this.downloadAllMentees}
          loading={this.props.stateValues.downloadLoading}
          style={{ marginBottom: 16 }}
        >
          Download All Mentees
        </Button>
        <Form
          onSubmit={this.downloadLogs}
          {...DOWNLOAD_FORM_OPTIONS}
          {...layout}
        >
          <Form.Item label="Report Type" required>
            <Select
              defaultValue="attendance"
              onChange={this.onReportTypeChange}
            >
              {Object.keys(REPORT_TYPE).map(key => (
                <Option value={REPORT_TYPE[key].value}>
                  {REPORT_TYPE[key].label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Field
            label="Period"
            name="period"
            component={ARangePicker}
            placeholder={['From', 'To']}
            hasFeedback
            required
            value={logRange}
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateState({ logRange: e })}
          />
          <Form.Item label="Employee" required>
            <AsyncSelect
              instanceId="employee"
              className="employee-container"
              classNamePrefix="employee-select"
              defaultOptions
              isSearchable
              isClearable
              styles={DropDownStyle}
              placeholder="Search Employee"
              loadOptions={this.onLoadUser}
              onChange={this.onEmployeeChange}
            />
          </Form.Item>
          {(reportType === REPORT_TYPE.PROJECT_WISE_PERSON_HOURS.value ||
            reportType === REPORT_TYPE.ATTENDANCE_LOGS.value ||
            reportType === REPORT_TYPE.BILLING_SHEET.value) && (
            <Form.Item label="Projects">
              <Select
                className="employeeProjects"
                mode="multiple"
                placeholder="Select Projects"
                onChange={this.onProjectChange}
              >
                {projects.map(projectName => (
                  <Option value={projectName}>{projectName}</Option>
                ))}
              </Select>
            </Form.Item>
          )}
          {reportType === REPORT_TYPE.PROJECT_WISE_PERSON_HOURS.value && (
            <Form.Item label="Project Headers">
              <Upload
                {...props}
                data-testid={TEST_IDS.UPLOAD_ATTACHMENT_PROJECT_HEADERS}
              >
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  loading={fileUploading}
                  data-testid={TEST_IDS.UPLOAD_PROJECT_HEADERS}
                >
                  Upload New Project Headers
                </Button>
              </Upload>
            </Form.Item>
          )}
        </Form>
        {this.state.showDownloadModal && (
          <Modal
            title="Download All Mentees"
            visible={this.state.showDownloadModal}
            onCancel={this.toggleDownloadModal}
            footer={[
              <Button key="cancel" onClick={this.toggleDownloadModal}>
                Cancel
              </Button>,
              <Button
                key="download"
                type="primary"
                loading={this.state.downloadLoading}
                onClick={this.handleDownloadAllMentees}
              >
                Download
              </Button>,
            ]}
          >
            <p>
              This will download all mentee names and information as a CSV file.
            </p>
          </Modal>
        )}
      </Modal>
    );
  }
}

DownloadLogs.propTypes = {
  // props from parent
  stateValues: PropTypes.object,
  updateState: PropTypes.func,
  toggleModal: PropTypes.func,
};

const withReducer = useInjectReducer({
  key: USER_LIST_FORM_KEY,
  reducer,
});

export default compose(
  withRouter,
  withReducer,
  reduxForm({
    form: USER_LIST_FORM_KEY,
    fields: ['period'],
  }),
)(DownloadLogs);
