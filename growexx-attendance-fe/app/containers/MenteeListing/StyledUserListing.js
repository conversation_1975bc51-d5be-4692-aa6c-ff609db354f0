import styled from 'styled-components';

export const StyledLabelContainer = styled.div`
  .labelContainer {
    @media (min-width: @screen-sm-min) {
      margin-left: 59px;
      label {
        min-width: 69px;
        max-width: 69px;
      }
      .ant-space-align-center {
        align-items: flex-start;
      }
      .ant-col-sm-14 {
        max-width: 100%;
      }
    }
  }
  .addRemoveLabelButton {
    border: none;
    outline: none;
    background: transparent;
    box-shadow: none;
    transition: none;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .labelErrorMessage {
    color: #ff4d4f;
    text-align: center;
    font-size: 14px;
    margin: @margin-sm 0;
  }
  .labelBelowContainer {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    margin-bottom: @margin-sm;
  }
`;

export const StyledTableContainer = styled.div`
  tr {
    td {
      cursor: pointer;
      & :last-child {
        cursor: auto;
      }
    }
  }
  .ant-table-tbody > tr.ant-table-row:hover > td {
    color: #350e4e;
  }
`;
