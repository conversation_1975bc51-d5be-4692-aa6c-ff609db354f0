import { createSelector } from 'reselect';
import { initialState } from './reducer';

/**
 * Direct selector to the userListing state domain
 */

const selectUserListingDomain = state => state.userListing || initialState;

/**
 * Default selector used by UserListing
 */

const makeSelectUserListing = () =>
  createSelector(
    selectUserListingDomain,
    substate => substate,
  );

export default makeSelectUserListing;
export { selectUserListingDomain };
