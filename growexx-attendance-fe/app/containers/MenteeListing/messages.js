/*
 * MenteeListing Messages
 *
 * This contains all the text for the MenteeListing container.
 */

import { defineMessages } from 'react-intl';

export const scope = 'app.containers.MenteeListing';

export default defineMessages({
  header: {
    id: `${scope}.header`,
    defaultMessage: 'Mentee Listing',
  },
  failedToLoad: {
    id: `${scope}.failedToLoad`,
    defaultMessage: 'Failed to load mentees',
  },
  addMentee: {
    id: `${scope}.addMentee`,
    defaultMessage: 'Add Mentee',
  },
  assignMentor: {
    id: `${scope}.assignMentor`,
    defaultMessage: 'Assign Mentor',
  },
  assignMentorInfo: {
    id: `${scope}.assignMentorInfo`,
    defaultMessage: 'Assign a mentor to this mentee',
  },
  assignMentorModalContent: {
    id: `${scope}.assignMentorModalContent`,
    defaultMessage: 'Are you sure you want to assign a mentor to this mentee?',
  },
  noData: {
    id: `${scope}.noData`,
    defaultMessage: 'No Data',
  },
  pliRating: {
    id: `${scope}.pliRating`,
    defaultMessage: 'PLI Rating',
  },
  noMenteesFound: {
    id: `${scope}.noMenteesFound`,
    defaultMessage: 'No mentees found matching the current filter criteria.',
  },
  myMentees: {
    id: `${scope}.myMentees`,
    defaultMessage: 'My Mentees',
  },
  myTechRoadmap: {
    id: `${scope}.myTechRoadmap`,
    defaultMessage: 'My Tech Roadmap',
  },
});
