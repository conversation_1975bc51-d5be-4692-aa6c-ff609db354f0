import React, { PureComponent } from 'react';
import { with<PERSON>out<PERSON> } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Field, reduxForm } from 'redux-form';
import { createStructuredSelector } from 'reselect';
import { Modal, Form, notification, Button } from 'antd';
import PropTypes from 'prop-types';
import request from 'utils/request';
import useInjectReducer from 'utils/injectReducer';
import { AInput } from 'utils/Fields';
import makeSelectMenteeList from './selectors';
import reducer from './reducer';
import * as actions from './actions';
import { MENTEE_LIST_FORM_KEY } from './constants';
import { API_ENDPOINTS } from '../constants';

class EditMentee extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {};
  }

  updateMenteeHandler = async () => {
    const {
      menteeStoreData,
      reset,
      stateValues: { showMenteeFormModal, menteeId },
      updateState,
      refreshMenteeList,
    } = this.props;
    updateState({
      isLoading: true,
    });
    const isUpdate = !!menteeId;
    const body = {
      ...menteeStoreData,
      id: menteeId,
    };

    const payload = {
      method: isUpdate ? 'PUT' : 'POST',
      body,
    };

    // Replace with your actual API endpoint for mentees
    const URL = API_ENDPOINTS.UPDATE_USER;
    try {
      const res = await request(URL, payload);
      updateState({
        isLoading: false,
        showMenteeFormModal: !showMenteeFormModal,
        menteeId: '',
      });
      refreshMenteeList();

      notification.success({
        description: res.message || 'Mentee saved successfully',
      });
      reset();
    } catch (error) {
      if (error.response) {
        error.response.json().then(err =>
          notification.error({
            description: err.message,
          }),
        );
      } else {
        notification.error({
          description: error.message,
        });
      }
      updateState({
        isLoading: false,
      });
    }
  };

  render() {
    const {
      pristine,
      submitting,
      invalid,
      stateValues: { isLoading, showMenteeFormModal, menteeId },
      toggleModal,
    } = this.props;
    const isUpdate = !!menteeId;

    return (
      <Modal
        title={isUpdate ? 'Edit Mentee' : 'Add Mentee'}
        visible={showMenteeFormModal}
        onCancel={toggleModal}
        footer={[
          <Button key="back" onClick={toggleModal}>
            Cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={isLoading}
            onClick={this.updateMenteeHandler}
            disabled={pristine || submitting || invalid}
          >
            {isUpdate ? 'Update' : 'Save'}
          </Button>,
        ]}
      >
        <Form layout="vertical">
          <Field
            label="First Name"
            name="firstName"
            component={AInput}
            placeholder="First Name"
            required
            hasFeedback
          />
          <Field
            label="Last Name"
            name="lastName"
            component={AInput}
            placeholder="Last Name"
            required
            hasFeedback
          />
          <Field
            label="Label"
            name="label"
            component={AInput}
            placeholder="Label"
            required
            hasFeedback
          />
        </Form>
      </Modal>
    );
  }
}

EditMentee.propTypes = {
  // Redux-form
  reset: PropTypes.func.isRequired,
  pristine: PropTypes.bool,
  submitting: PropTypes.bool,
  invalid: PropTypes.bool,

  // State
  stateValues: PropTypes.object.isRequired,
  updateState: PropTypes.func.isRequired,
  toggleModal: PropTypes.func.isRequired,
  refreshMenteeList: PropTypes.func.isRequired,

  // Store
  menteeStoreData: PropTypes.object,
};

const withReducer = useInjectReducer({
  key: MENTEE_LIST_FORM_KEY,
  reducer,
});

const mapStateToProps = state => {
  const getStateValues = createStructuredSelector({
    menteeStoreData: makeSelectMenteeList(),
  });
  return {
    ...getStateValues(state),
  };
};

export const mapDispatchToProps = dispatch => ({
  updateField: (key, value) => dispatch(actions.updateField(key, value)),
});

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withRouter,
  withReducer,
  withConnect,
  reduxForm({
    form: MENTEE_LIST_FORM_KEY,
  }),
)(EditMentee);
