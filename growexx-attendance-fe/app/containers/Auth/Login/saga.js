/**
 * Logs the user in the app
 */

import { call, put, select, takeLatest } from 'redux-saga/effects';
import sha256 from 'sha256';
import { push } from 'connected-react-router';
import request from 'utils/request';
import {
  makeSelectEmail,
  makeSelectPassword,
  makeSelectRedirectPath,
} from 'containers/Auth/Login/selectors';
import Emitter from 'utils/events';
import { API_ENDPOINTS, ROLE_BASED_DEFAULT_ROUTE } from 'containers/constants';
import { LOGIN, LOGIN_WITH_TOKEN } from './constants';
import { changeLoading, logInError, logInSuccess, resetState } from './actions';
import StorageService from '../../../utils/StorageService';
import {
  TOKEN_KEY,
  EMITTER_EVENTS,
  USER_DATA_KEY,
} from '../../../utils/constants';

/**
 * user login request/response handler
 */
export function* getSignIn() {
  const emailId = yield select(makeSelectEmail());
  const passWord = yield select(makeSelectPassword());
  const redirectPath = yield select(makeSelectRedirectPath());

  const payload = {
    email: emailId,
    password: sha256(passWord),
  };
  const data = {
    method: 'POST',
    body: payload,
  };

  try {
    yield put(changeLoading(true));
    const log = yield call(request, API_ENDPOINTS.LOGIN, data);
    if (log.status === 1) {
      // Store user data first
      StorageService.set(TOKEN_KEY, log.data.token);
      StorageService.set(USER_DATA_KEY, log.data);
      yield put(logInSuccess(log.message));
      yield put(changeLoading(false));

      // Store the redirect path in a local variable before any state changes
      const targetPath =
        redirectPath || ROLE_BASED_DEFAULT_ROUTE[log.data.role];

      // Only reset state after capturing the redirect path
      yield put(resetState());

      // Log the redirect path for debugging
      console.log('Redirecting to:', targetPath);

      // Redirect to the target path
      yield put(push(targetPath));
      Emitter.emit(EMITTER_EVENTS.LOG_IN);
    } else {
      yield put(changeLoading(false));
      yield put(logInError(log.message || 'Login failed'));
    }
  } catch (err) {
    // Safely handle error object which might not have response.json()
    let errorMessage = 'An error occurred during login';

    try {
      // Try to get detailed error if available
      if (err.response && typeof err.response.json === 'function') {
        const errorData = yield err.response.json();
        errorMessage = errorData.message || errorData;
      } else if (err.message) {
        errorMessage = err.message;
      }
    } catch (jsonError) {
      // If JSON parsing fails, use generic error
      console.error('Error parsing error response:', jsonError);
    }

    yield put(changeLoading(false));
    yield put(logInError({ message: errorMessage }));
  }
}

/**
 * Handle login with token and redirect
 */
export function* loginWithToken(action) {
  const { token, redirectPath } = action;

  try {
    yield put(changeLoading(true));

    // Verify token with backend
    const data = {
      method: 'POST',
      body: { token },
    };

    const log = yield call(request, API_ENDPOINTS.VERIFY_TOKEN, data);

    if (log.status === 1) {
      yield put(logInSuccess(log.message));
      StorageService.set(TOKEN_KEY, token);
      StorageService.set(USER_DATA_KEY, log.data);
      yield put(changeLoading(false));
      yield put(resetState());

      // Redirect to specified path or default route
      if (redirectPath) {
        yield put(push(redirectPath));
      } else {
        yield put(push(ROLE_BASED_DEFAULT_ROUTE[log.data.role]));
      }

      Emitter.emit(EMITTER_EVENTS.LOG_IN);
    } else {
      yield put(resetState());
      yield put(changeLoading(false));
      yield put(logInError(true));
      // Preserve the redirect path when redirecting to login
      yield put(
        push(
          redirectPath
            ? `/login?redirect=${encodeURIComponent(redirectPath)}`
            : '/login',
        ),
      );
    }
  } catch (err) {
    yield put(changeLoading(false));
    yield put(logInError(err.message || 'Authentication failed'));
    // Preserve the redirect path when redirecting to login
    yield put(
      push(
        redirectPath
          ? `/login?redirect=${encodeURIComponent(redirectPath)}`
          : '/login',
      ),
    );
  }
}

/**
 * Root saga manages watcher lifecycle
 */
export default function* login() {
  // Watches for LOGIN actions and calls getSignIn when one comes in.
  // By using `takeLatest` only the result of the latest API call is applied.
  // It returns task descriptor (just like fork) so we can continue execution
  // It will be cancelled automatically on component unmount
  yield takeLatest(LOGIN, getSignIn);
  yield takeLatest(LOGIN_WITH_TOKEN, loginWithToken);
}
