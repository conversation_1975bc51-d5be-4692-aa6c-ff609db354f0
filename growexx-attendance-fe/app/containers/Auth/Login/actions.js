/*
 * Login Actions
 *
 *
 */

import {
  CHANGE_EMAIL,
  CHANGE_PASSWORD,
  ERROR,
  LOADING,
  LOGIN,
  RESET,
  SUCCESS,
  LOGIN_WITH_TOKEN,
  SET_REDIRECT_PATH,
} from './constants';

/**
 * Changes the email field
 *
 * @param  {string} email The new text of the input field
 *
 * @return {object} An action object with a type of CHANGE_EMAIL
 */
export function changeEmail(email) {
  return {
    type: CHANGE_EMAIL,
    email,
  };
}

/**
 * Changes the password field
 *
 * @param  {string} password The new text of the input field
 *
 * @return {object} An action object with a type of CHANGE_PASSWORD
 */
export function changePassword(password) {
  return {
    type: CHANGE_PASSWORD,
    password,
  };
}

/**
 * Changes the loading state
 *
 * @param  {boolean} loading loading state of login form
 *
 * @return {object} An action object with a type of LOADING
 */
export function changeLoading(loading) {
  return {
    type: LOADING,
    loading,
  };
}

/**
 * Sets error
 *
 * @param  {object} error The error occurred
 *
 * @return {object} An action object with a type of ERROR
 */
export function logInError(error) {
  return {
    type: ERROR,
    error,
  };
}

/**
 * Sets success
 *
 * @param  {string} message The success message
 *
 * @return {object} An action object with a type of SUCCESS
 */
export function logInSuccess(message) {
  return {
    type: SUCCESS,
    message,
  };
}

/**
 * Log in request
 *
 * @return {object} An action object with a type of LOGIN
 */
export function fireLogin() {
  return {
    type: LOGIN,
  };
}

/**
 * Reset login states
 *
 * @return {object} An action object with a type of RESET
 */
export function resetState() {
  return {
    type: RESET,
  };
}

/**
 * Sets the redirect path to use after successful login
 *
 * @param {string} path - Path to redirect after successful login
 * @return {object} An action object with a type of SET_REDIRECT_PATH
 */
export function setRedirectPath(path) {
  return {
    type: SET_REDIRECT_PATH,
    path,
  };
}

/**
 * Login with token and redirect
 *
 * @param {string} token - Authentication token
 * @param {string} redirectPath - Path to redirect after successful login
 * @return {object} An action object with a type of LOGIN_WITH_TOKEN
 */
export function loginWithToken(token, redirectPath) {
  return {
    type: LOGIN_WITH_TOKEN,
    token,
    redirectPath,
  };
}
