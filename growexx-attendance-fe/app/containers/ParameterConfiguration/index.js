import React, { Component } from 'react';
import { Button, message, Select, Typography } from 'antd';
import styled from 'styled-components';
import request from 'utils/request';
import ParameterCard from './ParameterCard';
import { PARAMETER_MAP, POSITIONS } from './parameterMappings';
import { API_ENDPOINTS, ROUTES } from '../constants';

const { Title } = Typography;

const HeaderContainer = styled.div`
  margin-bottom: 24px;
`;

const RoleSelectorContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
`;

const RoleLabel = styled.label`
  font-weight: 500;
  margin-right: 16px;
  min-width: 120px;
`;

const BottomBar = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-bottom: 24px;
`;

class ParameterConfigurationPage extends Component {
  constructor(props) {
    super(props);

    // Create initial cards with guaranteed non-empty parent parameters
    const initialCards = [
      {
        key: 'project1',
        title: 'Project',
        parentParameter: 'Project',
        parentWeightage: '',
        editableWeightage: true,
        editableParentName: true,
        projectType: 'Fixed',
        showProjectType: false,
        childParameters: [],
        childSumError: '',
      },
      {
        key: 'project2',
        title: 'Project',
        parentParameter: 'Project',
        parentWeightage: '',
        editableWeightage: true,
        editableParentName: true,
        projectType: 'Dedicated',
        showProjectType: false,
        childParameters: [],
        childSumError: '',
      },
      {
        key: 'techRoadmap',
        title: 'Tech Roadmap',
        parentParameter: 'Tech Roadmap',
        parentWeightage: 30,
        editableWeightage: true,
        editableParentName: false,
        projectType: 'Dedicated',
        showProjectType: false,
        childParameters: [],
        childSumError: '',
        quarter: '1',
      },
      {
        key: 'hr',
        title: 'HR',
        parentParameter: 'HR',
        parentWeightage: 10,
        editableWeightage: true,
        editableParentName: false,
        projectType: 'Dedicated',
        showProjectType: false,
        childParameters: [],
        childSumError: '',
        quarter: '1',
      },
    ];

    // Ensure all parent parameters are set
    this.ensureParentParameters(initialCards);

    this.state = {
      selectedRole: '',
      roles: POSITIONS, // Initialize with the roles from parameterMappings
      cards: initialCards,
      formError: '',
      isLoading: true,
    };
  }

  componentDidMount() {
    // Fetch existing parameter configurations
    request(API_ENDPOINTS.GET_PARAMETER_CONFIG, {
      method: 'GET',
    })
      .then(response => {
        // Handle different response formats (data field or direct array)
        let existingParams = [];
        // Start with the default roles from POSITIONS
        const roles = [...POSITIONS];

        if (Array.isArray(response)) {
          existingParams = response;
        } else if (response && Array.isArray(response.data)) {
          existingParams = response.data;
        }

        // Extract all unique roles from the response
        if (existingParams.length > 0) {
          // Check if we have the new schema format with roleParameters
          if (existingParams[0].roleParameters) {
            // New schema format
            existingParams.forEach(doc => {
              doc.roleParameters.forEach(roleParam => {
                if (!roles.includes(roleParam.applicableRole)) {
                  roles.push(roleParam.applicableRole);
                }
              });
            });

            // Don't select any role by default
            const selectedRole = '';

            // Find the document that has the selected role
            const roleDoc = existingParams.find(doc =>
              doc.roleParameters.some(rp => rp.applicableRole === selectedRole),
            );

            if (roleDoc && selectedRole) {
              // Find the role parameters for the selected role
              const roleParams = roleDoc.roleParameters.find(
                rp => rp.applicableRole === selectedRole,
              );

              if (roleParams) {
                this.setState(prevState => {
                  // Create a copy of the current cards
                  const updatedCards = [...prevState.cards];

                  // Process each card with its corresponding parameters
                  updatedCards.forEach((_, idx) => {
                    let paramToUse = null;

                    if (idx === 0) {
                      // For project1 (Fixed type)
                      paramToUse = roleParams.parameters.find(
                        param => param.projectType === 'Fixed',
                      );
                    } else if (idx === 1) {
                      // For project2 (Dedicated type)
                      paramToUse = roleParams.parameters.find(
                        param =>
                          param.projectType === 'Dedicated' &&
                          param.parentParameter !== 'Tech Roadmap' &&
                          param.parentParameter !== 'HR',
                      );
                    } else if (idx === 2) {
                      // For Tech Roadmap
                      paramToUse = roleParams.parameters.find(
                        param => param.parentParameter === 'Tech Roadmap',
                      );
                    } else if (idx === 3) {
                      // For HR
                      paramToUse = roleParams.parameters.find(
                        param => param.parentParameter === 'HR',
                      );
                    }

                    if (paramToUse) {
                      updatedCards[idx] = this.updateCardWithParams(
                        updatedCards[idx],
                        paramToUse,
                        idx,
                      );
                    }
                  });

                  return {
                    roles,
                    selectedRole,
                    cards: updatedCards,
                    isLoading: false,
                  };
                });
              } else {
                this.setState({ roles, selectedRole, isLoading: false });
              }
            } else {
              this.setState({ roles, isLoading: false });
            }
          } else {
            // Old schema format - extract roles from applicableRoles fields
            existingParams.forEach(param => {
              if (
                param.applicableRoles &&
                Array.isArray(param.applicableRoles)
              ) {
                param.applicableRoles.forEach(role => {
                  if (!roles.includes(role)) {
                    roles.push(role);
                  }
                });
              }
            });

            // Don't select any role by default
            const selectedRole = '';

            if (selectedRole) {
              // Filter parameters for the selected role
              const roleParams = existingParams.filter(
                param =>
                  param.applicableRoles &&
                  param.applicableRoles.includes(selectedRole),
              );

              this.setState(prevState => {
                // Create a copy of the current cards
                const updatedCards = [...prevState.cards];

                // Process each card with its corresponding parameters
                updatedCards.forEach((_, idx) => {
                  let paramToUse = null;

                  if (idx === 0) {
                    // For project1 (Fixed type)
                    paramToUse = roleParams.find(
                      param =>
                        param.projectType === 'Fixed' &&
                        param.parentParameter.startsWith('Project'),
                    );
                  } else if (idx === 1) {
                    // For project2 (Dedicated type)
                    paramToUse = roleParams.find(
                      param =>
                        param.projectType === 'Dedicated' &&
                        param.parentParameter.startsWith('Project'),
                    );
                  } else if (idx === 2) {
                    // For Tech Roadmap
                    paramToUse = roleParams.find(
                      param => param.parentParameter === 'Tech Roadmap',
                    );
                  } else if (idx === 3) {
                    // For HR
                    paramToUse = roleParams.find(
                      param => param.parentParameter === 'HR',
                    );
                  }

                  if (paramToUse) {
                    updatedCards[idx] = this.updateCardWithParams(
                      updatedCards[idx],
                      paramToUse,
                      idx,
                    );
                  }
                });

                return {
                  roles,
                  selectedRole,
                  cards: updatedCards,
                  isLoading: false,
                };
              });
            } else {
              this.setState({ roles, isLoading: false });
            }
          }
        } else {
          // If no parameters found, still use the default roles but don't select any by default
          const selectedRole = '';
          this.setState({
            roles,
            selectedRole,
            isLoading: false,
          });
        }
      })
      .catch(error => {
        // eslint-disable-next-line no-console
        console.error('Failed to load parameter configuration:', error);
        // If API call fails, still use the default roles but don't select any by default
        const selectedRole = '';
        this.setState({
          roles: POSITIONS,
          selectedRole,
          isLoading: false,
        });
      });
  }

  // Helper method to update parent weightage
  adjustWeightagesForParent = (cards, idx, value) => {
    // Create a deep copy of the cards array to avoid modifying the original
    const updatedCards = JSON.parse(JSON.stringify(cards));

    // Allow any positive value or empty string
    if (value === '' || Number(value) >= 0) {
      // Set the current project's weightage
      updatedCards[idx].parentWeightage = value;
    }
    // Invalid values are ignored

    return updatedCards;
  };

  handleParentWeightageChange = (idx, value) => {
    // Ensure value is numeric or empty
    if (value !== '' && !Number.isFinite(Number(value))) {
      return; // Ignore non-numeric input
    }

    this.setState(prevState => {
      const cards = [...prevState.cards];

      // Use our helper method to adjust weightages and get the updated cards
      const updatedCards = this.adjustWeightagesForParent(cards, idx, value);

      return { cards: updatedCards, formError: '' };
    });
  };

  handleRoleChange = value => {
    // eslint-disable-next-line no-console
    console.log(`Selected role: ${value}`);
    this.setState(
      {
        selectedRole: value,
        isLoading: true,
      },
      () => {
        // Fetch parameters for the selected role using the existing API endpoint
        request(`${API_ENDPOINTS.GET_PARAMETER_CONFIG}?role=${value}`, {
          method: 'GET',
        })
          .then(response => {
            // Handle different response formats (data field or direct array)
            let existingParams = [];

            // Handle response from the new API endpoint
            if (response && response.roleParameters) {
              // Direct response with roleParameters (new schema)
              const roleParams = response.roleParameters.find(
                rp => rp.applicableRole === value,
              );
              if (roleParams && roleParams.parameters) {
                existingParams = [{ roleParameters: [roleParams] }];
              }
            } else if (
              response &&
              response.data &&
              response.data.roleParameters
            ) {
              // Response with data field containing roleParameters (new schema)
              const roleParams = response.data.roleParameters.find(
                rp => rp.applicableRole === value,
              );
              if (roleParams && roleParams.parameters) {
                existingParams = [{ roleParameters: [roleParams] }];
              }
            } else if (Array.isArray(response)) {
              // Direct array response (old schema)
              existingParams = response;
            } else if (response && Array.isArray(response.data)) {
              // Response with data field containing array (old schema)
              existingParams = response.data;
            }

            // Check if we have the new schema format with roleParameters
            if (existingParams.length > 0 && existingParams[0].roleParameters) {
              // New schema format
              const roleDoc = existingParams.find(doc =>
                doc.roleParameters.some(rp => rp.applicableRole === value),
              );

              if (roleDoc) {
                const roleParams = roleDoc.roleParameters.find(
                  rp => rp.applicableRole === value,
                );

                if (roleParams) {
                  this.setState(prevState => {
                    // Create a copy of the current cards
                    const updatedCards = [...prevState.cards];

                    // Process each card with its corresponding parameters
                    updatedCards.forEach((_, idx) => {
                      let paramToUse = null;

                      if (idx === 0) {
                        // For project1 (Fixed type)
                        paramToUse = roleParams.parameters.find(
                          param => param.projectType === 'Fixed',
                        );
                      } else if (idx === 1) {
                        // For project2 (Dedicated type)
                        paramToUse = roleParams.parameters.find(
                          param =>
                            param.projectType === 'Dedicated' &&
                            param.parentParameter !== 'Tech Roadmap' &&
                            param.parentParameter !== 'HR',
                        );
                      } else if (idx === 2) {
                        // For Tech Roadmap
                        paramToUse = roleParams.parameters.find(
                          param => param.parentParameter === 'Tech Roadmap',
                        );
                      } else if (idx === 3) {
                        // For HR
                        paramToUse = roleParams.parameters.find(
                          param => param.parentParameter === 'HR',
                        );
                      }

                      if (paramToUse) {
                        updatedCards[idx] = this.updateCardWithParams(
                          updatedCards[idx],
                          paramToUse,
                          idx,
                        );
                      } else {
                        // Reset to default if no data
                        updatedCards[idx] = this.resetCardToDefault(
                          updatedCards[idx],
                          idx,
                        );
                      }
                    });

                    return {
                      cards: updatedCards,
                      isLoading: false,
                    };
                  });
                } else {
                  // No parameters for this role in the API, try to load from PARAMETER_MAP
                  this.loadParametersFromMap(value);
                }
              } else {
                // No document for this role in the API, try to load from PARAMETER_MAP
                this.loadParametersFromMap(value);
              }
            } else {
              // Old schema format
              const roleParams = existingParams.filter(
                param =>
                  param.applicableRoles &&
                  param.applicableRoles.includes(value),
              );

              if (roleParams.length > 0) {
                this.setState(prevState => {
                  // Create a copy of the current cards
                  const updatedCards = [...prevState.cards];

                  // Process each card with its corresponding parameters
                  updatedCards.forEach((_, idx) => {
                    let paramToUse = null;

                    if (idx === 0) {
                      // For project1 (Fixed type)
                      paramToUse = roleParams.find(
                        param =>
                          param.projectType === 'Fixed' &&
                          param.parentParameter.startsWith('Project'),
                      );
                    } else if (idx === 1) {
                      // For project2 (Dedicated type)
                      paramToUse = roleParams.find(
                        param =>
                          param.projectType === 'Dedicated' &&
                          param.parentParameter.startsWith('Project'),
                      );
                    } else if (idx === 2) {
                      // For Tech Roadmap
                      paramToUse = roleParams.find(
                        param => param.parentParameter === 'Tech Roadmap',
                      );
                    } else if (idx === 3) {
                      // For HR
                      paramToUse = roleParams.find(
                        param => param.parentParameter === 'HR',
                      );
                    }

                    if (paramToUse) {
                      updatedCards[idx] = this.updateCardWithParams(
                        updatedCards[idx],
                        paramToUse,
                        idx,
                      );
                    } else {
                      // Reset to default if no data
                      updatedCards[idx] = this.resetCardToDefault(
                        updatedCards[idx],
                        idx,
                      );
                    }
                  });

                  return {
                    cards: updatedCards,
                    isLoading: false,
                  };
                });
              } else {
                // No parameters for this role in the API, try to load from PARAMETER_MAP
                this.loadParametersFromMap(value);
              }
            }
          })
          .catch(error => {
            // eslint-disable-next-line no-console
            console.error('Failed to load parameter configuration:', error);
            // Try to load from PARAMETER_MAP if API call fails
            this.loadParametersFromMap(value);
          });
      },
    );
  };

  loadParametersFromMap = role => {
    this.setState(prevState => {
      const updatedCards = [...prevState.cards];

      // Process each card with its corresponding parameters
      updatedCards.forEach((_, idx) => {
        if (idx === 0) {
          // Load Fixed parameters for card 0
          const fixedParams = this.getParametersForRoleAndType(
            role,
            'Fixed',
            [],
            0,
          );
          if (fixedParams.length > 0) {
            updatedCards[0] = {
              ...updatedCards[0],
              parentParameter: 'Project',
              parentWeightage: 20,
              projectType: 'Fixed',
              childParameters: fixedParams,
            };
          } else {
            // Reset to default if no parameters found
            updatedCards[0] = this.resetCardToDefault(updatedCards[0], 0);
          }
        } else if (idx === 1) {
          // Load Dedicated parameters for card 1
          const dedicatedParams = this.getParametersForRoleAndType(
            role,
            'Dedicated',
            [],
            1,
          );
          if (dedicatedParams.length > 0) {
            updatedCards[1] = {
              ...updatedCards[1],
              parentParameter: 'Project',
              parentWeightage: 40,
              projectType: 'Dedicated',
              childParameters: dedicatedParams,
            };
          } else {
            // Reset to default if no parameters found
            updatedCards[1] = this.resetCardToDefault(updatedCards[1], 1);
          }
        } else {
          // Reset Tech Roadmap and HR cards to defaults
          updatedCards[idx] = this.resetCardToDefault(updatedCards[idx], idx);
        }
      });

      // Ensure all parent parameters are set
      this.ensureParentParameters(updatedCards);

      return {
        cards: updatedCards,
        isLoading: false,
      };
    });
  };

  resetCardsToDefault = () => {
    this.setState(prevState => {
      // Create default cards using our helper method
      const defaultCards = prevState.cards.map((card, idx) =>
        this.resetCardToDefault(card, idx),
      );

      // Ensure all parent parameters are set
      this.ensureParentParameters(defaultCards);

      return {
        cards: defaultCards,
        isLoading: false,
      };
    });
  };

  // Helper method to update child parameters based on project type and selected role
  updateChildParameters = (cards, idx, projectType) => {
    const params = [];
    const { selectedRole } = this.state;

    if (
      selectedRole &&
      PARAMETER_MAP[projectType] &&
      PARAMETER_MAP[projectType][selectedRole]
    ) {
      // If we have a selected role and parameters for that role, use them
      const paramList = PARAMETER_MAP[projectType][selectedRole] || [];
      paramList.forEach((name, index) => {
        params.push({
          id: `${name}-param-${idx}-${index}`,
          name,
          weightage: '',
          isManual: false,
        });
      });
    } else if (selectedRole) {
      // If we have a selected role but no specific parameters, check if it's PO/PM/TPM
      let mapRole = selectedRole;
      if (['PO', 'PM', 'TPM'].includes(selectedRole)) {
        mapRole = 'PO/PM/TPM';
        if (PARAMETER_MAP[projectType] && PARAMETER_MAP[projectType][mapRole]) {
          const paramList = PARAMETER_MAP[projectType][mapRole] || [];
          paramList.forEach((name, index) => {
            params.push({
              id: `${name}-param-${idx}-${index}`,
              name,
              weightage: '',
              isManual: false,
            });
          });
        }
      }
    } else {
      // If no role is selected, get all unique parameters for this project type
      const paramMap = PARAMETER_MAP[projectType] || {};
      let paramIndex = 0;

      // Collect all unique parameters across all roles
      Object.values(paramMap).forEach(paramList => {
        if (Array.isArray(paramList)) {
          paramList.forEach(name => {
            if (!params.find(p => p.name === name)) {
              params.push({
                id: `${name}-param-${idx}-${paramIndex}`,
                name,
                weightage: '',
                isManual: false,
              });
              paramIndex += 1;
            }
          });
        }
      });
    }

    // Update the child parameters for this card
    const updatedCards = cards;
    updatedCards[idx].childParameters = params;
  };

  // This method is no longer needed as "Applies to" is now at the global level
  // We'll keep a simplified version for backward compatibility
  handleAppliesToChange = () => {
    // No-op - this functionality is now handled at the global level
  };

  handleChildParamChange = (cardIdx, childIdx, field, value) => {
    this.setState(prevState => {
      const cards = [...prevState.cards];
      const param = cards[cardIdx].childParameters[childIdx];

      // Determine the new value based on field type
      let newValue = value;

      if (field === 'weightage') {
        // For weightage, ensure it's a valid integer, 0, or empty string
        if (value === '') {
          newValue = '';
        } else if (value === 0 || value === '0') {
          // Explicitly handle 0 as a valid value
          newValue = 0;
        } else {
          // Remove any non-digit characters
          const cleaned = value ? value.toString().replace(/[^\d]/g, '') : '';
          const numValue = parseInt(cleaned, 10);
          if (Number.isFinite(numValue)) {
            newValue = numValue;
          } else {
            // If not a valid number, don't update
            return { cards };
          }
        }
      } else if (field === 'name' && !param.isManual) {
        // Only allow name changes for manually added parameters
        return { cards };
      }

      // Create a new card with the updated parameter
      const updatedCards = [
        ...cards.slice(0, cardIdx),
        {
          ...cards[cardIdx],
          childParameters: [
            ...cards[cardIdx].childParameters.slice(0, childIdx),
            {
              ...cards[cardIdx].childParameters[childIdx],
              [field]: newValue,
            },
            ...cards[cardIdx].childParameters.slice(childIdx + 1),
          ],
        },
        ...cards.slice(cardIdx + 1),
      ];

      return { cards: updatedCards };
    });
  };

  // Get available parameters that aren't already used
  getAvailableParameters = cardIdx => {
    const { cards, selectedRole } = this.state;
    const card = cards[cardIdx];
    const { projectType } = card;
    const existingParamNames = card.childParameters.map(param => param.name);

    // Get all parameters from all roles and project types
    let availableParams = [];

    // First try to get role-specific parameters
    if (selectedRole && PARAMETER_MAP[projectType]) {
      // Check if we have parameters for this exact role
      if (PARAMETER_MAP[projectType][selectedRole]) {
        availableParams = [
          ...availableParams,
          ...PARAMETER_MAP[projectType][selectedRole],
        ];
      }
      // Check if it's a PO/PM/TPM role
      else if (
        ['PO', 'PM', 'TPM'].includes(selectedRole) &&
        PARAMETER_MAP[projectType]['PO/PM/TPM']
      ) {
        availableParams = [
          ...availableParams,
          ...PARAMETER_MAP[projectType]['PO/PM/TPM'],
        ];
      }
    }

    // Then add all other parameters from all roles for this project type
    if (PARAMETER_MAP[projectType]) {
      Object.entries(PARAMETER_MAP[projectType]).forEach(([role, params]) => {
        if (
          role !== selectedRole &&
          !(role === 'PO/PM/TPM' && ['PO', 'PM', 'TPM'].includes(selectedRole))
        ) {
          availableParams = [...availableParams, ...params];
        }
      });
    }

    // Remove duplicates using Set
    const uniqueParams = [...new Set(availableParams)];

    // Filter out parameters that are already used
    return uniqueParams.filter(param => !existingParamNames.includes(param));
  };

  handleAddChildParam = idx => {
    this.setState(prevState => {
      const cards = [...prevState.cards];

      // Create a new card with an empty parameter
      const updatedCards = [
        ...cards.slice(0, idx),
        {
          ...cards[idx],
          childParameters: [
            ...cards[idx].childParameters,
            {
              id: `manual-param-${idx}-${cards[idx].childParameters.length}`,
              name: '',
              weightage: '',
              isManual: true, // Mark as manually added (will be editable)
            },
          ],
        },
        ...cards.slice(idx + 1),
      ];

      return { cards: updatedCards };
    });
  };

  handleRemoveChildParam = (cardIdx, childIdx) => {
    this.setState(prevState => {
      const cards = [...prevState.cards];

      // Create a new card with the parameter removed
      const updatedCards = [
        ...cards.slice(0, cardIdx),
        {
          ...cards[cardIdx],
          childParameters: [
            ...cards[cardIdx].childParameters.slice(0, childIdx),
            ...cards[cardIdx].childParameters.slice(childIdx + 1),
          ],
        },
        ...cards.slice(cardIdx + 1),
      ];

      return { cards: updatedCards };
    });
  };

  validateChildSum = idx => {
    this.setState(prevState => {
      const cards = [...prevState.cards];
      const sum = cards[idx].childParameters.reduce(
        (acc, c) => acc + (Number(c.weightage) || 0),
        0,
      );

      // Determine the appropriate error message
      let childSumError = '';
      if (sum !== Number(cards[idx].parentWeightage)) {
        childSumError = `Total value of child parameters should equal ${
          cards[idx].parentWeightage
        }% (parent parameter)`;
      }

      // Create a new card with the updated error message
      const updatedCards = [
        ...cards.slice(0, idx),
        {
          ...cards[idx],
          childSumError,
        },
        ...cards.slice(idx + 1),
      ];

      return { cards: updatedCards };
    });
  };

  handleParentParameterChange = (idx, value) => {
    this.setState(prevState => {
      const cards = [...prevState.cards];

      // Create a new card with the updated parameter
      const updatedCards = [
        ...cards.slice(0, idx),
        {
          ...cards[idx],
          parentParameter: value,
        },
        ...cards.slice(idx + 1),
      ];

      return { cards: updatedCards };
    });
  };

  handleCancel = () => {
    // Go back or clear form as needed
    window.history.back();
  };

  handleSubmit = () => {
    const { cards, selectedRole } = this.state;
    let valid = true;

    // Check if a role is selected
    if (!selectedRole) {
      this.setState({
        formError: 'Please select a role to apply these parameters to',
      });
      return;
    }

    // Fix any empty parent parameters before validation
    const validatedCards = [...cards];
    // eslint-disable-next-line no-console
    console.log('Validating cards:', JSON.stringify(cards, null, 2));

    // First, ensure all parent parameters are set
    validatedCards.forEach((card, idx) => {
      // eslint-disable-next-line no-console
      console.log(`Checking card ${idx}:`, {
        parentParameter: card.parentParameter,
        parentWeightage: card.parentWeightage,
        title: card.title,
        key: card.key,
      });

      // Force the parent parameter to a non-empty value if it's empty
      if (!card.parentParameter || card.parentParameter.trim() === '') {
        // eslint-disable-next-line no-console
        console.warn(`Card ${idx} has empty parent parameter, fixing it`);

        // Fix it based on the card index
        if (idx === 0 || idx === 1) {
          validatedCards[idx].parentParameter = 'Project';
        } else if (idx === 2) {
          validatedCards[idx].parentParameter = 'Tech Roadmap';
        } else if (idx === 3) {
          validatedCards[idx].parentParameter = 'HR';
        } else {
          validatedCards[idx].parentParameter = card.title || 'Project';
        }

        // eslint-disable-next-line no-console
        console.log(
          `Card ${idx} parentParameter fixed to: "${
            validatedCards[idx].parentParameter
          }"`,
        );
      }
    });

    // Now validate the cards with the fixed parent parameters
    validatedCards.forEach((card, idx) => {
      // eslint-disable-next-line no-console
      console.log(
        `Validating card ${idx} with parentParameter: "${
          card.parentParameter
        }"`,
      );

      // Double-check that parent parameter is not empty
      if (!card.parentParameter || card.parentParameter.trim() === '') {
        // This should never happen now, but just in case
        // eslint-disable-next-line no-console
        console.error(
          `Card ${idx} still has empty parent parameter after fixing!`,
        );
        this.setState({
          formError: `Card ${idx + 1} (${
            card.title
          }) has an empty parent parameter. Please enter a value.`,
        });
        valid = false;
        return;
      }

      // Validate parent weightage is not empty
      if (
        card.parentWeightage === '' ||
        !Number.isFinite(Number(card.parentWeightage))
      ) {
        validatedCards[idx].childSumError =
          'Parent weightage is required and must be a number';
        valid = false;
        return; // Skip further validation for this card
      }

      const childSum = card.childParameters.reduce(
        (acc, c) => acc + (Number(c.weightage) || 0),
        0,
      );

      const hasInvalidChildren = card.childParameters.some(
        c =>
          c.weightage !== '' &&
          !Number.isFinite(Number(c.weightage)) &&
          c.weightage !== 0,
      );

      if (hasInvalidChildren) {
        validatedCards[idx].childSumError =
          'All weightages must be valid numbers';
        valid = false;
      } else if (Math.abs(childSum - Number(card.parentWeightage)) > 0.01) {
        validatedCards[
          idx
        ].childSumError = `Total value of child parameters should equal ${
          card.parentWeightage
        }% (parent parameter)`;
        valid = false;
      } else {
        validatedCards[idx].childSumError = '';
      }
    });

    if (!valid) {
      this.setState({ cards: validatedCards });
      return;
    }

    // If validation passed, prepare and submit payload
    if (!this.state.formError) {
      // Prepare parameters array for the new schema
      const parameters = cards.map(card => {
        // Use the fixed project type for each card
        const { projectType, parentWeightage } = card;
        let { parentParameter } = card;

        // Ensure parent parameter is not empty
        // eslint-disable-next-line no-console
        console.log(
          `Preparing payload for card ${
            card.key
          }, parentParameter: "${parentParameter}"`,
        );

        if (!parentParameter || parentParameter.trim() === '') {
          // Use default parent parameter based on project type
          if (projectType === 'Fixed' || projectType === 'Dedicated') {
            parentParameter = 'Project';
          } else if (card.key === 'techRoadmap') {
            parentParameter = 'Tech Roadmap';
          } else if (card.key === 'hr') {
            parentParameter = 'HR';
          } else {
            parentParameter = card.title || 'Project';
          }
          // eslint-disable-next-line no-console
          console.log(`Fixed empty parentParameter to: "${parentParameter}"`);
        }

        // Filter out child parameters with empty names
        const filteredChildParams = card.childParameters
          .filter(child => child.name && child.name.trim() !== '')
          .map(child => ({
            name: child.name,
            // Allow zero weightage
            weightage: Number(child.weightage),
            description: child.description || '',
          }));

        // Calculate total child weightage
        const totalChildWeightage = filteredChildParams.reduce(
          (sum, child) => sum + child.weightage,
          0,
        );

        // Create a copy we can modify
        const validChildParameters = [...filteredChildParams];

        // If parent weightage is 0, we don't need any child parameters
        // Otherwise, adjust child weightages if they don't match parent weightage
        if (
          Number(parentWeightage) !== 0 &&
          Math.abs(totalChildWeightage - Number(parentWeightage)) > 0.01
        ) {
          // If there's a difference, adjust the first child parameter to make it match
          if (validChildParameters.length > 0) {
            const difference =
              Number(parentWeightage) -
              (totalChildWeightage - validChildParameters[0].weightage);
            validChildParameters[0].weightage = Math.max(0, difference);
          }
        }

        // Final check to ensure parentParameter is not empty
        if (!parentParameter || parentParameter.trim() === '') {
          if (card.key === 'project1' || card.key === 'project2') {
            parentParameter = 'Project';
          } else if (card.key === 'techRoadmap') {
            parentParameter = 'Tech Roadmap';
          } else if (card.key === 'hr') {
            parentParameter = 'HR';
          } else {
            parentParameter = card.title || 'Project';
          }
          // eslint-disable-next-line no-console
          console.log(`Final fix for parentParameter: "${parentParameter}"`);
        }

        // If parent weightage is 0, we can submit even with empty child parameters
        return {
          parentParameter,
          parentWeightage: Number(parentWeightage),
          projectType,
          childParameters:
            Number(parentWeightage) === 0 ? [] : validChildParameters,
        };
      });

      // Use the new schema format for the backend
      const payload = {
        roleParameters: [
          {
            applicableRole: selectedRole,
            parameters,
          },
        ],
      };

      // Always log the payload for debugging
      // eslint-disable-next-line no-console
      console.log(
        'Submitting payload to backend:',
        JSON.stringify(payload, null, 2),
      );

      // Final validation check for empty parent parameters
      let hasEmptyParentParameter = false;
      payload.roleParameters[0].parameters.forEach((param, idx) => {
        if (!param.parentParameter || param.parentParameter.trim() === '') {
          // eslint-disable-next-line no-console
          console.error(`Parameter ${idx} has empty parentParameter!`);
          hasEmptyParentParameter = true;
        }
      });

      if (hasEmptyParentParameter) {
        this.setState({
          formError:
            'Some parameters have empty parent parameter values. Please check the console for details.',
        });
        return;
      }

      // Direct fix for any empty parent parameters in the payload
      const fixedParameters = payload.roleParameters[0].parameters.map(
        (param, idx) => {
          // Create a new object to avoid modifying the parameter directly
          const fixedParam = { ...param };

          if (
            !fixedParam.parentParameter ||
            fixedParam.parentParameter.trim() === ''
          ) {
            // Set default parent parameter based on index
            if (idx === 0 || idx === 1) {
              fixedParam.parentParameter = 'Project';
            } else if (idx === 2) {
              fixedParam.parentParameter = 'Tech Roadmap';
            } else if (idx === 3) {
              fixedParam.parentParameter = 'HR';
            } else {
              fixedParam.parentParameter = 'Project';
            }
            // eslint-disable-next-line no-console
            console.log(
              `Fixed payload parameter ${idx} to: "${
                fixedParam.parentParameter
              }"`,
            );
          }

          return fixedParam;
        },
      );

      // Update the payload with fixed parameters
      payload.roleParameters[0].parameters = fixedParameters;

      // POST to backend using the existing API endpoint
      request(API_ENDPOINTS.SAVE_PARAMETER_CONFIG, {
        method: 'POST',
        body: payload,
      })
        .then(() => {
          // Show success message
          message.success('Parameter configuration saved successfully');
          // Success: redirect to PLI page
          window.location.href = ROUTES.PLI;
        })
        .catch(err => {
          // Try to extract detailed error message
          let errorMessage = 'Failed to save configuration. Please try again.';

          if (err.response) {
            try {
              // If we have a response object with data
              if (typeof err.response.json === 'function') {
                err.response.json().then(data => {
                  this.setState({
                    formError: data.message || errorMessage,
                  });
                });
                return; // Return early as we'll set state in the promise
              }
              if (err.response.data) {
                errorMessage = err.response.data.message || errorMessage;
              } else {
                errorMessage = err.response.message || errorMessage;
              }
            } catch (e) {
              // Error parsing response
            }
          } else if (err.message) {
            errorMessage = err.message;
          }

          this.setState({
            formError: errorMessage,
          });
        });
    }
  };

  // Helper method to ensure all parent parameters are set
  ensureParentParameters = cards => {
    // Create a deep copy of the cards array to avoid modifying the original
    const updatedCards = cards.map((card, idx) => {
      // Create a copy of the card
      const updatedCard = { ...card };

      // Normalize parent parameter name
      if (
        updatedCard.parentParameter === 'Project (Fixed)' ||
        updatedCard.parentParameter === 'Project (Dedicated)'
      ) {
        updatedCard.parentParameter = 'Project';
      }

      if (
        !updatedCard.parentParameter ||
        updatedCard.parentParameter.trim() === ''
      ) {
        // Set default parent parameter based on index
        if (idx === 0 || idx === 1) {
          updatedCard.parentParameter = 'Project';
        } else if (idx === 2) {
          updatedCard.parentParameter = 'Tech Roadmap';
        } else if (idx === 3) {
          updatedCard.parentParameter = 'HR';
        }
      }
      return updatedCard;
    });
    return updatedCards;
  };

  // Helper method to return cards without adjusting project weightages
  adjustProjectWeightages = cards => {
    // Create a deep copy of the cards array to avoid modifying the original
    const updatedCards = JSON.parse(JSON.stringify(cards));

    // No adjustment needed anymore, just return the cards
    return updatedCards;
  };

  // Helper method to map child parameters
  mapChildParameters = (childParams, cardIdx) =>
    childParams.map(child => ({
      id: `${child.name}-param-${cardIdx}`,
      name: child.name,
      weightage: child.weightage,
      description: child.description || '',
      isManual: false,
    }));

  // Helper method to update a card with parameters
  updateCardWithParams = (card, param, cardIdx) => {
    // Normalize parent parameter name
    let parentParam = param.parentParameter || card.title;

    // If the parent parameter is "Project (Fixed)" or "Project (Dedicated)", change it to just "Project"
    if (
      parentParam === 'Project (Fixed)' ||
      parentParam === 'Project (Dedicated)'
    ) {
      parentParam = 'Project';
    }

    return {
      ...card,
      parentParameter: parentParam,
      parentWeightage: param.parentWeightage,
      projectType: param.projectType || card.projectType,
      childParameters: this.mapChildParameters(param.childParameters, cardIdx),
    };
  };

  // Helper method to reset a card to default values
  resetCardToDefault = (card, idx) => {
    const defaults = {
      0: {
        parentParameter: 'Project',
        parentWeightage: 20,
        projectType: 'Fixed',
        editableWeightage: true,
        editableParentName: true,
      },
      1: {
        parentParameter: 'Project',
        parentWeightage: 40,
        projectType: 'Dedicated',
        editableWeightage: true,
        editableParentName: true,
      },
      2: {
        parentParameter: 'Tech Roadmap',
        parentWeightage: 30,
        projectType: 'Dedicated',
        editableWeightage: true,
        editableParentName: false,
      },
      3: {
        parentParameter: 'HR',
        parentWeightage: 10,
        projectType: 'Dedicated',
        editableWeightage: true,
        editableParentName: false,
      },
    };

    return {
      ...card,
      parentParameter: defaults[idx].parentParameter,
      parentWeightage: defaults[idx].parentWeightage,
      projectType: defaults[idx].projectType,
      editableWeightage: defaults[idx].editableWeightage,
      editableParentName: defaults[idx].editableParentName,
      childParameters: [],
    };
  };

  // Helper method to get parameters for a role and project type
  getParametersForRoleAndType = (
    role,
    projectType,
    existingParamNames = [],
    idx = 0,
  ) => {
    const newParams = [];

    if (
      role &&
      PARAMETER_MAP[projectType] &&
      PARAMETER_MAP[projectType][role]
    ) {
      // If we have a selected role and parameters for that role, use them
      const paramList = PARAMETER_MAP[projectType][role] || [];

      // Add new parameters that don't exist with values
      paramList.forEach((name, index) => {
        if (!existingParamNames.includes(name)) {
          newParams.push({
            id: `${name}-param-${idx}-${index}`,
            name,
            weightage: '',
            isManual: false,
          });
        }
      });
    } else if (role && ['PO', 'PM', 'TPM'].includes(role)) {
      // Check for PO/PM/TPM group
      const mapRole = 'PO/PM/TPM';
      if (PARAMETER_MAP[projectType] && PARAMETER_MAP[projectType][mapRole]) {
        const paramList = PARAMETER_MAP[projectType][mapRole] || [];

        // Add new parameters that don't exist with values
        paramList.forEach((name, index) => {
          if (!existingParamNames.includes(name)) {
            newParams.push({
              id: `${name}-param-${idx}-${index}`,
              name,
              weightage: '',
              isManual: false,
            });
          }
        });
      }
    } else if (!role) {
      // If no role is selected, get all unique parameters for this project type
      const paramMap = PARAMETER_MAP[projectType] || {};
      let paramIndex = 0;

      // Collect all unique parameters across all roles
      Object.values(paramMap).forEach(paramList => {
        if (Array.isArray(paramList)) {
          paramList.forEach(name => {
            // Don't add duplicates or params that already have values
            if (
              !newParams.find(p => p.name === name) &&
              !existingParamNames.includes(name)
            ) {
              newParams.push({
                id: `${name}-param-${idx}-${paramIndex}`,
                name,
                weightage: '',
                isManual: false,
              });
              paramIndex += 1;
            }
          });
        }
      });
    }

    return newParams;
  };

  refreshParameters = idx => {
    const { selectedRole, cards } = this.state;

    if (!selectedRole) {
      message.warning('Please select a role first');
      return;
    }

    // Get the current card and its project type
    const card = cards[idx];
    const { projectType } = card;

    // Get existing parameter names to avoid duplicates
    const existingParamNames = card.childParameters.map(param => param.name);

    // Get new parameters for the selected role and project type
    const newParams = this.getParametersForRoleAndType(
      selectedRole,
      projectType,
      existingParamNames,
      idx,
    );

    if (newParams.length === 0) {
      message.info(
        'No additional parameters found for this role and project type',
      );
      return;
    }

    // Update the card with the new parameters
    this.setState(prevState => {
      const updatedCards = [...prevState.cards];
      updatedCards[idx] = {
        ...updatedCards[idx],
        childParameters: [...updatedCards[idx].childParameters, ...newParams],
      };

      return { cards: updatedCards };
    });

    message.success(`Parameters refreshed for ${card.title}`);
  };

  handleQuarterChange = (idx, value) => {
    this.setState(prevState => {
      const cards = [...prevState.cards];
      cards[idx] = {
        ...cards[idx],
        quarter: value,
      };
      return { cards };
    });
  };

  handleQuarterChange = (idx, value) => {
    this.setState(prevState => {
      const cards = [...prevState.cards];
      cards[idx] = {
        ...cards[idx],
        quarter: value,
      };
      return { cards };
    });
  };

  render() {
    const { cards, formError, isLoading, roles, selectedRole } = this.state;

    if (isLoading) {
      return (
        <div>
          <Title level={2}>Parameter Configuration</Title>
          <div>Loading configuration data...</div>
        </div>
      );
    }

    return (
      <div>
        <HeaderContainer>
          <Title level={2}>Parameter Configuration</Title>
        </HeaderContainer>

        <RoleSelectorContainer>
          <RoleLabel>Apply Parameters To:</RoleLabel>
          <Select
            placeholder="Select a role"
            style={{ width: 200 }}
            value={selectedRole}
            onChange={this.handleRoleChange}
          >
            {roles &&
              roles.map(role => (
                <Select.Option key={role} value={role}>
                  {role}
                </Select.Option>
              ))}
          </Select>
        </RoleSelectorContainer>

        {cards.map((card, idx) => (
          <div key={card.key}>
            <ParameterCard
              title={card.title}
              parentParameter={card.parentParameter}
              parentWeightage={card.parentWeightage}
              onParentWeightageChange={
                card.editableWeightage
                  ? value => this.handleParentWeightageChange(idx, value)
                  : undefined
              }
              editableWeightage={card.editableWeightage}
              editableParentName={card.editableParentName}
              projectType={card.projectType}
              showProjectType={false}
              childParameters={card.childParameters}
              onChildParamChange={(childIdx, field, value) =>
                this.handleChildParamChange(idx, childIdx, field, value)
              }
              onAddChildParam={() => this.handleAddChildParam(idx)}
              onRemoveChildParam={childIdx =>
                this.handleRemoveChildParam(idx, childIdx)
              }
              childSumError={card.childSumError}
              onParentParameterChange={value =>
                this.handleParentParameterChange(idx, value)
              }
              onRefreshParameters={() => this.refreshParameters(idx)}
              quarter={card.quarter}
              showQuarter={['techRoadmap', 'hr'].includes(card.key)}
              onQuarterChange={value => this.handleQuarterChange(idx, value)}
            />
          </div>
        ))}
        {formError && (
          <div style={{ color: 'red', marginBottom: 16 }}>{formError}</div>
        )}
        <BottomBar>
          <Button
            type="default"
            onClick={this.handleCancel}
            style={{ minWidth: 120 }}
          >
            Cancel
          </Button>
          <Button
            type="primary"
            onClick={this.handleSubmit}
            style={{ minWidth: 120 }}
            disabled={!selectedRole}
          >
            Submit
          </Button>
        </BottomBar>
      </div>
    );
  }
}

export default ParameterConfigurationPage;
