export const PARAMETER_MAP = {
  Dedicated: {
    Developers: [
      'RAG',
      'Sprint Average RATING (PR) + (SONAR)',
      'B2D (NONE then best)',
      'Tech Audit/PR Audit',
      'Least Leave (Un-Planned) - (Individual) - Billing Loss',
      'Customer Feedback / Escalation - (Individual)',
      'Self managing the project, proactiveness - 1/0 ( Project Specific) - Individual',
    ],
    QA: [
      'RAG',
      'B2D from Customer (NONE then best)',
      'B2D Reported (more the better)',
      'Tech Audit',
      'Least Leave (Un-Planned) - (Individual) - Billing Loss',
      'Customer Feedback / Escalation - (Individual)',
      'Self managing the project, proactiveness - 1/0 ( Project Specific) - Individual',
    ],
    'PO/PM/TPM': [
      'RAG',
      'Customer Feedback / Escalation',
      'B2D (Team wide less the better)',
      'Tech Audit',
      'Least Leave (Un-Planned)',
      'Accurate timely Billing Sheet',
      'Velocity / Effort Variance',
    ],
    TL: [
      'RAG',
      'Velocity / Effort Variance',
      'B2D (Team wide less the better)',
      'Tech Audit',
      'Least Leave (Un-Planned)',
      'Customer Feedback / Escalation',
      'Process Audit',
    ],
  },
  Fixed: {
    Developers: [
      'RAG (Budget + Open/Close + B2D + Escalations)',
      'Sprint Average RATING - Sprint Metrics',
      'B2D (Individual)',
      'Tech Audit',
      'Effort Variance (individual)',
      'Process Audit',
      'Customer Feedback',
    ],
    QA: [
      'RAG (Budget + Open/Close + B2D + Escalations)',
      'B2D from Customer (NO then best)',
      'B2D Reported (more the better)',
      'Tech Audit',
      'Effort Variance (individual)',
      'Process Audit',
      'Customer Feedback',
    ],
    TL: [
      'RAG ( Open/Close + B2D + Escalations)',
      'Effort Variance FROM SPRINT REPORT (Avg. Rating)',
      'FPP > Budget Variance (Project Tracker)',
      'Tech Audit',
      'Process Audit',
      'Customer Feedback',
    ],
    'PO/PM/TPM': [
      'FPP > Budget Variance (Project Tracker)',
      'RAG ( Open/Close + B2D + Escalations)',
      'Tech Audit + Process Audit',
    ],
    DevOps: [
      'Projects Deviation Report',
      "POC's Done",
      'Uptime',
      'Security Practices- Follow security practices as per organization',
      'Problem Identification - Ability to identify problems/bottlenecks/RCA',
    ],
    'UI/UX': [
      'Effort Variance',
      'Process Audit',
      'Tech Audit',
      'External Bug (Raised by client - Vikas/project owners)',
      'Internal Bug (Raised by internal team - includes marketing team)',
    ],
  },
};

export const POSITIONS = [
  'Developers',
  'QA',
  'PO',
  'TPM',
  'PM',
  'TL',
  'DevOps',
  'UI/UX',
];
