import React from 'react';
import PropTypes from 'prop-types';
import { Button, Input, InputNumber, Select } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import styled from 'styled-components';
import { PARAMETER_MAP } from './parameterMappings';

const CardWrapper = styled.div`
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 24px 24px 16px 24px;
  margin-bottom: 32px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
`;

const SectionTitle = styled.h3`
  margin-top: 0;
  margin-bottom: 16px;
  font-size: 1.2rem;
  font-weight: 600;
`;

const FieldRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 12px;
`;

const Label = styled.label`
  min-width: 180px;
  font-weight: 500;
`;

const ChildParamRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 8px;
`;

const ErrorText = styled.div`
  color: #d32f2f;
  font-size: 0.95em;
  margin-top: 4px;
`;

const AddButton = styled(Button)`
  margin-left: 8px;
`;

const ParameterCard = ({
  title,
  parentParameter,
  parentWeightage,
  onParentWeightageChange,
  editableWeightage,
  editableParentName,
  projectType,
  childParameters,
  onChildParamChange,
  onAddChildParam,
  onRemoveChildParam,
  childSumError,
  errors,
  onParentParameterChange,
  onRefreshParameters,
  quarter,
  showQuarter,
  onQuarterChange,
}) => (
  <CardWrapper>
    <SectionTitle>{title}</SectionTitle>
    <FieldRow>
      <Label>Parent Parameter</Label>
      <Input
        value={parentParameter}
        onChange={e =>
          onParentParameterChange && onParentParameterChange(e.target.value)
        }
        placeholder="Enter Parent Parameter"
        style={{ width: '100%' }}
        disabled={!editableParentName}
      />
    </FieldRow>
    {showQuarter && (
      <FieldRow>
        <Label>Quarter</Label>
        <Select
          value={quarter}
          onChange={value => onQuarterChange && onQuarterChange(value)}
          style={{ width: '100%' }}
        >
          <Select.Option value="1">Q1</Select.Option>
          <Select.Option value="2">Q2</Select.Option>
          <Select.Option value="3">Q3</Select.Option>
          <Select.Option value="4">Q4</Select.Option>
        </Select>
      </FieldRow>
    )}
    <FieldRow>
      <Label>Parameter Contribution %</Label>
      <InputNumber
        value={parentWeightage}
        min={0}
        precision={0}
        onChange={value =>
          onParentWeightageChange && onParentWeightageChange(value)
        }
        disabled={!editableWeightage}
        style={{ width: '100%' }}
        parser={value => {
          if (value === '0' || value === 0) return 0;
          const cleaned = value ? value.toString().replace(/[^\d]/g, '') : '';
          const parsed = parseInt(cleaned, 10);
          return Number.isNaN(parsed) ? '' : parsed;
        }}
        formatter={value =>
          value !== undefined && value !== null ? `${value}` : ''
        }
        onKeyDown={e => {
          if (
            !/^\d$/.test(e.key) &&
            !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(
              e.key,
            )
          ) {
            e.preventDefault();
          }
        }}
      />
    </FieldRow>
    <FieldRow>
      <Label>Project Type</Label>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span style={{ marginRight: 16 }}>{projectType}</span>
        {onRefreshParameters && (
          <Button
            type="default"
            icon={<ReloadOutlined />}
            onClick={onRefreshParameters}
            style={{ marginLeft: 8 }}
          >
            Refresh Parameters
          </Button>
        )}
      </div>
    </FieldRow>
    <FieldRow style={{ alignItems: 'flex-start', flexDirection: 'column' }}>
      <Label style={{ marginBottom: 8 }}>Child Parameters</Label>
      <div style={{ width: '100%' }}>
        {childParameters.map((child, idx) => (
          <ChildParamRow key={child.id}>
            {child.isManual ? (
              <Select
                value={child.name}
                onChange={value => {
                  if (onChildParamChange) {
                    onChildParamChange(idx, 'name', value);
                  }
                }}
                placeholder="Select Parameter"
                style={{ width: '60%', marginRight: '8px' }}
                showSearch
                optionFilterProp="children"
              >
                {/* Get all available parameters for the project type */}
                {(() => {
                  // Get all parameters from all roles and project types
                  const allParams = Object.entries(PARAMETER_MAP).flatMap(
                    ([, /* key */ roles]) =>
                      Object.values(roles).flatMap(params => params),
                  );

                  // Remove duplicates using Set
                  const uniqueParams = [...new Set(allParams)];

                  // Filter out parameters that are already used in other child parameters
                  /* eslint-disable prettier/prettier */
                  return uniqueParams
                    .filter(function(param) {
                      return !childParameters.some(function(cp) {
                        return cp.name === param && cp.id !== child.id;
                      });
                    })
                    .map(function(param) {
                      return (
                        <Select.Option key={param} value={param}>
                          {param}
                        </Select.Option>
                      );
                    });
                })()}

                {/* Allow custom parameter name */}
                {child &&
                  child.name &&
                  !Object.entries(PARAMETER_MAP).some(function([/* key */, roles]) {
                    return Object.values(roles).some(function(params) {
                      return (
                        Array.isArray(params) &&
                        params.indexOf(child.name) !== -1
                      );
                    });
                  }) && (
                  <Select.Option key={child.name} value={child.name}>
                    {child.name} (Custom)
                  </Select.Option>
                )}
              </Select>
            ) : (
              <Input
                value={child && child.name ? child.name : ''}
                placeholder="Parameter Name"
                style={{ width: '60%', marginRight: '8px' }}
                disabled
              />
            )}
            <InputNumber
              value={child.weightage}
              onChange={value => {
                if (onChildParamChange) {
                  onChildParamChange(idx, 'weightage', value);
                }
              }}
              placeholder="Weightage"
              min={0}
              precision={0}
              parser={value => {
                if (value === '0' || value === 0) return 0;
                const cleaned = value
                  ? value.toString().replace(/[^\d]/g, '')
                  : '';
                const parsed = parseInt(cleaned, 10);
                return Number.isNaN(parsed) ? '' : parsed;
              }}
              formatter={value => (value !== undefined && value !== null ? `${value}` : '')}
              style={{ width: '30%' }}
              onKeyDown={e => {
                if (
                  !/^\d$/.test(e.key) &&
                  ![
                    'Backspace',
                    'Delete',
                    'ArrowLeft',
                    'ArrowRight',
                    'Tab',
                  ].includes(e.key)
                ) {
                  e.preventDefault();
                }
              }}
            />
            <Button
              type="default"
              danger
              style={{ marginLeft: 8 }}
              onClick={() => onRemoveChildParam && onRemoveChildParam(idx)}
            >
              -
            </Button>
          </ChildParamRow>
        ))}
        <AddButton type="dashed" onClick={onAddChildParam}>
          Add a new parameter
        </AddButton>
        {childSumError && <ErrorText>{childSumError}</ErrorText>}
      </div>
    </FieldRow>
    {errors && <ErrorText>{errors}</ErrorText>}
  </CardWrapper>
);

ParameterCard.propTypes = {
  title: PropTypes.string.isRequired,
  parentParameter: PropTypes.string.isRequired,
  parentWeightage: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onParentWeightageChange: PropTypes.func,
  editableWeightage: PropTypes.bool,
  editableParentName: PropTypes.bool,
  projectType: PropTypes.string,
  childParameters: PropTypes.array,
  onChildParamChange: PropTypes.func,
  onAddChildParam: PropTypes.func,
  onRemoveChildParam: PropTypes.func,
  childSumError: PropTypes.string,
  errors: PropTypes.string,
  onParentParameterChange: PropTypes.func,
  onRefreshParameters: PropTypes.func,
  quarter: PropTypes.string,
  showQuarter: PropTypes.bool,
  onQuarterChange: PropTypes.func,
};

export default ParameterCard;

