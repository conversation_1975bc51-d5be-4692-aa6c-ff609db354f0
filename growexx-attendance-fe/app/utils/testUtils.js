import StorageService from './StorageService';
import { TOKEN_KEY, USER_DATA_KEY } from './constants';

/**
 * Sets up test authentication by storing a test token and user data
 * This is only for development/testing purposes
 */
export const setupTestAuth = () => {
  // Replace this with a valid token from your backend
  const testToken = 'your-test-token-here';
  const testUserData = {
    id: 1,
    email: '<EMAIL>',
    role: 'mentor',
  };

  StorageService.set(TOKEN_KEY, testToken);
  StorageService.set(USER_DATA_KEY, testUserData, { stringify: true });
};

/**
 * Clears test authentication data
 */
export const clearTestAuth = () => {
  StorageService.delete(TOKEN_KEY);
  StorageService.delete(USER_DATA_KEY);
};
