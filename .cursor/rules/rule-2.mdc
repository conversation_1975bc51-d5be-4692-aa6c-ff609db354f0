---
description: 
globs: 
alwaysApply: true
---
# Frontend Architecture
The frontend is a React application using Redux for state management following the container/presentational pattern.
## Component Structure
### Components
Components in @app/components/ are presentational and focus on UI rendering:
- They receive data via props
- They should not contain business logic
- Each component has its own directory with tests
Example components:
- @Header: Application header
- @SideBar: Navigation sidebar
- @ModalComponent: Reusable modal dialog
### Containers
Containers in @app/containers/ are connected to Redux:
- They contain business logic
- They handle data fetching and state updates
- They use presentational components for rendering
- Structure follows Redux pattern with actions, reducers, sagas, and selectors
Example containers:
- @Login: Authentication login page
- @UserListing: User management
- @ProjectListing: Project management
## Testing
- Components and containers should have tests in their respective `tests` directories
- Use Jest and Enzyme for testing
- Snapshot tests for UI components
- Unit tests for business logic