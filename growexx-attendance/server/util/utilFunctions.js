const HTTPStatus = require('../util/http-status');

/**
 * This class reprasents common utilities for application
 */
class Utils {
    static errorResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 0,
                data: {},
                message: ''
            })
        );
    }

    static successResponse () {
        return JSON.parse(
            JSON.stringify({
                status: 1,
                data: {},
                message: ''
            })
        );
    }

    /**
   * This function is being used to add pagination for user table
   * @auther Growexx
   * @param {string} error Error Message
   * @param {Object} data Object to send in response
   * @param {Object} res Response Object
   * @param {string} successMessage success message
   * @param {Object} additionalData additional data outside of data object in response
   * @param {string} successMessageVars
   * @since 01/03/2021
   */
    static sendResponse (error, data, res, successMessage, successMessageVars) {
        let responseObject;

        if (error) {
            if (process.env.DEBUG) {
                CONSOLE_LOGGER.error(error);
            }
            let status;
            responseObject = Utils.errorResponse();
            if (typeof error === 'object') {
                responseObject.message = error.message
                    ? error.message
                    : res.__('ERROR_MSG');
                status = error.statusCode ? error.statusCode : HTTPStatus.BAD_REQUEST;
            } else {
                responseObject.message = res.__(error);
                status = HTTPStatus.BAD_REQUEST;
            }

            responseObject.data = error.data;
            res.status(status).send(responseObject);
        } else {
            responseObject = Utils.successResponse();
            responseObject.message = successMessageVars
                ? res.__.apply('', [successMessage].concat(successMessageVars))
                : successMessage;
            responseObject.data = data;
            res.status(HTTPStatus.OK).send(responseObject);
        }
    }

    static sendResponseWithInvalidData (
        error,
        data,
        res,
        successMessage,
        successMessageVars
    ) {
        const responseObject = Utils.successResponse();
        responseObject.message = successMessageVars
            ? res.__.apply('', [successMessage].concat(successMessageVars))
            : successMessage;
        responseObject.data = data;
        responseObject.status = 0;
        res.status(HTTPStatus.OK).send(responseObject);
    }

    static downloadFile (res, data) {
        data.headers.map((h) => {
            res.setHeader(h.key, h.value);
        });
        res.status(200).end(data.data);
    }

    static extractHostname (url) {
        var hostname;
        if (url.indexOf('//') > -1) {
            hostname = url.split('/')[2];
        } else {
            hostname = url.split('/')[0];
        }

        hostname = hostname.split(':')[0];
        hostname = hostname.split('?')[0];

        return hostname;
    }

    static getHoursFromSeconds (seconds) {
        return Math.round((seconds / 3600) * 100) / 100;
    }

    static getDeviationPercentace (loggedHours) {
        return (
            Math.round(
                ((CONSTANTS.STANDARD_DAY_HOURS - loggedHours) /
          CONSTANTS.STANDARD_DAY_HOURS) *
          10000
            ) / 100
        );
    }

    static roundToTwo (num) {
        return Math.round(num * 100) / 100;
    }

    static calculateBusinessDays (startDate, endDate) {
        const today = MOMENT();
        const d1 = MOMENT(startDate);
        let d2 = MOMENT(endDate);

        if (today < d2) {
            d2 = today;
        }

        const days = d2.diff(d1, 'days');
        let newDay = d1;
        let workingDays = 0;

        for (let i = 0; i <= days; i++) {
            const day = MOMENT(newDay).day();
            const isWeekend = day % 6 === 0;
            if (!isWeekend) {
                workingDays++;
            }

            newDay = MOMENT(newDay).add(1, 'days');
        }

        return workingDays;
    }

    static calculateDOJDifferenceDays (doj, startDate) {
        const d1 = MOMENT(doj).add(-1, 'days');
        const d2 = MOMENT(startDate);
        let workingDays = 0;

        if (d1.year() === d2.year() && d1.month() === d2.month() && d1 > d2) {
            const days = d1.diff(d2, 'days');
            let newDay = d2;
            for (let i = 0; i <= days; i++) {
                const day = MOMENT(newDay).day();
                const isWeekend = day % 6 === 0;
                if (!isWeekend) {
                    workingDays++;
                }

                newDay = MOMENT(newDay).add(1, 'days');
            }
        }
        return workingDays;
    }

    static getOffDayLeaveHours (leavesArr) {
        let totalHours = 0;
        for (let i = 0; i < leavesArr.length; i++) {
            const date = leavesArr[i].leaveDate;
            const hour = leavesArr[i].timeSpentHours;
            const day = MOMENT(date).day();
            const isWeekend = day % 6 === 0;
            if (isWeekend) {
                totalHours = totalHours + hour;
            }
        }
        return totalHours;
    }

    static getTimeSpentHours (duration) {
        let timeSpentHours = 0;
        switch (duration) {
            case 'full':
                timeSpentHours = 8;
                break;
            case 'first':
            case 'second':
                timeSpentHours = 4;
                break;
            default:
        }

        return timeSpentHours;
    }

    static getEmployeeList (userId, myTeam) {
        const allMyTeamUsers = [userId];

        myTeam.forEach((d) => {
            d.users.forEach((user) => {
                allMyTeamUsers.push(user.empId);
            });
        });

        return { $in: allMyTeamUsers };
    }

    static getOriginalDateFromTimeZoneMoment (foreignDate, timeZone) {
        const momentTZ = require('moment-timezone');
        const foreignDateMoment = momentTZ.tz(foreignDate, timeZone);
        return foreignDateMoment.startOf('day');
    }
    static generateUuid () {
        const { v4: uuidv4 } = require('uuid');
        return uuidv4();
    }
}

module.exports = Utils;
