module.exports = {
    IV_LENGTH: 16,
    LOG_LEVEL: 'debug',
    PROFILE_PICTURE: {
        MIN_SIZE: 5120,
        MAX_SIZE: 5242880,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png']
    },
    USER_DOCUMENT_FILE: {
        MIN_SIZE: 10240,
        MAX_SIZE: 5242880,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf']
    },
    KRA_ATTACHMENT_FILE: {
        MIN_SIZE: 5120,
        MAX_SIZE: 1048576,
        ALLOWED_TYPE: ['image/jpg', 'image/jpeg', 'image/png', 'application/pdf'],
        MAX_FILE_COUNT: 2
    },
    REGEX: {
        EMAIL: /^[A-Za-z0-9](\.?[A-Za-z0-9_-]){0,}@[A-Za-z0-9-]+\.([a-z]{1,6}\.)?[a-z]{2,6}$/,
        FIRSTNAME: /^[a-zA-Z0-9,'~._^ -]*$/,
        SURNAME: /^[a-zA-Z0-9,'~._^ -]*$/,
        ALPHA_ONLY: /^[a-zA-Z']*$/,
        ALPHA_SPECIAL_CHAR: /^[ A-Za-z0-9_@./#&+-]*$/,
        ALPHA_SPECIAL_CHAR_EXCEPT_NUMBER: /^[ A-Za-z_@./#&+-]*$/,
        FULL_ACCESS: /^[^<> ?//\\]+$/,
        ALPHA_NUMARIC: /^[\w@ ]+$/,
        URL: /(http(s)?:\/\/www\.)?[-a-zA-Z0-9@:%.+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%+.~#?&//=]*)/
    },
    OTPLENGTH: 6,
    STATUS: {
        PENDING: 0,
        ACTIVE: 1,
        SUSPENDED: 2
    },
    ENVIRONMENT: {
        TESTING: 'testing',
        LOCAL: 'local',
        DEV: 'dev',
        PRODUCTION: 'production'
    },
    DEVELOPERS_EMAIL: '<EMAIL>',
    SES_HOST: 'email-smtp.eu-west-1.amazonaws.com',
    ROLE: {
        USER: 1,
        ADMIN: 4,
        PM: 2,
        BU: 5,
        HR: 6
    },
    DURATION: {
        FIRST: 'first',
        SECOND: 'second',
        FULL: 'full'
    },
    HALF_DAY_STRING: '(Half Day)',
    LEAVE_TYPE: ['Sick or Casual Leave', 'Paid Leave', 'Leave Without Pay',
        'Compensatory Off', 'Holiday', 'Comp off', 'H', 'Paternity Leave',
        'Optional Holiday', 'Sick Leave', 'Casual Leave', 'Maternity Leave',
        'Present ( Paid Leave  )', 'Present ( Leave Without Pay  )',
        'Present ( Compensatory Off  )', 'Present ( Holiday  )', 'Present ( Sick Leave  )',
        'Present ( Casual Leave  )',
        'Present ( Paid Leave )', 'Present ( Leave Without Pay )',
        'Present ( Compensatory Off )', 'Present ( Holiday )', 'Present ( Sick Leave )',
        'Present ( Casual Leave )', 'P (Sick Leave)'
    ],
    STANDARD_DAY_HOURS: 8,
    LEAVE_FILE_TYPE: ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    PROJECT_HEADER_FILE_TYPE: ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
    PROJECT_INACTIVE_STATE: ['Archived', 'Completed', 'Hold'],
    ALL_PROJECT_STATE: ['Active', 'Archived', 'Completed', 'Hold'],
    LEVELS: ['L0', 'L1', 'L2', 'L3', 'L4', 'L5', 'L6'],
    DESIGNATION: [
        'Asst. Manager - HR',
        'Asst. Manager - Talent Acquisition',
        'Business Analyst',
        'Business Advisor',
        'Business Development Executive',
        'Business Development Manager',
        'Business Unit Head - Web & Mobile',
        'Business Unit Head',
        'Co-Founder',
        'Data Analyst',
        'Data Engineer',
        'Database Administrator',
        'Data Scientist',
        'Database Administrator',
        'Digital Marketing Head',
        'Director',
        'Engineer - IT & DevOps',
        'Executive - Business Development',
        'Executive - Digital Marketing',
        'Executive - HR',
        'Executive - Lead Generation',
        'Executive - Talent Acquisition',
        'Executive - Web Research',
        'Executive Assistant',
        'Head - HR & Ops',
        'Jr. Data Analyst',
        'Jr. Database Administrator',
        'Jr. Data Engineer',
        'Jr. Database Administrator',
        'Jr. Software Engineer',
        'Jr. Software Engineer - DevOps',
        'Lead - Data Engineering',
        'Lead Engineer - QA',
        'Lead - UX',
        'Management Trainee',
        'Management Trainee - HR',
        'Manager - Business Development',
        'Manager - Data Engineering',
        'Manager - HR',
        'Manager Solutions',
        'Office Boy',
        'Practice Head - Data Science and Analytics',
        'Product Manager',
        'Product Owner',
        'Project Manager',
        'Regional Head - Sales',
        'Technical Consultant',
        'Scrum Master',
        'SEO Executive',
        'Software Engineer',
        'Software Engineer - DevOps',
        'Software Engineer - QA',
        'Sr. Business Analyst',
        'Sr. Data Analyst',
        'Sr. Database Administrator',
        'Sr. Data Engineer',
        'Sr. Data Scientist',
        'Sr. Database Administrator',
        'Sr. Executive - Business Development',
        'Sr. Executive - Digital Marketing',
        'Sr. Executive - HR',
        'Sr. Executive - Lead Generation',
        'Sr. Executive - Talent Acquisition',
        'Sr. Executive - Web Research',
        'Sr. Product Owner',
        'Sr. Software Engineer',
        'Sr. Software Engineer - DevOps',
        'Sr. Software Engineer - QA',
        'Sr. Technical Lead',
        'Sr. Technical Project Manager',
        'Sr. UI/UX Designer',
        'Technical Lead',
        'Sr. Web Analytics Engineer',
        'Technical Lead - DevOps',
        'Technical Project Manager',
        'UI/UX Designer',
        'Web Research Executive',
        'Service Delivery Manager',
        'Jr. Consultant',
        'Sr. Consultant'
    ],
    PROJECT_STATUS: [0, 1],
    KRA_CATAGORY: ['Business Result', 'Development Goals', 'Impact to customer', 'Process Adherence', 'Value Add'],
    SIZE: {
        MIN: 1,
        MAX: 1000
    },
    WEIGHTAGE: {
        MIN: 0,
        MAX: 100
    },
    KRA_QUARTER: ['Q1', 'Q2', 'Q3', 'Q4'],
    KRA_YEAR: [2022, 2023, 2024, 2025],
    AWS_S3_PUBLIC_BUCKET: 's3-dev-timesheet-kra-documents',
    MANAGER_TYPE: ['REPORTING_MANAGER', 'REVIEW_MANAGER'],
    ADMIN_EMAILS: ['<EMAIL>'],
    KRA_ADMINS_EMAIL: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ],
    MISSED_LOGS_EMAIL: {
        production: [
            '<EMAIL>'
        ],
        development: [
            '<EMAIL>'
        ]
    },
    BUSINESS_UNIT: [
        'Data Science',
        'HR & Operations',
        'Management',
        'Microsoft Services',
        'Sales',
        'Web & Mobile',
        'Oracle',
        'N/A'
    ],
    KRA_STATUS: {
        KRA_ASSIGNED: 'KRA Assigned',
        ASSESSMENT_STARTED: 'Assessment Started',
        SELF_COMMENT_FROZEN: 'Self Comment Frozen',
        REPORTING_MANAGER_RATING_FROZEN: 'Reporting Manager rating frozen',
        UNFREEZE_FOR_REPORTING_MANAGER: 'Unfreezed for Reporting Manager',
        REVIEW_MANAGER_RATING_FROZEN: 'Review Manager rating frozen',
        UNFREEZE_FOR_REVIEW_MANAGER: 'Unfreezed for Review Manager',
        FEEDBACK_MEETING_STARTED: 'Feedback Meeting Started',
        FEEDBACK_MEETING_DONE: 'Feedback Meeting Done',
        FEEDBACK_MEETING_ACKNOWLEDGE: 'Feedback meeting acknowledged',
        RATING_RELEASED: 'Rating Released',
        BU_HEAD_APPROVED: 'BU Head Approved',
        KRA_SAVED: 'KRA Saved'
    },
    PARKSTREET_PROJECT_ID: '60704d1eb965020716a63ab0',
    CHEATCODE_MAINTENANCE: true,
    APPYHOUR_MAINTENANCE: false,
    PROJECT_TRACKER: {
        USER_TYPE: {
            DEV: 'Developer',
            PO: 'PO',
            TPM_TL: 'TPM/TL',
            DEVOPS: 'DevOps',
            SCRUM_RITUALS: 'Scrum Rituals'
        }
    },
    ISSUE_TYPE: {
        EPIC: 'Epic',
        BUG: 'Bug',
        STORY: 'Story',
        TASK: 'Task',
        SUBTASK: 'Sub-task'
    },
    EPIC_STATUS: {
        TO_DO: 'To Do',
        IN_PROGRESS: 'In Progress',
        DONE: 'Done'
    },
    SPRINT_STATUS: {
        ACTIVE: 'active',
        CLOSED: 'closed',
        FUTURE: 'future'
    },
    COLOR_CODE: {
        RED: 'red',
        AMBER: 'amber',
        GREEN: 'green'
    },
    SONAR_COVERAGE_URL: 'https://quality.growexx.com/api/measures/search_history',
    SONAR_SEARCH_ISSUES_URL: 'https://quality.growexx.com/api/issues/search',
    DEFAULT_PR_FETCH_LIMIT: 100,
    DEFAULT_PR_FETCH_PAGE: 1,
    NOT_AVAILABLE_TEXT: 'NA',
    SORTING: {
        FIRST_PRIORITY: 'resource',
        SECOND_PRIORITY: 'prCount',
        THIRD_PRIORITY: 'repo'
    },
    SWAGGER_RATINGS: {
        FIVE: 100,
        FOUR: 80,
        THREE: 50,
        TWO: 25
    },
    COVERAGE_RATINGS: {
        FIVE: 90,
        FOUR: 80,
        THREE: 50,
        TWO: 30,
        ONE: 1
    },
    PR_RATINGS: {
        FIVE: 100,
        FOUR: 80,
        THREE: 50,
        TWO: 25,
        ONE: 1
    },
    CRON_FAILURE_EMAIL: [
        '<EMAIL>',
        '<EMAIL>'
    ],
    PROJECTS: {
        ATLAS: 'ATLAS- The Internal Auditing',
        BEAT_BREAD: 'Beat-Bread',
        READERR_IO: 'Readerr.io',
        CERTAINTY_SOURCE: 'certaintySource',
        GROWEXX_WEBSITE: 'growexxWebsite',
        SCRUM_IT: 'Scrumit',
        PSI_MOBILE_FLUTTER: 'PSI Mobile',
        PSI_ENHANCEMENTS: 'PSI Enhancements',
        PSI_II: 'PSI II',
        PSI_IV: 'PSI IV',
        PSI_V: 'PSI V',
        PSI_VI: 'PSI VI',
        PSI_VII: 'PSI VII',
        PSI_WW: 'PSI WW',
        PSI_IX: 'PSI IX',
        SECRET_IMAGE: 'SecretImage',
        SIMPLY_MUSIC: 'Simply Music',
        IMADRASSA: 'iMadrassa Mobile Application',
        MATANGI: 'Matangi Scrapper',
        PURPLI: 'Purpli',
        VAALEE: 'Vaalee',
        INFLUENTEX: 'Influentex'
    },
    RAG_EDITABLE_COLUMNS: {
        CLIENT_ESCALATION: 'clientEscalations',
        RISKS_IDENTIFIED: 'risksIdentified',
        MITIGATION_PLAN: 'mitigationPlan',
        COMMENTS: 'comments',
        TECH_AUDIT: 'techAudit',
        PROCESS_AUDIT: 'processAudit',
        DELIVERY_HEAD_COMMENTS: 'deliveryHeadComments',
        API_CREATED: 'apiCreated',
        MEMBER_CLIENT_ESCALATION: 'memberClientEscalation'
    },
    DAYS_MAPPING: {
        Sunday: 0,
        Monday: 1,
        Tuesday: 2,
        Wednesday: 3,
        Thursday: 4,
        Friday: 5,
        Saturday: 6
    },
    IN_PROGRESS: 'IN_PROGRESS',
    LOG_STATUS: {
        PENDING: 0,
        APPROVED: 1,
        REJECTED: 2
    }
};
