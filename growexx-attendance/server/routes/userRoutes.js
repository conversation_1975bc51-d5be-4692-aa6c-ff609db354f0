const router = require('express').Router();
const AuthMiddleWare = require('../middleware/auth');
const ACLMiddleWare = require('../middleware/acl');
const UploadMiddleWare = require('../middleware/upload');
const AuthMiddleWareManager = require('../middleware/managerAuth');
const userProfileController = require('../services/userProfile/userProfileController');
const listUserController = require('../services/listUser/listUserController');
const listMemberController = require('../services/listMember/listMemberController');
const listProjectManagerController = require('../services/listProjectManager/listProjectManagerController');
const addUserController = require('../services/addUser/addUserController');
const editUserController = require('../services/editUser/editUserController');
const getUserAttendanceController = require('../services/getAttendance/getAttendanceController');
const downloadUserAttendanceController = require('../services/downloadAttendance/downloadAttendanceController');
const addUserLeaveController = require('../services/addLeave/addLeaveController');
const uploadUserLeaveController = require('../services/uploadLeave/uploadLeaveController');
const downloadPersonDayReportController = require('../services/downloadPersonDayReport/downloadPersonDayReportController');
const downloadLogsReport = require('../services/downloadLogsReport/downloadLogsReportController');
const changUserStatusController = require('../services/changeStatusUser/changeStatusUserController');
const DownloadBillingReportController = require('../services/downloadBillingReport/downloadBillingReportController');
const GetUserAttendanceByProjectController = require('../services/getAttendanceByProject/getUserAttendanceByProjectController');
const UpdateLogController = require('../services/updateLog/updateLogController');
const DesignationController = require('../services/designation/designationController');
const menteeController = require('../services/mentee/menteeController');

router.get(
    '/',
    AuthMiddleWare,
    AuthMiddleWareManager,
    ACLMiddleWare,
    listUserController.listUsers
);
router.get(
    '/members',
    AuthMiddleWare,
    ACLMiddleWare,
    listMemberController.listMembers
);
router.get(
    '/pms',
    AuthMiddleWare,
    ACLMiddleWare,
    listProjectManagerController.listPms
);
router.post('/', AuthMiddleWare, ACLMiddleWare, addUserController.addUser);
router.put('/', AuthMiddleWare, ACLMiddleWare, editUserController.editUser);
router.patch(
    '/role',
    AuthMiddleWare,
    ACLMiddleWare,
    editUserController.editUserRole
);
router.get(
    '/details',
    AuthMiddleWare,
    ACLMiddleWare,
    userProfileController.getUserDetails
);
router.put(
    '/picture',
    AuthMiddleWare,
    ACLMiddleWare,
    UploadMiddleWare.single('photo'),
    userProfileController.updateProfilePicture
);
router.delete(
    '/picture',
    AuthMiddleWare,
    ACLMiddleWare,
    userProfileController.deleteProfilePicture
);
router.put('/password', AuthMiddleWare, userProfileController.changePassword);
router.post(
    '/designation',
    AuthMiddleWare,
    DesignationController.addDesignation
);
router.get(
    '/designation',
    AuthMiddleWare,
    DesignationController.listDesignation
);

router.get(
    '/attendance',
    AuthMiddleWare,
    ACLMiddleWare,
    getUserAttendanceController.getUserAttendance
);
router.get(
    '/attendance/download',
    AuthMiddleWare,
    ACLMiddleWare,
    downloadUserAttendanceController.downloadUserAttendance
);

router.post(
    '/leave',
    AuthMiddleWare,
    ACLMiddleWare,
    addUserLeaveController.addUserLeave
);
router.post(
    '/leaves',
    AuthMiddleWare,
    ACLMiddleWare,
    UploadMiddleWare.single('file'),
    uploadUserLeaveController.uploadUserLeave
);

router.get(
    '/person-day/download',
    AuthMiddleWare,
    ACLMiddleWare,
    downloadPersonDayReportController.downloadPersonDayReport
);
router.get(
    '/logs/download',
    AuthMiddleWare,
    ACLMiddleWare,
    downloadLogsReport.downloadLogsReport
);
router.patch(
    '/status',
    AuthMiddleWare,
    ACLMiddleWare,
    changUserStatusController.changeUserStatus
);

router.get('/bu', AuthMiddleWare, listUserController.buList);
router.get(
    '/billing-sheet/download',
    AuthMiddleWare,
    ACLMiddleWare,
    DownloadBillingReportController.downloadBillingReport
);

router.get(
    '/attendance-by-project',
    AuthMiddleWare,
    GetUserAttendanceByProjectController.getUserAttendanceByProject
);
router.put(
    '/logs/update-status',
    AuthMiddleWare,
    UpdateLogController.updateLogStatus
);
router.get('/server-time', AuthMiddleWare, userProfileController.getServerTime);
router.get('/logs/bulk-action', UpdateLogController.updateBulkLogStatus);

// Legacy route (keeping for backward compatibility)
router.get('/mentees', AuthMiddleWare, menteeController.getMentees);

// New routes with pagination and filtering
router.get('/mentees/list', AuthMiddleWare, menteeController.listMentees);

// New route to get all mentees grouped by mentors (public access)
router.get(
    '/mentees/grouped-by-mentor',
    menteeController.getMenteesGroupedByMentor
);

router.get('/mentees/:id', AuthMiddleWare, menteeController.getMenteeById);
router.put('/mentees/:id', AuthMiddleWare, menteeController.updateMentee);
router.post(
    '/mentees/assign',
    AuthMiddleWare,
    menteeController.assignMenteesToMentor
);

// New route for processing mentor-mentee assignments from JSON data
router.post(
    '/mentees/process-assignments',
    menteeController.processMentorMenteeAssignments
);

// New route for uploading mentor-mentee assignments
router.post(
    '/mentees/upload-assignments',
    UploadMiddleWare.single('file'),
    menteeController.processMentorMenteeAssignments
);

module.exports = router;
