/**
 * Tech Roadmap Routes
 */
const express = require('express');
const router = express.Router();
const multer = require('multer');
const techRoadmapController = require('../services/techRoadmap/techRoadmapController');
const techRoadmapValidator = require('../services/techRoadmap/techRoadmapValidator');
const auth = require('../middleware/auth');
const { checkRole } = require('../middleware/roleAuth');
const { ROLE } = require('../util/constants');

// Configure multer for file uploads
const storage = multer.memoryStorage();
const upload = multer({
    storage,
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    },
    fileFilter: (req, file, cb) => {
    // Accept only pdf, jpg, jpeg and png files
        if (
            file.mimetype === 'application/pdf' ||
      file.mimetype === 'image/jpeg' ||
      file.mimetype === 'image/jpg' ||
      file.mimetype === 'image/png'
        ) {
            cb(null, true);
        } else {
            cb(new Error('Only PDF, JPG, JPEG, and PNG files are allowed'), false);
        }
    }
});

/**
 * @route POST /tech-roadmap/course
 * @desc Add a new course
 * @access Private - HR only
 */
router.post('/course', auth, checkRole([ROLE.HR, ROLE.ADMIN]), techRoadmapValidator.validateAddCourse, techRoadmapController.addCourse);

/**
 * @route GET /tech-roadmap/course
 * @desc Get all courses
 * @access Private - All authenticated users
 */
router.get('/course', auth, techRoadmapController.getAllCourses);

/**
 * @route POST /tech-roadmap/assign
 * @desc Assign a course to a mentee
 * @access Private
 */
router.post('/assign', auth, techRoadmapValidator.validateAssignCourse, techRoadmapController.assignCourse);

/**
 * @route GET /tech-roadmap/mentee/:menteeId
 * @desc Get all courses assigned to a mentee
 * @access Private
 */
router.get('/mentee/:menteeId', auth, techRoadmapValidator.validateObjectId('menteeId'), techRoadmapController.getMenteeCourses);

/**
 * @route PATCH /tech-roadmap/status/:assignmentId
 * @desc Update course completion status
 * @access Private
 */
router.patch('/status/:assignmentId',
    auth,
    techRoadmapValidator.validateObjectId('assignmentId'),
    techRoadmapValidator.validateUpdateCourseStatus,
    techRoadmapController.updateCourseStatus
);

/**
 * @route GET /tech-roadmap/all-assignments
 * @desc Get all tech roadmap assignments
 * @access Private - All authenticated users
 */
router.get('/all-assignments', auth, techRoadmapController.getAllAssignments);

/**
 * @route POST /tech-roadmap/upload-document
 * @desc Upload a document for a course assignment
 * @access Public - Anyone can upload documents
 */
router.post('/upload-document', upload.single('document'), techRoadmapController.uploadDocument);

/**
 * @route POST /tech-roadmap/document-approval
 * @desc Approve or reject a document
 * @access Private - Only authenticated users (mentors, HR, admin)
 */
router.post('/document-approval', auth, techRoadmapValidator.validateDocumentApproval, techRoadmapController.updateDocumentApprovalStatus);

/**
 * @route POST /tech-roadmap/update-comments
 * @desc Update mentor or mentee comments for a course assignment
 * @access Private - Only authenticated users
 */
router.post('/update-comments', auth, techRoadmapValidator.validateUpdateComments, techRoadmapController.updateComments);

/**
 * @route DELETE /tech-roadmap/assignment/:id
 * @desc Delete a course assignment
 * @access Private - Only authenticated users
 */
router.delete('/assignment/:id', auth, techRoadmapValidator.validateObjectId('id'), techRoadmapController.deleteCourseAssignment);


// Serve uploaded files statically
router.use('/uploads', express.static('uploads'));

module.exports = router;
