const mongoose = require("mongoose");
const Logs = require("../../models/logs.model");
const User = require("../../models/user.model");
const BusinessUnit = require("../../models/bu.model");
const Leave = require("../../models/leave.model");
const GeneralError = require("../../util/GeneralError");
const Utils = require("../../util/utilFunctions");
const logsModel = require("../../models/logs.model");
/**
 * Class represents services for fetch user attendance.
 */
class FetchJiraLogsService {
  /**
   * @desc This function is being used to fetch user attendance
   * <AUTHOR>
   * @since 25/03/2021
   * @param {Object} req Request
   * @param {Object} user Logged in user details
   * @param {Object} locale Locale passed from request
   * @return {Object} response Success response
   */
  static async getUserAttendance(req, user, locale) {
    const where = {};
    const leaveWhere = {};
    const userId = mongoose.Types.ObjectId(req.query.userId);

    let buDetails = "";
    if (user.role === CONSTANTS.ROLE.BU) {
      buDetails = await BusinessUnit.findOne(
        { userId: user._id },
        { _id: 1 }
      ).lean();
    }

    if (user.role === CONSTANTS.ROLE.ADMIN) {
      if (!req.query.userId) {
        throw new GeneralError(locale("SELECT_EMPLOYEE"), 400);
      }
      where.userId = userId;
      leaveWhere.userId = userId;
    } else if (req.query.userId && user._id !== userId) {
      const aggregateParams = [
        {
          $match: {
            userId,
          },
        },
        {
          $lookup: {
            from: "projects",
            let: {
              projectName: "$jiraProjectName",
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ["$projectName", "$$projectName"],
                      },
                      {
                        $or: [
                          {
                            $eq: ["$pmUser", user._id],
                          },
                          {
                            $eq: ["$reviewManager", user._id],
                          },
                          {
                            $eq: ["$businessUnitId", buDetails?._id],
                          },
                        ],
                      },
                    ],
                  },
                },
              },
            ],
            as: "projectDetails",
          },
        },
        {
          $unwind: {
            path: "$projectDetails",
            preserveNullAndEmptyArrays: false,
          },
        },
      ];
      const isMyTeamMember = await logsModel.aggregate(aggregateParams);

      if (!isMyTeamMember.length) {
        throw new GeneralError(locale("NOT_TEAM_MEMEBER"), 400);
      }
      where.userId = userId;
      leaveWhere.userId = userId;
    } else {
      where.userId = user._id;
      leaveWhere.userId = user._id;
    }

    if (req.query.projectId) {
      const projectId = mongoose.Types.ObjectId(req.query.projectId);
      where.projectId = projectId;
      leaveWhere.projectId = projectId;
    }

    const userData = await User.findOne({ _id: where.userId });
    const startDate = req.query.startDate
      ? MOMENT.utc(req.query.startDate, "YYYY-MM-DD").startOf("day")._d
      : MOMENT.utc().startOf("month")._d;
    const endDate = req.query.endDate
      ? MOMENT.utc(req.query.endDate, "YYYY-MM-DD").endOf("day")._d
      : MOMENT.utc().endOf("month")._d;

    console.log(startDate, endDate);

    where.logDate = {
      $gte: startDate,
      $lte: endDate,
    };

    leaveWhere.leaveDate = {
      $gte: startDate,
      $lte: endDate,
    };

    return {
      logs: await FetchJiraLogsService.getLogs(where, leaveWhere),
      summary: await FetchJiraLogsService.getSummary(
        where,
        leaveWhere,
        req.query,
        userData.doj
      ),
    };
  }

  static async getLogs(where, leaveWhere) {
    const aggregateParams = [
      {
        $match: where,
      },
      {
        $project: {
          userId: 1,
          projectId: 1,
          projectName: 1,
          logDate: 1,
          timeSpentHours: 1,
          daysDeviation: 1,
          jiraProjectName: 1,
          jiraIssueUrl: 1,
          type: "present",
          duration: "full",
          logStatus: 1,
          lastActionPerformedBy: 1,
          lastActionPerformedTimestamp: 1,
          jiraTitle: 1,
        },
      },
      {
        $unionWith: {
          coll: "leaves",
          pipeline: [
            {
              $match: leaveWhere,
            },
            {
              $project: {
                logDate: "$leaveDate",
                projectId: "1",
                projectName: "1",
                timeSpentHours: 1,
                type: "$leaveType",
                userId: 1,
                duration: 1,
              },
            },
          ],
        },
      },
    ];
    return await Logs.aggregate(aggregateParams);
  }

  static async getSummary(where, leaveWhere, query, doj) {
    const { startDate } = query;
    const endDate = query.endDate;

    const leaveDays = await Leave.find(
      {
        userId: mongoose.Types.ObjectId(leaveWhere.userId),
        leaveDate: leaveWhere.leaveDate,
      },
      { _id: 0, leaveDate: 1, timeSpentHours: 1 }
    ).lean();

    const getNonWorkingDaysLeaveHours = Utils.getOffDayLeaveHours(leaveDays);
    const days = Utils.calculateBusinessDays(startDate, endDate);
    const dojDiff = Utils.calculateDOJDifferenceDays(doj, startDate);
    const totalHours =
      days * CONSTANTS.STANDARD_DAY_HOURS
        ? days * CONSTANTS.STANDARD_DAY_HOURS + getNonWorkingDaysLeaveHours
        : 1;
    const aggregateParams = [
      {
        $match: where,
      },
      {
        $project: {
          _id: 0,
          logDate: 1,
          timeSpentHours: 1,
          daysDeviation: 1,
          logStatus: 1,
          type: "log",
        },
      },
      {
        $unionWith: {
          coll: "leaves",
          pipeline: [
            {
              $match: leaveWhere,
            },
            {
              $project: {
                logDate: "$leaveDate",
                timeSpentHours: 1,
                _id: 0,
                type: "leave",
              },
            },
          ],
        },
      },
      {
        $group: {
          _id: {
            date: { $dateToString: { format: "%Y-%m-%d", date: "$logDate" } },
            type: "$type",
            logStatus: "$logStatus",
          },
          daysDeviation: { $sum: "$daysDeviation" },
          hours: {
            $sum: {
              $cond: [{ $lte: ["$timeSpentHours", 8] }, "$timeSpentHours", 8],
            },
          },
        },
      },
      {
        $group: {
          _id: null,
          punctualityDeviation: { $sum: "$daysDeviation" },
          approvedLogs: {
            $sum: {
              $cond: [{ $eq: ["$_id.logStatus", 1] }, "$hours", 0],
            },
          },
          rejectedLogs: {
            $sum: {
              $cond: [{ $eq: ["$_id.logStatus", 2] }, "$hours", 0],
            },
          },
          pendingLogs: {
            $sum: {
              $cond: [
                { $ne: ["$_id.logStatus", 1] },
                {
                  $cond: [{ $ne: ["$_id.logStatus", 2] }, "$hours", 0],
                },
                0,
              ],
            },
          },
          logs: {
            $sum: {
              $cond: [
                {
                  $eq: ["$_id.type", "log"],
                },
                "$hours",
                0,
              ],
            },
          },
          leaves: {
            $sum: {
              $cond: [
                {
                  $eq: ["$_id.type", "leave"],
                },
                "$hours",
                0,
              ],
            },
          },
        },
      },
      {
        $project: {
          punctualityDeviation: "$punctualityDeviation",
          totalHours: "$logs",
          approvedLogs: "$approvedLogs",
          rejectedLogs: "$rejectedLogs",
          pendingLogs: "$pendingLogs",
          logDeviation: {
            $round: [
              {
                $multiply: [
                  {
                    $divide: [
                      {
                        $subtract: [
                          {
                            $subtract: [
                              {
                                $subtract: [
                                  totalHours,
                                  { $multiply: [dojDiff, 8] },
                                ],
                              },
                              "$leaves",
                            ],
                          },
                          "$logs",
                        ],
                      },
                      {
                        $cond: [
                          {
                            $eq: [
                              {
                                $subtract: [
                                  {
                                    $subtract: [
                                      totalHours,
                                      { $multiply: [dojDiff, 8] },
                                    ],
                                  },
                                  "$leaves",
                                ],
                              },
                              0,
                            ],
                          },
                          1,
                          {
                            $subtract: [
                              {
                                $subtract: [
                                  totalHours,
                                  { $multiply: [dojDiff, 8] },
                                ],
                              },
                              "$leaves",
                            ],
                          },
                        ],
                      },
                    ],
                  },
                  100,
                ],
              },
              2,
            ],
          },
        },
      },
    ];

    const defaultValue = {
      _id: null,
      punctualityDeviation: 0,
      totalHours: 0,
      approvedLogs: 0,
      rejectedLogs: 0,
      pendingLogs: 0,
      logDeviation: 100,
    };
    const summary = await Logs.aggregate(aggregateParams);
    return summary.length ? summary[0] : defaultValue;
  }
}

module.exports = FetchJiraLogsService;
