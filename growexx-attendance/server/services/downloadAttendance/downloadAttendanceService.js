const mongoose = require("mongoose");
const Logs = require("../../models/logs.model");
const Project = require("../../models/project.model");
const BusinessUnit = require("../../models/bu.model");
const { parse } = require("json2csv");

/**
 * Class represents services for download user attendance.
 */
class DownloadJiraLogsService {
  /**
   * @desc This function is being used to download user attendance
   * <AUTHOR>
   * @since 25/03/2021
   * @param {Object} req Request
   * @param {Object} user Logged in user details
   * @param {Object} locale Locale passed from request
   * @return {Object} response Success response
   */
  static async downloadUserAttendance(req, user) {
    const where = {};
    let buDetails = "";
    if (user.role === CONSTANTS.ROLE.BU) {
      buDetails = await BusinessUnit.findOne(
        { userId: user._id },
        { _id: 1 }
      ).lean();
    }
    const isUserManager = await Project.countDocuments({
      $or: [
        { pmUser: { $eq: user._id } },
        { reviewManager: { $eq: user._id } },
        { businessUnitId: { $eq: buDetails?._id } },
      ],
    });

    if (user.role === CONSTANTS.ROLE.ADMIN || isUserManager) {
      if (req.query.userId) {
        where.userId = mongoose.Types.ObjectId(req.query.userId);
      }
    } else {
      where.userId = user._id;
    }

    where.logDate = {
      $gte: req.query.startDate
        ? MOMENT(req.query.startDate).startOf("day")._d
        : MOMENT().startOf("month")._d,
      $lte: req.query.endDate
        ? MOMENT(req.query.endDate).endOf("day")._d
        : MOMENT().endOf("month")._d,
    };

    const aggregateParams = [
      {
        $match: where,
      },
      {
        $project: {
          userId: 1,
          logDate: 1,
          timeSpentHours: 1,
        },
      },
      {
        $group: {
          _id: {
            userId: "$userId",
            date: { $dateToString: { format: "%Y-%m-%d", date: "$logDate" } },
          },
          totalHours: {
            $sum: "$timeSpentHours",
          },
        },
      },
      {
        $sort: {
          "_id.date": 1,
        },
      },
      {
        $project: {
          _id: 0,
          user: "$_id.userId",
          date: "$_id.date",
          totalHours: 1,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "userDetails",
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [{ $arrayElemAt: ["$userDetails", 0] }, "$$ROOT"],
          },
        },
      },
      {
        $project: {
          email: 1,
          employeeId: 1,
          date: 1,
          totalHours: 1,
        },
      },
      {
        $sort: {
          employeeId: 1,
        },
      },
    ];

    const logs = await Logs.aggregate(aggregateParams);
    const formattedLogs = [];
    logs.forEach((obj) => {
      const { employeeId, email, date, totalHours } = obj;
      formattedLogs.push({ employeeId, email, date, totalHours });
    });
    const fields = ["employeeId", "email", "date", "totalHours"];
    const opts = { fields, quote: "" };
    const csvData = parse(formattedLogs, opts);

    return {
      headers: [
        {
          key: "Content-Type",
          value: "text/csv",
        },
        {
          key: "Content-Disposition",
          value: "attachment; filename=attendance.csv",
        },
      ],
      data: csvData,
    };
  }
}

module.exports = DownloadJiraLogsService;
