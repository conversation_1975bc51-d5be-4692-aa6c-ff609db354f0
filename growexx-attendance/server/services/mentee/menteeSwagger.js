/**
 * @name mentee swagger
 * <AUTHOR>
 */
const message = require("../../locales/en.json");

module.exports = (swaggerJson) => {
  // Legacy endpoint
  swaggerJson.paths["/user/mentees"] = {
    get: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["User"],
      summary: "Get mentees for the authenticated user (legacy endpoint)",
      description: "Returns a list of mentees for the authenticated user",
      parameters: [
        {
          in: "query",
          name: "_id",
          description: "Mentor ID (optional)",
          required: false,
          schema: {
            type: "string",
          },
        },
        {
          in: "query",
          name: "page",
          description: "Page number for pagination",
          required: false,
          schema: {
            type: "integer",
            default: 1,
          },
        },
        {
          in: "query",
          name: "limit",
          description: "Number of items per page",
          required: false,
          schema: {
            type: "integer",
            default: 10,
          },
        },
        {
          in: "query",
          name: "sort",
          description: "Sort order (1 for ascending, -1 for descending)",
          required: false,
          schema: {
            type: "integer",
            enum: [1, -1],
            default: -1,
          },
        },
        {
          in: "query",
          name: "sortBy",
          description: "Field to sort by",
          required: false,
          schema: {
            type: "string",
            enum: ["employeeId", "name", "email", "menteeId"],
            default: "menteeId",
          },
        },
        {
          in: "query",
          name: "name",
          description: "Search text to filter mentees by name or email",
          required: false,
          schema: {
            type: "string",
          },
        },
        {
          in: "query",
          name: "isActive",
          description: "Filter by active status (1 for active, 0 for inactive)",
          required: false,
          schema: {
            type: "integer",
            enum: [0, 1],
          },
        },
      ],
      responses: {
        200: {
          description: "Success",
          schema: {
            $ref: "#/definitions/successGetMentees",
          },
        },
        400: {
          description: "Bad Request",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
        401: {
          description: "Unauthorized",
          schema: {
            $ref: "#/definitions/unauthorisedAccess",
          },
        },
        500: {
          description: "Internal Server Error",
          schema: {
            $ref: "#/definitions/unexpextedError",
          },
        },
      },
    },
  };

  // New paginated list endpoint
  swaggerJson.paths["/user/mentees/list"] = {
    get: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["User"],
      summary: "Get paginated list of mentees with filtering and sorting",
      description:
        "Returns a paginated list of mentees with filtering and sorting options",
      parameters: [
        {
          in: "query",
          name: "page",
          description: "Page number for pagination",
          required: false,
          schema: {
            type: "integer",
            default: 1,
          },
        },
        {
          in: "query",
          name: "limit",
          description: "Number of items per page",
          required: false,
          schema: {
            type: "integer",
            default: 10,
          },
        },
        {
          in: "query",
          name: "sort",
          description: "Sort order (1 for ascending, -1 for descending)",
          required: false,
          schema: {
            type: "integer",
            enum: [1, -1],
            default: -1,
          },
        },
        {
          in: "query",
          name: "sortBy",
          description: "Field to sort by",
          required: false,
          schema: {
            type: "string",
            enum: ["employeeId", "name", "email", "menteeId", "designation"],
            default: "menteeId",
          },
        },
        {
          in: "query",
          name: "name",
          description: "Search text to filter mentees by name or email",
          required: false,
          schema: {
            type: "string",
          },
        },
        {
          in: "query",
          name: "isActive",
          description: "Filter by active status (1 for active, 0 for inactive)",
          required: false,
          schema: {
            type: "integer",
            enum: [0, 1],
          },
        },
      ],
      responses: {
        200: {
          description: "Success",
          schema: {
            $ref: "#/definitions/successGetPaginatedMentees",
          },
        },
        400: {
          description: "Bad Request",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
        401: {
          description: "Unauthorized",
          schema: {
            $ref: "#/definitions/unauthorisedAccess",
          },
        },
        500: {
          description: "Internal Server Error",
          schema: {
            $ref: "#/definitions/unexpextedError",
          },
        },
      },
    },
  };

  // Get mentee by ID endpoint
  swaggerJson.paths["/user/mentees/{id}"] = {
    get: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["User"],
      summary: "Get a specific mentee by ID",
      description: "Returns details of a specific mentee",
      parameters: [
        {
          in: "path",
          name: "id",
          description: "Mentee ID",
          required: true,
          schema: {
            type: "string",
          },
        },
      ],
      responses: {
        200: {
          description: "Success",
          schema: {
            $ref: "#/definitions/successGetMentee",
          },
        },
        400: {
          description: "Bad Request",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
        401: {
          description: "Unauthorized",
          schema: {
            $ref: "#/definitions/unauthorisedAccess",
          },
        },
        404: {
          description: "Not Found",
          schema: {
            $ref: "#/definitions/notFoundError",
          },
        },
        500: {
          description: "Internal Server Error",
          schema: {
            $ref: "#/definitions/unexpextedError",
          },
        },
      },
    },
    put: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["User"],
      summary: "Update a mentee",
      description: "Update details of a specific mentee",
      parameters: [
        {
          in: "path",
          name: "id",
          description: "Mentee ID",
          required: true,
          schema: {
            type: "string",
          },
        },
        {
          in: "body",
          name: "body",
          description: "Mentee data",
          required: true,
          schema: {
            type: "object",
            properties: {
              firstName: {
                type: "string",
                example: "John",
              },
              lastName: {
                type: "string",
                example: "Doe",
              },
              email: {
                type: "string",
                example: "<EMAIL>",
              },
              skills: {
                type: "array",
                items: {
                  type: "string",
                },
                example: ["JavaScript", "React"],
              },
              label: {
                type: "array",
                items: {
                  type: "string",
                },
                example: ["Developer", "Frontend"],
              },
              isActive: {
                type: "integer",
                enum: [0, 1],
                example: 1,
              },
              designation: {
                type: "string",
                example: "Software Engineer",
              },
              level: {
                type: "string",
                example: "Senior",
              },
            },
          },
        },
      ],
      responses: {
        200: {
          description: "Success",
          schema: {
            $ref: "#/definitions/successUpdateMentee",
          },
        },
        400: {
          description: "Bad Request",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
        401: {
          description: "Unauthorized",
          schema: {
            $ref: "#/definitions/unauthorisedAccess",
          },
        },
        404: {
          description: "Not Found",
          schema: {
            $ref: "#/definitions/notFoundError",
          },
        },
        500: {
          description: "Internal Server Error",
          schema: {
            $ref: "#/definitions/unexpextedError",
          },
        },
      },
    },
  };

  // Add the new endpoint for assigning mentees to a mentor
  swaggerJson.paths["/user/mentees/assign"] = {
    post: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["User"],
      summary: "Assign multiple mentees to a mentor",
      description:
        "Assigns multiple mentees to a specified mentor. Duplicates are automatically ignored. If a mentee is already assigned to the same mentor, they are skipped. If any mentee is already assigned to a different mentor, the entire operation fails.",
      parameters: [
        {
          in: "body",
          name: "body",
          description: "Assignment parameters",
          required: true,
          schema: {
            $ref: "#/definitions/assignMenteesRequest",
          },
        },
      ],
      responses: {
        200: {
          description: "Mentees successfully assigned",
          schema: {
            $ref: "#/definitions/assignMenteesResponse",
          },
        },
        400: {
          description: "Bad Request",
          schema: {
            $ref: "#/definitions/errorResponse",
          },
        },
        401: {
          description: "Unauthorized",
          schema: {
            $ref: "#/definitions/errorResponse",
          },
        },
        500: {
          description: "Internal Server Error",
          schema: {
            $ref: "#/definitions/errorResponse",
          },
        },
      },
    },
  };

  // Add the request schema for assigning mentees
  swaggerJson.definitions.assignMenteesRequest = {
    type: "object",
    required: ["mentorId", "menteeIds"],
    properties: {
      mentorId: {
        type: "string",
        description: "ID of the mentor to assign mentees to",
      },
      menteeIds: {
        type: "array",
        description: "Array of mentee IDs to assign to the mentor",
        items: {
          type: "string",
        },
      },
    },
  };

  // Add the response schema for assigning mentees
  swaggerJson.definitions.assignMenteesResponse = {
    type: "object",
    properties: {
      status: {
        type: "number",
        example: 1,
      },
      data: {
        type: "object",
        properties: {
          mentorId: {
            type: "string",
            description: "ID of the mentor",
          },
          assignedCount: {
            type: "number",
            description: "Number of mentees successfully assigned",
          },
          totalMentees: {
            type: "number",
            description: "Total number of unique mentees in the request",
          },
          skippedDuplicates: {
            type: "number",
            description: "Number of duplicate mentee IDs that were skipped",
          },
          skippedAlreadyAssigned: {
            type: "number",
            description:
              "Number of mentees already assigned to this mentor that were skipped",
          },
        },
      },
      message: {
        type: "string",
        example: message.MENTEES_ASSIGNED,
      },
    },
  };

  // Add error response schema
  swaggerJson.definitions.errorResponse = {
    type: "object",
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: message.ERROR_MSG,
      },
    },
  };

  // Add specific error response for mentee already assigned to another mentor
  swaggerJson.definitions.menteeAlreadyAssignedError = {
    type: "object",
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: "MENTEE_ALREADY_ASSIGNED_TO_OTHER",
      },
    },
  };

  // Add endpoint for processing mentor-mentee assignments from JSON data
  swaggerJson.paths["/user/mentees/process-assignments"] = {
    post: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["User"],
      summary: "Process mentor-mentee assignments from JSON data",
      description:
        "Process mentor-mentee assignments directly from JSON data in the request body. Can overwrite existing mentor assignments. Will continue processing valid mentee emails even if some mentees in a row are out of the organization.",
      parameters: [
        {
          in: "query",
          name: "overwriteExisting",
          description:
            "If true, existing mentor assignments will be overwritten (default: true)",
          required: false,
          type: "boolean",
          default: true,
        },
        {
          in: "body",
          name: "body",
          description: "Mentor-mentee assignment data",
          required: true,
          schema: {
            type: "array",
            items: {
              type: "object",
              properties: {
                "Mentor email": {
                  type: "string",
                  description: "Email address of the mentor",
                },
                "Mentee email": {
                  type: "array",
                  items: {
                    type: "string",
                  },
                  description: "Array of mentee email addresses",
                },
              },
              required: ["Mentor email", "Mentee email"],
            },
          },
          example: [
            {
              "Mentor email": "<EMAIL>",
              "Mentee email": [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>", // Example of an external user
              ],
            },
            {
              "Mentor email": "<EMAIL>",
              "Mentee email": ["<EMAIL>"],
            },
            {
              "Mentor email": "<EMAIL>",
              "Mentee email": ["<EMAIL>"],
            },
          ],
        },
      ],
      responses: {
        200: {
          description: "Mentor-mentee assignments processed successfully",
          schema: {
            type: "object",
            properties: {
              status: {
                type: "number",
                example: 1,
              },
              data: {
                type: "object",
                properties: {
                  totalRows: {
                    type: "number",
                    example: 3,
                    description: "Total number of rows processed",
                  },
                  successfulAssignments: {
                    type: "number",
                    example: 2,
                    description:
                      "Number of successful mentor-mentee assignments",
                  },
                  skippedRows: {
                    type: "number",
                    example: 1,
                    description: "Number of rows skipped due to errors",
                  },
                  partiallyProcessedRows: {
                    type: "number",
                    example: 1,
                    description:
                      "Number of rows where some mentees were valid and processed, while others were invalid and skipped",
                  },
                  skippedEmails: {
                    type: "array",
                    items: {
                      type: "string",
                    },
                    example: [
                      "<EMAIL>",
                      "<EMAIL>",
                    ],
                    description: "List of emails that were skipped",
                  },
                  errors: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        email: {
                          type: "string",
                          description: "Email address that caused an error",
                        },
                        reason: {
                          type: "string",
                          description: "Reason for the error",
                        },
                      },
                    },
                    example: [
                      {
                        email: "<EMAIL>",
                        reason:
                          "Mentee email not found in organization (for mentor: <EMAIL>)",
                      },
                      {
                        email: "<EMAIL>",
                        reason:
                          "Mentor email not found in organization: <EMAIL>",
                      },
                    ],
                    description: "List of errors encountered during processing",
                  },
                  reassignedMentees: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        menteeId: {
                          type: "string",
                          example: "60d5f8b6e7d9f52a3c9d4e7f",
                        },
                        menteeEmail: {
                          type: "string",
                          example: "<EMAIL>",
                        },
                        menteeFullName: {
                          type: "string",
                          example: "Milan Manvar",
                        },
                        previousMentorId: {
                          type: "string",
                          example: "60d5f8b6e7d9f52a3c9d4e8a",
                        },
                        newMentorId: {
                          type: "string",
                          example: "60d5f8b6e7d9f52a3c9d4e8b",
                        },
                      },
                    },
                    description:
                      "List of mentees who were reassigned from one mentor to another",
                  },
                  summary: {
                    type: "string",
                    example:
                      "Processed 3 rows: 2 successful assignments, 1 skipped. 2 emails skipped. 1 rows were partially processed (some mentees were valid, some were not).",
                    description: "Summary of the processing results",
                  },
                },
              },
              message: {
                type: "string",
                example: "Mentor-mentee assignments processed successfully",
              },
            },
            example: {
              status: 1,
              data: {
                totalRows: 3,
                successfulAssignments: 2,
                skippedRows: 1,
                partiallyProcessedRows: 1,
                skippedEmails: [
                  "<EMAIL>",
                  "<EMAIL>",
                ],
                errors: [
                  {
                    email: "<EMAIL>",
                    reason:
                      "Mentee email not found in organization (for mentor: <EMAIL>)",
                  },
                  {
                    email: "<EMAIL>",
                    reason:
                      "Mentor email not found in organization: <EMAIL>",
                  },
                ],
                summary:
                  "Processed 3 rows: 2 successful assignments, 1 skipped. 2 emails skipped. 1 rows were partially processed (some mentees were valid, some were not).",
              },
              message: "Mentor-mentee assignments processed successfully",
            },
          },
        },
        400: {
          description: "Bad request - invalid input or logic error",
          schema: {
            $ref: "#/definitions/errorResponse",
          },
        },
        401: {
          description: "Unauthorized",
          schema: {
            $ref: "#/definitions/errorResponse",
          },
        },
        403: {
          description: "Forbidden",
          schema: {
            $ref: "#/definitions/errorResponse",
          },
        },
        500: {
          description: "Internal server error",
          schema: {
            $ref: "#/definitions/errorResponse",
          },
        },
      },
    },
  };

  // Add endpoint for getting mentees grouped by their mentors
  swaggerJson.paths["/user/mentees/grouped-by-mentor"] = {
    get: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["User"],
      summary: "Get all mentees grouped by their mentors",
      description: "Returns a list of mentors with their assigned mentees",
      responses: {
        200: {
          description: "Mentees successfully retrieved",
          schema: {
            type: "object",
            properties: {
              status: {
                type: "number",
                example: 1,
              },
              data: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: {
                      type: "string",
                      description: "Mentor ID",
                      example: "60d5f8b6e7d9f52a3c9d4e8a",
                    },
                    mentor: {
                      type: "string",
                      description: "Mentor's full name",
                      example: "John Doe",
                    },
                    department: {
                      type: "string",
                      description: "Mentor's department/business unit",
                      example: "Engineering",
                    },
                    empId: {
                      type: "string",
                      description: "Mentor's employee ID",
                      example: "EMP123",
                    },
                    mentees: {
                      type: "array",
                      description: "List of mentees assigned to this mentor",
                      items: {
                        type: "object",
                        properties: {
                          id: {
                            type: "string",
                            description: "Mentee ID",
                            example: "60d5f8b6e7d9f52a3c9d4e7f",
                          },
                          name: {
                            type: "string",
                            description: "Mentee's full name",
                            example: "Jane Smith",
                          },
                          email: {
                            type: "string",
                            description: "Mentee's email",
                            example: "<EMAIL>",
                          },
                          designation: {
                            type: "string",
                            description: "Mentee's job designation",
                            example: "Junior Software Engineer",
                          },
                          empId: {
                            type: "string",
                            description: "Mentee's employee ID",
                            example: "EMP456",
                          },
                        },
                      },
                    },
                  },
                },
              },
              message: {
                type: "string",
                example: "Success",
              },
            },
          },
          examples: {
            "application/json": {
              status: 1,
              data: [
                {
                  id: "60d5f8b6e7d9f52a3c9d4e8a",
                  mentor: "John Doe",
                  department: "Engineering",
                  empId: "EMP123",
                  mentees: [
                    {
                      id: "60d5f8b6e7d9f52a3c9d4e7f",
                      name: "Jane Smith",
                      email: "<EMAIL>",
                      designation: "Junior Software Engineer",
                      empId: "EMP456",
                    },
                    {
                      id: "60d5f8b6e7d9f52a3c9d4e80",
                      name: "Bob Johnson",
                      email: "<EMAIL>",
                      designation: "Junior Software Engineer",
                      empId: "EMP789",
                    },
                  ],
                },
                {
                  id: "60d5f8b6e7d9f52a3c9d4e8b",
                  mentor: "Alice Williams",
                  department: "Design",
                  empId: "EMP012",
                  mentees: [
                    {
                      id: "60d5f8b6e7d9f52a3c9d4e81",
                      name: "Charlie Brown",
                      email: "<EMAIL>",
                      designation: "Junior Designer",
                      empId: "EMP345",
                    },
                  ],
                },
              ],
              message: "Success",
            },
          },
        },
        500: {
          description: "Internal Server Error",
          schema: {
            $ref: "#/definitions/errorResponse",
          },
        },
      },
    },
  };

  // Add schema definitions
  swaggerJson.definitions.successGetMentees = {
    type: "object",
    properties: {
      success: {
        type: "boolean",
        example: true,
      },
      data: {
        type: "object",
        properties: {
          docs: {
            type: "array",
            items: {
              type: "object",
              properties: {
                _id: {
                  type: "string",
                  example: "60d21b4667d0d8992e610c85",
                },
                employeeId: {
                  type: "string",
                  example: "GE-12345",
                },
                firstName: {
                  type: "string",
                  example: "John",
                },
                lastName: {
                  type: "string",
                  example: "Doe",
                },
                email: {
                  type: "string",
                  example: "<EMAIL>",
                },
                label: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                  example: ["Developer", "Frontend"],
                },
                skills: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                  example: ["JavaScript", "React"],
                },
                designation: {
                  type: "string",
                  example: "Software Engineer",
                },
                isActive: {
                  type: "integer",
                  example: 1,
                },
              },
            },
          },
          totalDocs: {
            type: "integer",
            example: 25,
          },
          limit: {
            type: "integer",
            example: 10,
          },
          page: {
            type: "integer",
            example: 1,
          },
          totalPages: {
            type: "integer",
            example: 3,
          },
          hasPrevPage: {
            type: "boolean",
            example: false,
          },
          hasNextPage: {
            type: "boolean",
            example: true,
          },
        },
      },
      message: {
        type: "string",
        example: message.MENTEES_FETCHED,
      },
    },
  };

  swaggerJson.definitions.successGetPaginatedMentees = {
    type: "object",
    properties: {
      success: {
        type: "boolean",
        example: true,
      },
      data: {
        type: "object",
        properties: {
          docs: {
            type: "array",
            items: {
              type: "object",
              properties: {
                _id: {
                  type: "string",
                  example: "60d21b4667d0d8992e610c85",
                },
                employeeId: {
                  type: "string",
                  example: "GE-12345",
                },
                firstName: {
                  type: "string",
                  example: "John",
                },
                lastName: {
                  type: "string",
                  example: "Doe",
                },
                email: {
                  type: "string",
                  example: "<EMAIL>",
                },
                label: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                  example: ["Developer", "Frontend"],
                },
                name: {
                  type: "string",
                  example: "John Doe",
                },
              },
            },
          },
          totalDocs: {
            type: "integer",
            example: 25,
          },
          limit: {
            type: "integer",
            example: 10,
          },
          page: {
            type: "integer",
            example: 1,
          },
          totalPages: {
            type: "integer",
            example: 3,
          },
          prevPage: {
            type: "integer",
            example: null,
          },
          nextPage: {
            type: "integer",
            example: 2,
          },
          pagingCounter: {
            type: "integer",
            example: 1,
          },
          hasPrevPage: {
            type: "boolean",
            example: false,
          },
          hasNextPage: {
            type: "boolean",
            example: true,
          },
        },
      },
      message: {
        type: "string",
        example: message.MENTEES_FETCHED,
      },
    },
  };

  swaggerJson.definitions.successGetMentee = {
    type: "object",
    properties: {
      success: {
        type: "boolean",
        example: true,
      },
      data: {
        type: "object",
        properties: {
          _id: {
            type: "string",
            example: "60d21b4667d0d8992e610c85",
          },
          employeeId: {
            type: "string",
            example: "GE-12345",
          },
          firstName: {
            type: "string",
            example: "John",
          },
          lastName: {
            type: "string",
            example: "Doe",
          },
          email: {
            type: "string",
            example: "<EMAIL>",
          },
          label: {
            type: "array",
            items: {
              type: "string",
            },
            example: ["Developer", "Frontend"],
          },
          skills: {
            type: "array",
            items: {
              type: "string",
            },
            example: ["JavaScript", "React"],
          },
          designation: {
            type: "string",
            example: "Software Engineer",
          },
          isActive: {
            type: "integer",
            example: 1,
          },
        },
      },
      message: {
        type: "string",
        example: message.MENTEE_FETCHED,
      },
    },
  };

  swaggerJson.definitions.successUpdateMentee = {
    type: "object",
    properties: {
      success: {
        type: "boolean",
        example: true,
      },
      data: {
        type: "object",
        properties: {
          _id: {
            type: "string",
            example: "60d21b4667d0d8992e610c85",
          },
          employeeId: {
            type: "string",
            example: "GE-12345",
          },
          firstName: {
            type: "string",
            example: "John",
          },
          lastName: {
            type: "string",
            example: "Doe",
          },
          email: {
            type: "string",
            example: "<EMAIL>",
          },
          label: {
            type: "array",
            items: {
              type: "string",
            },
            example: ["Developer", "Frontend"],
          },
          skills: {
            type: "array",
            items: {
              type: "string",
            },
            example: ["JavaScript", "React"],
          },
          designation: {
            type: "string",
            example: "Software Engineer",
          },
          isActive: {
            type: "integer",
            example: 1,
          },
        },
      },
      message: {
        type: "string",
        example: message.MENTEE_UPDATED,
      },
    },
  };

  swaggerJson.definitions.notFoundError = {
    type: "object",
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: "Mentee not found",
      },
    },
  };

  swaggerJson.definitions.validationError = {
    type: "object",
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: message.INVALID_REQUEST,
      },
    },
  };

  swaggerJson.definitions.unauthorisedAccess = {
    type: "object",
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: message.ACCESS_DENIED,
      },
    },
  };

  swaggerJson.definitions.unexpextedError = {
    type: "object",
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: message.ERROR_MSG,
      },
    },
  };

  return swaggerJson;
};
