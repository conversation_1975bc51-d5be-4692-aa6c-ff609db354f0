/**
 * @name mentee validator
 * <AUTHOR>
 */
const validation = require("../../util/validation");
const GeneralError = require("../../util/GeneralError");

/**
 * Class represents validator for mentee
 */
class MenteeValidator extends validation {
  constructor(body, locale) {
    super(locale);
    this.body = body;
  }

  /**
   * @desc This function is being used to validate mentor id
   * <AUTHOR>
   * @param {string} mentorId mentor id
   */
  validate(mentorId) {
    if (!mentorId) {
      throw new GeneralError("Invalid request parameters", 400);
    }
    try {
      super.checkValidMongoId(mentorId, "mentorId");
    } catch (error) {
      throw new GeneralError("Invalid request parameters", 400);
    }
  }

  /**
   * @desc This function is being used to validate assign mentees request
   * <AUTHOR>
   * @param {Object} data Request data containing mentorId and menteeIds
   */
  validateAssignMentees(data) {
    const { mentorId, menteeIds } = data;

    // Validate mentor ID
    if (!mentorId) {
      throw new GeneralError("Invalid request parameters", 400);
    }
    try {
      super.checkValidMongoId(mentorId, "mentorId");
    } catch (error) {
      throw new GeneralError("Invalid request parameters", 400);
    }

    // Validate mentee IDs
    if (!menteeIds || !Array.isArray(menteeIds) || menteeIds.length === 0) {
      throw new GeneralError("Invalid request parameters", 400);
    }

    // Validate each mentee ID
    menteeIds.forEach((id, index) => {
      if (!id) {
        throw new GeneralError("Invalid request parameters", 400);
      }
      try {
        super.checkValidMongoId(id, `menteeIds[${index}]`);
      } catch (error) {
        throw new GeneralError("Invalid request parameters", 400);
      }
    });

    // Validate that mentor and mentee IDs are not the same
    if (menteeIds.includes(mentorId)) {
      throw new GeneralError("MENTOR_CANNOT_BE_MENTEE", 400);
    }
  }

  /**
   * @desc This function is being used to validate file upload for mentor-mentee assignments
   * <AUTHOR>
   * @param {Object} file The uploaded file object
   */
  validateFileUpload(file) {
    // Check if file exists
    if (!file) {
      throw new GeneralError("FILE_NOT_PROVIDED", 400);
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      throw new GeneralError("FILE_TOO_LARGE", 400);
    }

    // Check file type (must be JSON)
    if (file.mimetype !== "application/json") {
      throw new GeneralError("INVALID_FILE_TYPE", 400);
    }

    // Check if file has content
    if (!file.buffer || file.buffer.length === 0) {
      throw new GeneralError("EMPTY_FILE", 400);
    }
  }
}

module.exports = MenteeValidator;
