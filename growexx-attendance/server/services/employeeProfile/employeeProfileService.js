/**
 * Class represents services for employee profile data.
 */
const User = require('../../models/user.model');
const moment = require('moment');
const AWS = require('aws-sdk');
const Utils = require('../../util/utilFunctions');
const { PliRating } = require('../../models/pliRating.model');
const ProjectSprintDataService = require('../getProjectSprintData/projectSprintDataService');

// Configure AWS with environment variables or defaults
const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || 'your_access_key',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || 'your_secret_key',
    region: process.env.AWS_REGION || 'us-east-1'
});

// Add bucket name
const S3_BUCKET = process.env.AWS_S3_BUCKET || 'your-bucket-name';

class EmployeeProfileService {
    /**
   * Get employee profile data for PLI from project tracker
   * @param {string} employeeId - ID of the employee
   * @returns {object} Employee profile data
   */
    static async getEmployeeProfileForPli (employeeId) {
    // Find the employee in the database by numeric employeeId
        const employee = await User.findOne(
            { employeeId: Number(employeeId) },
            {
                _id: 1,
                firstName: 1,
                lastName: 1,
                employeeId: 1,
                businessUnit: 1,
                designation: 1,
                doj: 1,
                label: 1,
                department: 1,
                mentorId: 1
            }
        ).lean();

        if (!employee) {
            throw new Error('Employee not found');
        }

        // Ensure we have an object even if the database returns null
        const safeEmployee = employee || {};

        // Format the DOJ date
        const formattedDoj = safeEmployee.doj
            ? moment(safeEmployee.doj).format('DD-MMM-YYYY')
            : '';

        // reporting manager details is not there anywhere so currently sending blank values

        const employeeProfile = {
            menteeId: employee._id,
            name: `${employee.firstName} ${employee.lastName}`,
            empId: employee.employeeId,
            department: employee.department || employee.businessUnit || '',
            reportingManager: '',
            reportingManagerId: '',
            doj: formattedDoj,
            pliDuration: '1 Month', // Fixed as per requirements
            label: employee.label[0],
            mentorId: employee.mentorId,
            designation: ProjectSprintDataService.mapDesignationToRole(employee.designation)
        };

        return employeeProfile;
    }

    static async uploadToS3 (file) {
        try {
            if (!S3_BUCKET) {
                throw new Error('AWS S3 bucket name is not configured');
            }

            const fileExtension = file.originalname.split('.').pop();
            const uniqueId = Utils.generateUuid();
            const key = `pli-documents/${uniqueId}.${fileExtension}`;

            const params = {
                Bucket: S3_BUCKET,
                Key: key,
                Body: file.buffer,
                ContentType: file.mimetype,
                ACL: 'public-read'
            };

            console.log('Uploading to S3 with params:', {
                bucket: params.Bucket,
                key: params.Key,
                contentType: params.ContentType
            });

            const uploadResult = await s3.upload(params).promise();
            return {
                url: uploadResult.Location,
                key: uploadResult.Key
            };
        } catch (error) {
            console.error('Error uploading to S3:', error);
            throw new Error('Failed to upload file to S3: ' + error.message);
        }
    }

    static async saveFileDetails (fileDetails, employeeId) {
        try {
            // Find and update PLI rating with new file
            const result = await PliRating.findOneAndUpdate(
                { employeeId },
                {
                    $push: {
                        files: {
                            fileName: fileDetails.fileName,
                            fileUrl: fileDetails.fileUrl,
                            uploadedAt: new Date()
                        }
                    }
                },
                { new: true, upsert: true }
            );

            return result.files[result.files.length - 1];
        } catch (error) {
            console.error('Error saving file details:', error);
            throw error;
        }
    }

    static async getFilesByEmployeeId (employeeId) {
        try {
            const pliRating = await PliRating.findOne({ employeeId }, { files: 1 });
            return pliRating?.files || [];
        } catch (error) {
            console.error('Error fetching files:', error);
            throw error;
        }
    }

    static async getPliFiles (employeeId) {
        const user = await User.findOne(
            { employeeId: Number(employeeId) },
            { pliFiles: 1 }
        ).lean();

        return user?.pliFiles || [];
    }
}

module.exports = EmployeeProfileService;
