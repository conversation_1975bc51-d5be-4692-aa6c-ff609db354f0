const message = require('../../locales/en');

module.exports = swaggerJson => {
    swaggerJson.paths['/pli/employee-profile/{employeeId}'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: [
                'PLI'
            ],
            description: 'Get employee profile data for PLI (name, emp ID, department, reporting manager & ID, DOJ, PLI duration)',
            summary: 'Get employee profile data for PLI',
            parameters: [
                {
                    in: 'path',
                    name: 'employeeId',
                    description: 'Employee ID (numeric)',
                    required: true,
                    type: 'string'
                }
            ],
            responses: {
                200: {
                    description: 'Success',
                    schema: {
                        $ref: '#/definitions/successGetEmployeeProfile'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Something went wrong. Try again.',
                    schema: {
                        $ref: '#/definitions/unexpextedError'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.successGetEmployeeProfile = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            message: {
                example: message.SUCCESS
            },
            data: {
                type: 'object',
                properties: {
                    name: {
                        type: 'string',
                        example: 'John Doe'
                    },
                    empId: {
                        type: 'number',
                        example: 123
                    },
                    department: {
                        type: 'string',
                        example: 'Engineering'
                    },
                    reportingManager: {
                        type: 'string',
                        example: 'Jane Smith',
                        description: 'Reporting manager name, determined from project team or organizational structure'
                    },
                    reportingManagerId: {
                        type: 'string',
                        example: '456',
                        description: 'Reporting manager employee ID'
                    },
                    doj: {
                        type: 'string',
                        example: '01-01-2020',
                        description: 'Date of joining in DD-MM-YYYY format'
                    },
                    pliDuration: {
                        type: 'string',
                        example: '1 Month'
                    }
                }
            }
        }
    };

    return swaggerJson;
};
