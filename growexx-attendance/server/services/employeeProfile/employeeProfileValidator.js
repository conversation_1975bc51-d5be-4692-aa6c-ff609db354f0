/**
 * Class represents validations for employee profile data.
 */
const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';

/**
 * Class represents validations for employee profile
 */
class EmployeeProfileValidator extends validation {
    constructor (locale) {
        super(locale);
        this.__ = locale;
    }

    /**
     * @desc This function is being used to validate employee ID
     * <AUTHOR>
     * @since 01/01/2023
     * @param {string|number} employeeId Employee ID
     */
    employeeId (employeeId) {
        if (!employeeId) {
            throw new GeneralError(this.__(REQUIRED, 'Employee ID'), 400);
        }

        // Convert to number and check if it's a valid number
        const empIdNum = Number(employeeId);
        if (isNaN(empIdNum) || empIdNum <= 0 || !Number.isInteger(empIdNum)) {
            throw new GeneralError(this.__(INVALID, 'Employee ID'), 400);
        }
    }

    /**
     * @desc This function validates all required fields for the employee profile
     * <AUTHOR>
     * @since 01/01/2023
     * @param {Object} params Request parameters
     */
    validateGetEmployeeProfileParams (params) {
        this.employeeId(params.employeeId);
    }
}

module.exports = EmployeeProfileValidator;
