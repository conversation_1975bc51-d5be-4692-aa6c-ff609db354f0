/**
 * This controller is for employee profile management
 */
const EmployeeProfileService = require("./employeeProfileService");
const EmployeeProfileValidator = require("./employeeProfileValidator");
const Utils = require("../../util/utilFunctions");

/**
 * Class represents controller for employee profile
 */
class EmployeeProfileController {
  /**
   * @param {object} req - Request object
   * @param {object} res - Response object
   * @description - Get employee profile data for PLI
   */
  static async getEmployeeProfileForPli(req, res) {
    try {
      const { employeeId } = req.params;
      // Validate employee ID before processing
      const validator = new EmployeeProfileValidator(res.__);
      validator.validateGetEmployeeProfileParams(req.params);

      const data =
        await EmployeeProfileService.getEmployeeProfileForPli(employeeId);
      Utils.sendResponse(null, data, res, res.__("SUCCESS"));
    } catch (error) {
      if (error.message === "Employee not found") {
        Utils.sendResponse(
          { message: res.__("EMPLOYEE_NOT_FOUND"), statusCode: 400 },
          null,
          res
        );
      } else {
        Utils.sendResponse(error, null, res);
      }
    }
  }

  static async uploadPliFile(req, res) {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
      }

      const { employeeId } = req.params;

      // First upload to S3
      const s3Result = await EmployeeProfileService.uploadToS3(req.file);

      // Then save file details
      const fileDetails = {
        fileName: req.file.originalname,
        fileUrl: s3Result.url,
      };

      const savedFile = await EmployeeProfileService.saveFileDetails(
        fileDetails,
        employeeId
      );

      res.status(200).json({
        success: true,
        message: "File uploaded successfully",
        file: savedFile,
      });
    } catch (error) {
      console.error("Error uploading file:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Error uploading file",
      });
    }
  }

  static async getPliFiles(req, res) {
    try {
      const { employeeId } = req.params;
      const files =
        await EmployeeProfileService.getFilesByEmployeeId(employeeId);
      res.status(200).json(files);
    } catch (error) {
      console.error("Error fetching files:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Error fetching files",
      });
    }
  }
}

module.exports = EmployeeProfileController;
