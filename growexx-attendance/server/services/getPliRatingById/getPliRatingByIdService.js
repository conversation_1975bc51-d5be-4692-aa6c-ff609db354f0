/**
 * Service to get PLI rating by ID with populated names
 */
const PLIRating = require('../../models/pliRating.model');
const User = require('../../models/user.model');
const Project = require('../../models/project.model');
const PLIParameters = require('../../models/pliParameters.model');
const GeneralError = require('../../util/GeneralError');
const ProjectSprintDataService = require('../getProjectSprintData/projectSprintDataService');

class GetPliRatingByIdService {
    /**
     * @desc This function gets the PLI parameter document
     * <AUTHOR>
     * @since 21/05/2025
     * @returns {Promise<Object>} PLI parameter document
     */
    static async getPliParameterDoc () {
        return await PLIParameters.findOne({});
    }

    /**
     * @desc This function gets the weightage for a child parameter
     * <AUTHOR>
     * @since 21/05/2025
     * @param {String} designation Employee designation
     * @param {String} projectType Project type (Fixed/Dedicated)
     * @param {String} parentParameter Parent parameter name
     * @param {String} childParameterId Child parameter ID/name
     * @returns {Number} Weightage of the child parameter
     */
    static async getChildParameterWeighatge (designation, projectType, parentParameter, childParameterId) {
        try {
            // Handle case where any input parameter is undefined
            if (!designation || !projectType || !parentParameter || !childParameterId) {
                return 0; // Default weightage
            }

            const pliParameterDoc = await this.getPliParameterDoc();

            // Check if PLI parameter document exists
            if (!pliParameterDoc || !pliParameterDoc.roleParameters) {
                return 0;
            }

            // Map designation to role
            const role = ProjectSprintDataService.mapDesignationToRole(designation);

            // Find role parameters for this role
            const roleDoc = pliParameterDoc.roleParameters.find(doc => doc.applicableRole === role);
            if (!roleDoc || !roleDoc.parameters) {
                return 0;
            }

            // Find parameter for this project type and parent parameter
            const parameterDoc = roleDoc.parameters.find(parameter =>
                parameter.parentParameter === parentParameter &&
                parameter.projectType === projectType
            );
            if (!parameterDoc || !parameterDoc.childParameters) {
                return 0;
            }

            // Find child parameter by name
            const childParameterDoc = parameterDoc.childParameters.find(child => child.name === childParameterId);
            if (!childParameterDoc) {
                return 0;
            }

            // Return the weightage, defaulting to 0 if undefined
            const weightage = childParameterDoc.weightage || 0;

            return weightage;
        } catch (error) {
            return 0; // Default weightage in case of any error
        }
    }
    /**
     * @desc This function is being used to get PLI rating by ID with populated names
     * <AUTHOR>
     * @since 16/05/2025
     * @param {Object} params - Query parameters
     * @param {string} params.pliRatingId - ID of the PLI rating
     * @param {Function} locale - Localization function
     * @returns {Object} PLI rating with populated names
     */
    static async getPliRatingById (params, locale) {
        const { pliRatingId } = params;

        // Validation is now handled by the validator in the controller

        // Find PLI rating by ID
        const pliRating = await PLIRating.findById(pliRatingId);

        // Check if PLI rating exists
        if (!pliRating) {
            throw new GeneralError(locale('PLI_RATING_NOT_FOUND'), 404);
        }

        // Create a response object with names instead of IDs
        const response = pliRating.toObject();

        // Get mentee details and replace ID with name
        if (response.menteeId) {
            const mentee = await User.findById(response.menteeId);
            if (mentee) {
                const menteeName = `${mentee.firstName} ${mentee.lastName}`.trim();
                const menteeData = {
                    name: menteeName,
                    email: mentee.email,
                    employeeId: mentee.employeeId
                };
                // Replace menteeId with mentee details
                response.mentee = menteeData;
                delete response.menteeId;
            }
        }

        // Get mentor details and replace ID with name
        if (response.mentorId) {
            const mentor = await User.findById(response.mentorId);
            if (mentor) {
                const mentorName = `${mentor.firstName} ${mentor.lastName}`.trim();
                const mentorData = {
                    name: mentorName,
                    email: mentor.email,
                    employeeId: mentor.employeeId
                };
                // Replace mentorId with mentor details
                response.mentor = mentorData;
                delete response.mentorId;
            }
        }

        // Populate project and parameter names for each project rating
        for (let i = 0; i < response.projectRatings.length; i++) {
            const projectRating = response.projectRatings[i];

            // Get project details and replace ID with name
            if (projectRating.projectId) {
                const project = await Project.findById(projectRating.projectId);
                if (project) {
                    // Create project data object
                    const projectData = {
                        name: project.projectName,
                        code: project.projectCode,
                        client: project.clientName
                    };
                    // Replace projectId with project details
                    projectRating.project = projectData;
                    delete projectRating.projectId;
                }
            }

            // Populate parameter names and replace IDs
            for (let j = 0; j < projectRating.parameterScores.length; j++) {
                const parameterScore = projectRating.parameterScores[j];

                // With the new structure, we use the projectType and parentParameter fields directly
                // instead of looking up the parameter details from the PLI parameters collection
                if (parameterScore.parameterId) {
                    // Get the single PLI parameters document
                    const pliParametersDoc = await PLIParameters.findOne({ isActive: 1 });

                    if (pliParametersDoc) {
                        // Replace parameterId with parameter details from the parameterScore itself
                        parameterScore.parameter = {
                            name: parameterScore.parentParameter,
                            type: parameterScore.projectType
                        };

                        // Remove fields we've extracted into the parameter object
                        delete parameterScore.parameterId;
                        delete parameterScore.projectType;
                        delete parameterScore.parentParameter; // Remove to avoid duplication

                        // For child scores, the childParameterId is already a string with the name
                        // Just rename the field to make it clearer
                        for (let k = 0; k < parameterScore.childScores.length; k++) {
                            const childScore = parameterScore.childScores[k];
                            childScore.childParameter = childScore.childParameterId;
                            delete childScore.childParameterId;
                            // Add the three new fields based on the PLI rating structure
                            try {
                                // 1. childParameterWeightage - Get from PLI parameters based on role
                                const pliParametersDoc = await this.getPliParameterDoc();

                                // Default to Software Engineer role if we don't have mentee info
                                let role = 'Developers'; // Default role

                                // Get the mentee details from the database again if needed
                                let userDesignation = null;
                                if (response.menteeId) {
                                    try {
                                        // Find the user by ID to get their designation
                                        const userDetails = await User.findById(response.menteeId).lean();
                                        if (userDetails && userDetails.designation) {
                                            userDesignation = userDetails.designation;
                                            // Map the designation to a role using ProjectSprintDataService
                                            role = ProjectSprintDataService.mapDesignationToRole(userDesignation);
                                        }
                                    } catch (err) {
                                        // Continue with default role if there's an error
                                    }
                                }

                                // Find the role parameters for this role
                                const roleParameters = pliParametersDoc.roleParameters.find(rp =>
                                    rp.applicableRole === role
                                );

                                if (roleParameters) {
                                    // Find the parameter set for this project type and parent parameter
                                    const parameter = roleParameters.parameters.find(p =>
                                        p.projectType === parameterScore.parameter.type &&
                                        p.parentParameter === parameterScore.parameter.name
                                    );

                                    if (parameter) {
                                        // Find the child parameter that matches this child parameter name
                                        const childParam = parameter.childParameters.find(cp =>
                                            cp.name === childScore.childParameter
                                        );

                                        // Instead of directly using childParam.weightage, call the getChildParameterWeighatge method
                                        try {
                                            childScore.childParameterWeightage = await this.getChildParameterWeighatge(
                                                userDesignation || 'Software Engineer',
                                                parameterScore.parameter.type,
                                                parameterScore.parameter.name,
                                                childScore.childParameter
                                            );

                                        } catch (weightageError) {

                                            childScore.childParameterWeightage = 0;
                                        }
                                    } else {
                                        childScore.childParameterWeightage = 0;
                                    }
                                } else {
                                    childScore.childParameterWeightage = 0;
                                }
                            } catch (error) {
                                // Use default value if there's an error
                                childScore.childParameterWeightage = 0;
                            }

                            // 2. calculation - average of all sprint scores
                            if (childScore.sprintScores && childScore.sprintScores.length > 0) {
                                // Filter out any non-numeric scores
                                const scores = childScore.sprintScores
                                    .map(sprint => Number(sprint.score))
                                    .filter(score => !isNaN(score));

                                if (scores.length > 0) {
                                    const totalScore = scores.reduce((sum, score) => sum + score, 0);
                                    childScore.calculation = Number((totalScore / scores.length).toFixed(2));
                                } else {
                                    childScore.calculation = 0;
                                }
                            } else {
                                childScore.calculation = 0;
                            }

                            // 3. weightageAverage - calculation * childParameterWeightage / 100
                            childScore.weightageAverage = Number(
                                ((childScore.calculation * childScore.childParameterWeightage)).toFixed(2)
                            );
                        }
                    }
                }
            }
        }

        return response;
    }
}

module.exports = GetPliRatingByIdService;
