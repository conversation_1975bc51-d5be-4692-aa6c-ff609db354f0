module.exports = {
  getPliRatingById: [
    {
      it: 'As a user, I should not be able to get PLI rating without providing PLI Rating ID',
      options: {
        pliRatingId: ''
      },
      status: 0,
      expectedHttpStatus: 400  // Bad Request
    },
    {
      it: 'As a user, I should not be able to get PLI rating with invalid PLI Rating ID format',
      options: {
        pliRatingId: 'invalid-id'
      },
      status: 0,
      expectedHttpStatus: 400  // Bad Request
    },
    {
      it: 'As a user, I should not be able to get PLI rating with non-existent PLI Rating ID',
      options: {
        pliRatingId: '507f1f77bcf86cd799439011' // Valid format but non-existent
      },
      status: 0,
      expectedHttpStatus: 400  // The actual response is 400, not 404
    }
  ]
};
