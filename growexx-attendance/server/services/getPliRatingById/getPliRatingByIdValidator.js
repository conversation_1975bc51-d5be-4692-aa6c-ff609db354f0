const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');
const mongoose = require('mongoose');

/**
 * Class represents validations for getting PLI rating by ID
 */
class GetPliRatingByIdValidator extends validation {
    constructor(query, locale) {
        super(locale);
        this.query = query;
    }

    /**
     * @desc This function is being used to validate getting PLI rating by ID
     * <AUTHOR>
     * @since 16/05/2025
     */
    validateGetPliRatingById() {
        const { pliRatingId } = this.query;
        this.pliRatingId(pliRatingId, 'PLI Rating ID');
    }

    /**
     * @desc This function is being used to validate PLI rating ID
     * <AUTHOR>
     * @since 16/05/2025
     * @param {String} pliRatingId PLI Rating ID
     * @param {String} field Field name
     */
    pliRatingId(pliRatingId, field) {
        if (!pliRatingId) {
            throw new GeneralError(this.__(this.REQUIRED, field), 400);
        }

        if (!mongoose.Types.ObjectId.isValid(pliRatingId)) {
            throw new GeneralError(this.__(this.NOT_VALID, field), 400);
        }
    }
}

module.exports = GetPliRatingByIdValidator;
