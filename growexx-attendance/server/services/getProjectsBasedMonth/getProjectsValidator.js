// server/services/getProjectsBasedMonth/getProjectsValidator.js
const validation = require("../../util/validation");
const GeneralError = require("../../util/GeneralError");
const mongoose = require("mongoose");
const User = require("../../models/user.model");

/**
 * Class represents validations for PLI Projects.
 */
class ProjectsValidator extends validation {
  constructor(data, locale) {
    super(locale);
    this.data = data;
  }

  /**
   * @desc This function is being used to validate get mentee projects request
   * <AUTHOR>
   * @since 30/04/2024
   */
  async validateGetMenteeProjects() {
    await this.validateMenteeId();
    await this.validateMonth();
    await this.validateYear();
  }

  /**
   * @desc This function is being used to validate sync projects request
   * <AUTHOR>
   * @since 30/04/2024
   */
  async validateSyncProjects() {
    await this.validateMenteeId();
    await this.validateMentorId();
    await this.validateMonth();
    await this.validateYear();
    await this.validateProjects();
    await this.validateTotalWeightage();
  }

  /**
   * @desc This function is being used to validate mentee id
   * <AUTHOR>
   * @since 30/04/2024
   */
  async validateMenteeId() {
    if (!this.data.menteeId) {
      throw new GeneralError(this.__("FIELD_REQUIRED", "Mentee ID"), 400);
    }

    // Check if menteeId is a valid ObjectId
    if (mongoose.Types.ObjectId.isValid(this.data.menteeId)) {
      // It's a valid ObjectId, so we're good
      return;
    }

    // If it's not a valid ObjectId, check if it's a valid employee ID
    const employeeId = parseInt(this.data.menteeId);
    if (isNaN(employeeId)) {
      throw new GeneralError(this.__("INVALID_MENTEE_ID"), 400);
    }

    // Try to find the user with this employeeId
    const user = await User.findOne({ employeeId });
    if (!user) {
      throw new GeneralError(
        this.__("USER_NOT_FOUND", "with Employee ID"),
        400
      );
    }
  }

  /**
   * @desc This function is being used to validate mentor id
   * <AUTHOR>
   * @since 30/04/2024
   */
  async validateMentorId() {
    if (!this.data.mentorId) {
      throw new GeneralError(this.__("FIELD_REQUIRED", "Mentor ID"), 400);
    }

    // Check if mentorId is a valid ObjectId
    if (mongoose.Types.ObjectId.isValid(this.data.mentorId)) {
      // It's a valid ObjectId, so we're good
      return;
    }

    // If it's not a valid ObjectId, check if it's a valid employee ID
    const employeeId = parseInt(this.data.mentorId);
    if (isNaN(employeeId)) {
      throw new GeneralError(this.__("INVALID_MENTOR_ID"), 400);
    }

    // Try to find the user with this employeeId
    const user = await User.findOne({ employeeId });
    if (!user) {
      throw new GeneralError(
        this.__("USER_NOT_FOUND", "with Employee ID"),
        400
      );
    }
  }

  /**
   * @desc This function is being used to validate month
   * <AUTHOR>
   * @since 30/04/2024
   */
  async validateMonth() {
    if (!this.data.month) {
      throw new GeneralError(this.__("FIELD_REQUIRED", "Month"), 400);
    }
    const month = parseInt(this.data.month);
    if (isNaN(month) || month < 1 || month > 12) {
      throw new GeneralError(this.__("INVALID_MONTH"), 400);
    }
  }

  /**
   * @desc This function is being used to validate year
   * <AUTHOR>
   * @since 30/04/2024
   */
  async validateYear() {
    if (!this.data.year) {
      throw new GeneralError(this.__("FIELD_REQUIRED", "Year"), 400);
    }
    const year = parseInt(this.data.year);
    const currentYear = new Date().getFullYear();
    if (isNaN(year) || year < 2000 || year > currentYear + 1) {
      throw new GeneralError(this.__("INVALID_YEAR"), 400);
    }
  }

  /**
   * @desc This function is being used to validate projects array
   * <AUTHOR>
   * @since 30/04/2024
   */
  async validateProjects() {
    if (!this.data.projects || !Array.isArray(this.data.projects)) {
      throw new GeneralError(this.__("FIELD_REQUIRED", "Projects"), 400);
    }

    if (!this.data.projects.length) {
      throw new GeneralError(
        this.__("FIELD_REQUIRED", "At least one project"),
        400
      );
    }

    for (const project of this.data.projects) {
      if (!project.projectId) {
        throw new GeneralError(this.__("FIELD_REQUIRED", "Project ID"), 400);
      }
      if (!mongoose.Types.ObjectId.isValid(project.projectId)) {
        throw new GeneralError(this.__("INVALID_OBJECT_ID", "projectId"), 400);
      }
      if (
        typeof project.projectWeightage !== "number" ||
        project.projectWeightage < 0 ||
        project.projectWeightage > 100
      ) {
        throw new GeneralError(this.__("INVALID_PROJECT_WEIGHTAGE"), 400);
      }
    }
  }

  /**
   * @desc This function is being used to validate total weightage equals 100
   * <AUTHOR>
   * @since 30/04/2024
   */
  async validateTotalWeightage() {
    const totalWeightage = this.data.projects.reduce(
      (sum, project) => sum + project.projectWeightage,
      0
    );
    if (Math.abs(totalWeightage - 100) > 0.01) {
      throw new GeneralError(this.__("TOTAL_WEIGHTAGE_HUNDRED"), 400);
    }
  }

  /**
   * @desc This function is being used to validate get mentee projects by label request
   * <AUTHOR>
   * @since 06/05/2025
   */
  async validateGetMenteeProjectsByLabel() {
    await this.validateMonth();
    await this.validateYear();
    
    // Validate mentee label
    if (!this.data.menteeLabel) {
      throw new GeneralError(this.__('FIELD_REQUIRED', 'Mentee Label'), 400);
    }
  }
}

// Validator middleware for GET request
const getMenteeProjectsValidator = async (req, res, next) => {
  try {
    const validator = new ProjectsValidator(req.query, res.__);
    await validator.validateGetMenteeProjects();
    next();
  } catch (error) {
    res.status(error.code || 400).json({
      status: 0,
      message: error.message,
    });
  }
};

// Validator middleware for GET request with mentee label
const getMenteeProjectsByLabelValidator = async (req, res, next) => {
  try {
    const validator = new ProjectsValidator(req.query, res.__);
    await validator.validateGetMenteeProjectsByLabel();
    next();
  } catch (error) {
    res.status(error.code || 400).json({
      status: 0,
      message: error.message,
    });
  }
};

// Validator middleware for POST request
const syncProjectsValidator = async (req, res, next) => {
  try {
    const validator = new ProjectsValidator(req.body, res.__);
    await validator.validateSyncProjects();
    next();
  } catch (error) {
    res.status(error.code || 400).json({
      status: 0,
      message: error.message,
    });
  }
};

module.exports = {
  getMenteeProjectsValidator,
  getMenteeProjectsByLabelValidator,
  syncProjectsValidator,
};
