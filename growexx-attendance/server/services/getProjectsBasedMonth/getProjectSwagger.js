const message = require("../../locales/en");

module.exports = (swagger<PERSON>son) => {
  // Get Projects Endpoint
  swaggerJson.paths["/api/pli-projects"] = {
    get: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["PLI Projects"],
      description: "Fetch mentee projects with types (Rated and Additional)",
      summary:
        "Get mentee projects for a given month and year with project types",
      parameters: [
        {
          in: "query",
          name: "menteeId",
          description:
            "ID of the mentee (user). Can be either MongoDB ObjectId or Employee ID",
          required: true,
          schema: {
            type: "string",
          },
        },
        {
          in: "query",
          name: "month",
          description: "Month for which to fetch projects (1-12)",
          required: true,
          schema: {
            type: "integer",
            minimum: 1,
            maximum: 12,
          },
        },
        {
          in: "query",
          name: "year",
          description: "Year for which to fetch projects (e.g., 2025)",
          required: true,
          schema: {
            type: "integer",
            minimum: 2000,
            maximum: 2100,
          },
        },
      ],
      responses: {
        200: {
          description: "Projects data fetched successfully",
          schema: {
            $ref: "#/definitions/successGetProjects",
          },
        },
        400: {
          description: "Invalid request parameters",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
        401: {
          description: "Unauthorized Access",
          schema: {
            $ref: "#/definitions/unauthorisedAccess",
          },
        },
        500: {
          description: "Internal server error",
          schema: {
            $ref: "#/definitions/unexpectedError",
          },
        },
      },
    },
  };

  // Get Projects By Label Endpoint
  swaggerJson.paths["/api/pli-projects-by-label"] = {
    get: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["PLI Projects"],
      description: "Fetch mentee projects based on mentee label from RAG model",
      summary:
        "Get mentee projects for a given mentee label, month and year with project types",
      parameters: [
        {
          in: "query",
          name: "menteeLabel",
          description:
            "Label of the mentee as it appears in the RAG model team.member field",
          required: true,
          schema: {
            type: "string",
          },
        },
        {
          in: "query",
          name: "month",
          description: "Month for which to fetch projects (1-12)",
          required: true,
          schema: {
            type: "integer",
            minimum: 1,
            maximum: 12,
          },
        },
        {
          in: "query",
          name: "year",
          description: "Year for which to fetch projects (e.g., 2025)",
          required: true,
          schema: {
            type: "integer",
            minimum: 2000,
            maximum: 2100,
          },
        },
      ],
      responses: {
        200: {
          description: "Projects data fetched successfully",
          schema: {
            $ref: "#/definitions/successGetProjects",
          },
        },
        400: {
          description: "Invalid request parameters",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
        401: {
          description: "Unauthorized Access",
          schema: {
            $ref: "#/definitions/unauthorisedAccess",
          },
        },
        500: {
          description: "Internal server error",
          schema: {
            $ref: "#/definitions/unexpectedError",
          },
        },
      },
    },
  };

  // Sync Projects Endpoint
  swaggerJson.paths["/api/pli-projects/sync"] = {
    post: {
      security: [
        {
          bearerAuth: [],
        },
      ],
      tags: ["PLI Projects"],
      description: "Sync project weights and create/update PLI rating",
      summary: "Sync selected projects with weights and create PLI rating",
      parameters: [
        {
          in: "body",
          name: "body",
          description: "Project sync data",
          required: true,
          schema: {
            $ref: "#/definitions/syncProjectsInput",
          },
        },
      ],
      responses: {
        200: {
          description: "Projects synced successfully",
          schema: {
            $ref: "#/definitions/successSyncProjects",
          },
        },
        400: {
          description: "Invalid request or total weight not 100%",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
        401: {
          description: "Unauthorized Access",
          schema: {
            $ref: "#/definitions/unauthorisedAccess",
          },
        },
        500: {
          description: "Internal server error",
          schema: {
            $ref: "#/definitions/unexpectedError",
          },
        },
      },
    },
  };

  // Define request/response schemas
  swaggerJson.definitions.syncProjectsInput = {
    type: "object",
    required: ["menteeId", "mentorId", "month", "year", "projects"],
    properties: {
      menteeId: {
        type: "string",
        description:
          "ID of the mentee. Can be either MongoDB ObjectId or Employee ID",
        example: "507f1f77bcf86cd799439011",
      },
      mentorId: {
        type: "string",
        description:
          "ID of the mentor. Can be either MongoDB ObjectId or Employee ID",
        example: "507f1f77bcf86cd799439012",
      },
      month: {
        type: "integer",
        minimum: 1,
        maximum: 12,
        example: 7,
      },
      year: {
        type: "integer",
        minimum: 2000,
        maximum: 2100,
        example: 2024,
      },
      projects: {
        type: "array",
        items: {
          type: "object",
          required: ["projectId", "projectWeightage"],
          properties: {
            projectId: {
              type: "string",
              example: "507f1f77bcf86cd799439013",
            },
            projectWeightage: {
              type: "number",
              minimum: 0,
              maximum: 100,
              example: 40,
            },
          },
        },
      },
    },
  };

  swaggerJson.definitions.successGetProjects = {
    type: "object",
    properties: {
      success: {
        type: "boolean",
        example: true,
      },
      data: {
        type: "object",
        properties: {
          ratedProjects: {
            type: "array",
            items: {
              type: "object",
              properties: {
                _id: {
                  type: "string",
                  example: "507f1f77bcf86cd799439014",
                },
                projectName: {
                  type: "string",
                  example: "Project A",
                },
                projectType: {
                  type: "string",
                  enum: ["Dedicated", "Fixed"],
                  example: "Fixed",
                },
              },
            },
          },
          additionalProjects: {
            type: "array",
            items: {
              type: "object",
              properties: {
                _id: {
                  type: "string",
                  example: "507f1f77bcf86cd799439015",
                },
                projectName: {
                  type: "string",
                  example: "Project B",
                },
                projectType: {
                  type: "string",
                  enum: ["Dedicated", "Fixed"],
                  example: "Dedicated",
                },
              },
            },
          },
        },
      },
    },
  };

  swaggerJson.definitions.successSyncProjects = {
    type: "object",
    properties: {
      success: {
        type: "boolean",
        example: true,
      },
      data: {
        type: "object",
        properties: {
          _id: {
            type: "string",
            example: "507f1f77bcf86cd799439016",
          },
          menteeId: {
            type: "object",
            properties: {
              _id: {
                type: "string",
                example: "507f1f77bcf86cd799439011",
              },
              name: {
                type: "string",
                example: "John Doe",
              },
              email: {
                type: "string",
                example: "<EMAIL>",
              },
              employeeId: {
                type: "number",
                example: 12345,
              },
            },
          },
          mentorId: {
            type: "object",
            properties: {
              _id: {
                type: "string",
                example: "507f1f77bcf86cd799439012",
              },
              name: {
                type: "string",
                example: "Jane Smith",
              },
              email: {
                type: "string",
                example: "<EMAIL>",
              },
              employeeId: {
                type: "number",
                example: 54321,
              },
            },
          },
          month: {
            type: "number",
            example: 7,
          },
          year: {
            type: "number",
            example: 2024,
          },
          projectRatings: {
            type: "array",
            items: {
              type: "object",
              properties: {
                projectId: {
                  type: "object",
                  properties: {
                    _id: {
                      type: "string",
                      example: "507f1f77bcf86cd799439013",
                    },
                    projectName: {
                      type: "string",
                      example: "Project A",
                    },
                  },
                },
                projectWeightage: {
                  type: "number",
                  example: 40,
                },
              },
            },
          },
          status: {
            type: "string",
            example: "Draft",
          },
        },
      },
      message: {
        type: "string",
        example: "Projects synced successfully",
      },
    },
  };

  swaggerJson.definitions.validationError = {
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: message.INVALID_REQUEST,
      },
    },
  };

  swaggerJson.definitions.unauthorisedAccess = {
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: message.ACCESS_DENIED,
      },
    },
  };

  swaggerJson.definitions.unexpectedError = {
    properties: {
      status: {
        type: "number",
        example: 0,
      },
      message: {
        type: "string",
        example: message.ERROR_MSG,
      },
    },
  };

  return swaggerJson;
};
