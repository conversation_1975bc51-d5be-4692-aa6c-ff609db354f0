const pliProjectService = require('./getProjectService');

exports.getMenteeProjects = async (req, res) => {
    try {
        const { menteeId, month, year } = req.query;
        const data = await pliProjectService.getMenteeProjects({
            menteeId,
            month,
            year
        });
        return res.status(200).json({ success: true, data });
    } catch (error) {
        return res.status(500).json({ success: false, message: error.message });
    }
};

exports.getMenteeProjectsByLabel = async (req, res) => {
    try {
        const { menteeLabel, month, year } = req.query;
        const data = await pliProjectService.getMenteeProjectsByLabel({
            menteeLabel,
            month,
            year
        });
        return res.status(200).json({ success: true, data });
    } catch (error) {
        return res.status(500).json({ success: false, message: error.message });
    }
};

exports.syncProjectWeights = async (req, res) => {
    try {
        const { menteeId, mentorId, month, year, projects } = req.body;

        // Validate required fields
        if (!menteeId || !mentorId || !month || !year || !projects) {
            return res.status(400).json({
                success: false,
                message: 'Missing required fields'
            });
        }

        // Validate total weight equals 100%
        const totalWeight = projects.reduce(
            (sum, proj) => sum + proj.projectWeightage,
            0
        );
        if (Math.abs(totalWeight - 100) > 0.01) {
            return res.status(400).json({
                success: false,
                message:
          'Total weight must equal 100%. Please adjust the weights accordingly.'
            });
        }

        const data = await pliProjectService.syncProjectWeights({
            menteeId,
            mentorId,
            month,
            year,
            projects
        });

        return res.status(200).json({
            success: true,
            data,
            message: 'Projects synced successfully'
        });
    } catch (error) {
        return res.status(500).json({
            success: false,
            message: error.message
        });
    }
};
