const chai = require("chai");
const chaiHttp = require("chai-http");
const sinon = require("sinon");
const mongoose = require("mongoose");
const expect = chai.expect;
const PliRating = require("../../../models/pliRating.model");
const Project = require("../../../models/project.model");
const PliParameter = require("../../../models/pliParameters.model");
const app = require("../../../server");

chai.use(chaiHttp);

describe("Get Projects Based Month", () => {
  // Define mock IDs at the top level
  const mockMenteeId = new mongoose.Types.ObjectId();
  const mockMentorId = new mongoose.Types.ObjectId();
  const mockProjectId1 = new mongoose.Types.ObjectId();
  const mockProjectId2 = new mongoose.Types.ObjectId();

  // Test data with properly initialized IDs
  const mockPliRating = {
    _id: new mongoose.Types.ObjectId(),
    menteeId: mockMenteeId,
    mentorId: mockMentorId,
    month: 7,
    year: 2022,
    projectRatings: [
      {
        projectId: mockProjectId1,
        projectWeightage: 60,
      },
    ],
    status: "Draft",
    proactivenessScores: [],
  };

  const mockProjects = [
    {
      _id: mockProjectId1,
      projectName: "Project A",
      isDelete: 0,
      users: [
        {
          empId: mockMenteeId,
          startDate: new Date("2022-07-01"),
          endDate: new Date("2022-07-31"),
        },
      ],
    },
  ];

  const mockPliParameters = [
    {
      _id: new mongoose.Types.ObjectId(),
      parentParameter: "Project A",
      projectType: "Dedicated",
      isActive: 1,
      childParameters: [],
    },
  ];

  // Define valid request body using the mock IDs
  const validRequestBody = {
    menteeId: mockMenteeId.toString(),
    mentorId: mockMentorId.toString(),
    month: 7,
    year: 2022,
    projects: [
      {
        projectId: mockProjectId1.toString(),
        projectWeightage: 100,
      },
    ],
  };

  beforeEach(() => {
    sinon.restore();
  });

  describe("GET /api/pli-projects", () => {
    it("should return 400 if menteeId is missing", async () => {
      const res = await chai
        .request(app)
        .get("/api/pli-projects")
        .query({ month: 7, year: 2022 });

      expect(res).to.have.status(400);
      expect(res.body).to.have.property("status", 0);
      expect(res.body.message).to.include("Mentee ID");
    });

    it("should return 400 if month is invalid", async () => {
      const res = await chai
        .request(app)
        .get("/api/pli-projects")
        .query({ menteeId: mockMenteeId.toString(), month: 13, year: 2022 });

      expect(res).to.have.status(400);
      expect(res.body).to.have.property("status", 0);
      expect(res.body.message).to.equal("INVALID_MONTH");
    });

    it("should return 400 if year is invalid", async () => {
      const res = await chai
        .request(app)
        .get("/api/pli-projects")
        .query({ menteeId: mockMenteeId.toString(), month: 7, year: 1999 });

      expect(res).to.have.status(400);
      expect(res.body).to.have.property("status", 0);
      expect(res.body.message).to.equal("INVALID_YEAR");
    });

    it("should return rated projects when PLI rating exists", async () => {
      // Create a mock response with expected structure
      const expectedResponse = {
        ratedProjects: [
          {
            _id: mockProjectId1,
            projectName: "Project A",
            projectType: "Dedicated",
          },
        ],
        additionalProjects: [],
      };

      // Properly stub the service method directly instead of DB calls
      const getMenteeProjectsStub = sinon
        .stub(require("../getProjectService"), "getMenteeProjects")
        .resolves(expectedResponse);

      const res = await chai.request(app).get("/api/pli-projects").query({
        menteeId: mockMenteeId.toString(),
        month: 7,
        year: 2022,
      });

      expect(res).to.have.status(200);
      expect(res.body).to.have.property("success", true);
      expect(res.body.data)
        .to.have.property("ratedProjects")
        .that.is.an("array");
      expect(res.body.data)
        .to.have.property("additionalProjects")
        .that.is.an("array");

      // Verify stub was called with expected arguments
      expect(getMenteeProjectsStub.calledOnce).to.be.true;
    });

    it("should return projects from Project model when no PLI rating exists", async () => {
      // Create a mock response with expected structure
      const expectedResponse = {
        ratedProjects: [
          {
            _id: mockProjectId1,
            projectName: "Project A",
            projectType: "Dedicated",
          },
        ],
        additionalProjects: [],
      };

      // Properly stub the service method directly
      const getMenteeProjectsStub = sinon
        .stub(require("../getProjectService"), "getMenteeProjects")
        .resolves(expectedResponse);

      const res = await chai.request(app).get("/api/pli-projects").query({
        menteeId: mockMenteeId.toString(),
        month: 7,
        year: 2022,
      });

      expect(res).to.have.status(200);
      expect(res.body).to.have.property("success", true);
      expect(res.body.data.ratedProjects).to.be.an("array");
      expect(res.body.data.additionalProjects).to.be.an("array");
    });

    it("should return empty arrays when no projects found", async () => {
      // Create a mock response with empty arrays
      const expectedResponse = {
        ratedProjects: [],
        additionalProjects: [],
      };

      // Properly stub the service method directly
      const getMenteeProjectsStub = sinon
        .stub(require("../getProjectService"), "getMenteeProjects")
        .resolves(expectedResponse);

      const res = await chai.request(app).get("/api/pli-projects").query({
        menteeId: mockMenteeId.toString(),
        month: 7,
        year: 2022,
      });

      expect(res).to.have.status(200);
      expect(res.body).to.have.property("success", true);
      expect(res.body.data.ratedProjects).to.be.an("array").that.is.empty;
      expect(res.body.data.additionalProjects).to.be.an("array").that.is.empty;
    });
  });

  describe("POST /api/pli-projects/sync", () => {
    it("should return 400 if menteeId is missing", async () => {
      const { menteeId, ...invalidBody } = { ...validRequestBody };
      const res = await chai
        .request(app)
        .post("/api/pli-projects/sync")
        .send(invalidBody);

      expect(res).to.have.status(400);
      expect(res.body).to.have.property("status", 0);
      expect(res.body.message).to.include("Mentee ID");
    });

    it("should return 400 if mentorId is missing", async () => {
      const { mentorId, ...invalidBody } = { ...validRequestBody };
      const res = await chai
        .request(app)
        .post("/api/pli-projects/sync")
        .send(invalidBody);

      expect(res).to.have.status(400);
      expect(res.body).to.have.property("status", 0);
      expect(res.body.message).to.include("Mentor ID");
    });

    it("should return 400 if total weightage is not 100%", async () => {
      const invalidBody = {
        ...validRequestBody,
        projects: [
          {
            projectId: mockProjectId1.toString(),
            projectWeightage: 60,
          },
        ],
      };

      const res = await chai
        .request(app)
        .post("/api/pli-projects/sync")
        .send(invalidBody);

      expect(res).to.have.status(400);
      expect(res.body).to.have.property("status", 0);
    });

    it("should successfully sync projects", async () => {
      // Create a populated response similar to what the service would return
      const populatedResponse = {
        _id: mockPliRating._id,
        menteeId: {
          _id: mockMenteeId,
          name: "Test User",
          email: "<EMAIL>",
        },
        mentorId: {
          _id: mockMentorId,
          name: "Test Mentor",
          email: "<EMAIL>",
        },
        month: 7,
        year: 2022,
        projectRatings: [
          {
            projectId: { _id: mockProjectId1, projectName: "Project A" },
            projectWeightage: 100,
          },
        ],
        status: "Draft",
      };

      // Stub the service method directly
      const syncProjectWeightsStub = sinon
        .stub(require("../getProjectService"), "syncProjectWeights")
        .resolves(populatedResponse);

      const res = await chai
        .request(app)
        .post("/api/pli-projects/sync")
        .send(validRequestBody);

      expect(res).to.have.status(200);
      expect(res.body).to.have.property("success", true);
      expect(res.body).to.have.property(
        "message",
        "Projects synced successfully"
      );
      expect(res.body).to.have.property("data");

      // Verify stub was called with expected arguments
      expect(syncProjectWeightsStub.calledOnce).to.be.true;
    });
  });
});
