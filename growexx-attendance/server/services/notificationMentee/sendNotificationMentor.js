const path = require("path");
const logger = require("../../util/logger");
const EmailService = require("../../util/sendEmail");
const User = require("../../models/user.model");
const JWT = require("../../util/jwt");

/**
 * Sends a PLI query notification email to the mentor
 * @param {string} mentorId - The ID of the mentor user
 * @param {object} payload - Data for generating the email content
 * @returns {Promise<boolean>} - Status of email sending
 */
const sendNotificationMentor = async (mentorId, payload) => {
  try {
    const {
      month,
      year,
      menteeId,
      projectName,
      parameterName,
      queryFeedback,
      companyName = "Growexx",
    } = payload;

    // Fetch mentor details
    const mentor = await User.findById(mentorId);
    if (!mentor) throw new Error("Mentor not found");

    // Fetch mentee details
    const mentee = await User.findById(menteeId);
    if (!mentee) throw new Error("Mentee not found");

    const mentorName = `${mentor.firstName} ${mentor.lastName}`;
    const mentorEmail = mentor.email;
    const menteeName = `${mentee.firstName} ${mentee.lastName}`;

    const token = JWT.createTempAuthToken(mentorId, {
      redirectTo: "/pli",
    });

    const baseUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    const pliViewUrl = `${baseUrl}/pli?token=${token}`;

    const templateVariables = {
      MENTOR_NAME: mentorName,
      MENTEE_NAME: menteeName,
      MONTH: month,
      YYYY: year,
      PROJECT_NAME: projectName,
      PARAMETER_NAME: parameterName,
      QUERY_FEEDBACK: queryFeedback,
      ACTIONURL: pliViewUrl,
      APPURL: baseUrl,
      COMPANY_NAME: companyName,
    };

    const templatePath = path.join(
      process.cwd(),
      "emailTemplates",
      "mentorNotificationRegardingQuery.html"
    );

    const subject = `Query Raised on PLI Rating for ${menteeName} – ${month} ${year}`;

    await EmailService.prepareAndSendEmail(
      [mentorEmail],
      subject,
      templatePath,
      templateVariables
    );

    logger.info(`Query PLI notification email sent to mentor: ${mentorEmail}`);
    return true;
  } catch (error) {
    logger.error(`Failed to send PLI query notification: ${error.message}`);
    return false;
  }
};

module.exports = sendNotificationMentor;
