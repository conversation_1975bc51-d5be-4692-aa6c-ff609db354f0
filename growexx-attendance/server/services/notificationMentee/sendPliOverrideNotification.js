const path = require('path');
const logger = require('../../util/logger');
const EmailService = require('../../util/sendEmail');
const User = require('../../models/user.model');
const JWT = require('../../util/jwt');

/**
 * Send override notification email to mentee and mentor
 * @param {string} menteeId - Mentee user ID
 * @param {string} mentorId - Mentor user ID
 * @param {object} payload - Data for the email
 * @returns {Promise<boolean>}
 */
const sendOverrideNotificationToMentee = async (
    menteeId,
    mentorId,
    payload
) => {
    try {
    // Fetch mentee and mentor details
        const mentee = await User.findById(menteeId);
        const mentor = await User.findById(mentorId);

        if (!mentee || !mentor) {
            throw new Error('Mentee or Mentor not found');
        }

        // Prepare email addresses
        const recipients = [mentee.email, mentor.email];
        const token = JWT.createTempAuthToken(menteeId, {
            redirectTo: '/pli'
        });
        const baseUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
        const pliViewUrl = `${baseUrl}/pli?token=${token}`;
        // Prepare template variables
        const hrLink = 'mailto:<EMAIL>';
        const {
            fyCycle // e.g. "FY 2023-24"
            // e.g "https://yourapp.com/pli-dashboard // e.g. "mailto:<EMAIL>"
        } = payload;

        const templatePath = path.join(
            process.cwd(),
            'emailTemplates',
            'overriidenPLIByBU.html'
        );

        const templateVariables = {
            MENTEE_NAME: `${mentee.firstName} ${mentee.lastName}`,
            FY_CYCLE: fyCycle,
            SYSTEM_LINK: pliViewUrl,
            HR_LINK: hrLink
        };

        await EmailService.prepareAndSendEmail(
            recipients,
            'Your PLI Ratings Have Been Overridden by BU',
            templatePath,
            templateVariables
        );

        logger.info(
            `Override notification sent to mentee: ${mentee.email} and mentor: ${mentor.email}`
        );
        return true;
    } catch (error) {
        logger.error(`Error sending override notification: ${error.message}`);
        return false;
    }
};

module.exports = sendOverrideNotificationToMentee;
