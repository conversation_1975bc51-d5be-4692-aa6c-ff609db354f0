const fs = require("fs");
const path = require("path");
const logger = require("../../util/logger");
const EmailService = require("../../util/sendEmail");
const User = require("../../models/user.model");
const JWT = require("../../util/jwt");
const moment = require("moment");

/**
 * Send override notification email to super admin
 * @param {string} adminId - Super admin user ID
 * @param {object} payload - Data for the email
 * @returns {Promise<boolean>}
 */
const sendFrozenPliOverrideNotification = async (adminId, payload) => {
  if (!adminId) throw new Error("adminId is required");
  try {
    // Fetch admin user from DB
    const admin = await User.findById(adminId);
    if (!admin) {
      throw new Error("Admin user not found");
    }

    const adminEmail = admin.email;
    const adminName = `${admin.firstName} ${admin.lastName}`;

    const {
      menteeName,
      mentorName,
      month,
      year,
      menteeLink = "#",
      mentorLink = "#",
      monthLink = "#",
      adminLink = "#",
      companyName = "Growexx",
    } = payload;

    // Generate a token for the admin to view PLI details
    const token = JWT.createTempAuthToken(adminId, { redirectTo: "/pli" });
    const baseUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    const pliViewUrl = `${baseUrl}/pli?token=${token}`;

    // Path to email template
    const templatePath = path.join(
      process.cwd(),
      "emailTemplates",
      "frozenPLIscore.html"
    );

    // Template variables
    const templateVariables = {
      ADMIN_NAME: adminName,
      MENTEE_NAME: menteeName,
      MENTOR_NAME: mentorName,
      MONTH: month,
      YYYY: year,
      ADMIN_LINK: adminLink || pliViewUrl,
      MENTEE_LINK: menteeLink || `${baseUrl}/profile/${menteeName}`,
      MENTOR_LINK: mentorLink || `${baseUrl}/profile/${mentorName}`,
      MONTH_LINK: monthLink || `${baseUrl}/pli/month/${month}-${year}`,
      APPURL: baseUrl,
      COMPANY_NAME: companyName,
      YEAR: new Date().getFullYear(),
    };

    // Send the email
    await EmailService.prepareAndSendEmail(
      [adminEmail],
      `PLI Score for ${menteeName} – ${month} ${year} Has Been Frozen`,
      templatePath,
      templateVariables
    );

    logger.info(`Override PLI notification email sent to admin: ${adminEmail}`);
    return true;
  } catch (error) {
    logger.error(
      `Error sending override PLI notification email: ${error.message}`
    );
    return false;
  }
};

module.exports = sendFrozenPliOverrideNotification;
