module.exports = {
    validUpdateSprintScores: {
        menteeId: '60d5a7f0b6e8a12345678901',
        mentorId: '60d5a7f0b6e8a12345678900',
        month: 5,
        year: 2025,
        projectRatings: [
            {
                projectId: '60d5a7f0b6e8a12345678902',
                parameterScores: [
                    {
                        parameterId: '60d5a7f0b6e8a12345678903',
                        comments: 'Good performance in this parameter',
                        childScores: [
                            {
                                childParameterId: '60d5a7f0b6e8a12345678904',
                                sprintScores: [
                                    {
                                        sprintNumber: 'Sprint 1',
                                        score: 4.5,
                                        comment: 'Excellent work in this sprint'
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    },
    invalidUpdateSprintScores: {
        missingMenteeId: {
            month: 5,
            year: 2025,
            projectRatings: [
                {
                    projectId: '60d5a7f0b6e8a12345678902',
                    parameterScores: [
                        {
                            parameterId: '60d5a7f0b6e8a12345678903',
                            childScores: [
                                {
                                    childParameterId: '60d5a7f0b6e8a12345678904',
                                    sprintScores: [
                                        {
                                            sprintNumber: 'Sprint 1',
                                            score: 4.5
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        invalidMonth: {
            menteeId: '60d5a7f0b6e8a12345678901',
            month: 13,
            year: 2025,
            projectRatings: [
                {
                    projectId: '60d5a7f0b6e8a12345678902',
                    parameterScores: [
                        {
                            parameterId: '60d5a7f0b6e8a12345678903',
                            childScores: [
                                {
                                    childParameterId: '60d5a7f0b6e8a12345678904',
                                    sprintScores: [
                                        {
                                            sprintNumber: 'Sprint 1',
                                            score: 4.5
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        nonStringSprintNumber: {
            menteeId: '60d5a7f0b6e8a12345678901',
            mentorId: '60d5a7f0b6e8a12345678900',
            month: 5,
            year: 2025,
            projectRatings: [
                {
                    projectId: '60d5a7f0b6e8a12345678902',
                    parameterScores: [
                        {
                            parameterId: '60d5a7f0b6e8a12345678903',
                            childScores: [
                                {
                                    childParameterId: '60d5a7f0b6e8a12345678904',
                                    sprintScores: [
                                        {
                                            sprintNumber: 1,
                                            score: 4.5
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        },
        invalidScore: {
            menteeId: '60d5a7f0b6e8a12345678901',
            month: 5,
            year: 2025,
            projectRatings: [
                {
                    projectId: '60d5a7f0b6e8a12345678902',
                    parameterScores: [
                        {
                            parameterId: '60d5a7f0b6e8a12345678903',
                            childScores: [
                                {
                                    childParameterId: '60d5a7f0b6e8a12345678904',
                                    sprintScores: [
                                        {
                                            sprintNumber: 'Sprint 1',
                                            score: 6
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    }
};
