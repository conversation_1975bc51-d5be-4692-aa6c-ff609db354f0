const PLIRating = require('../../models/pliRating.model');
const UpdateSprintScoresValidator = require('./updateSprintScoresValidator');
const GeneralError = require('../../util/GeneralError');
const User = require('../../models/user.model');
const Project = require('../../models/project.model');
const sendPliNotificationEmail = require('../notificationMentee/sendPLIRatingNotification');


/**
 * Class represents services for updating sprint scores in PLI Rating
 */
class UpdateSprintScoresService {
    /**
   * @desc This function is being used to update sprint scores in PLI rating
   * <AUTHOR>
   * @since 13/05/2025
   * @param {Object} req Request
   * @param {function} locale Translation function
   * @return {Object} Updated PLI rating
   */
    static async updateSprintScores (req, locale) {
        try {
            console.log('Starting updateSprintScores service');
            const Validator = new UpdateSprintScoresValidator(req.body, locale);
            await Validator.validateUpdateSprintScores();

            const { menteeId, mentorId, month, year, projectRatings } = req.body;

            // Check if PLI rating exists for the given mentee, month, and year
            let pliRating = await PLIRating.findOne({
                menteeId,
                month,
                year
            });

            // If PLI rating doesn't exist, create a new one
            if (!pliRating) {
                // Create a new PLI rating with the provided project ratings
                if (!mentorId) {
                    throw new GeneralError(locale('MENTOR_ID_REQUIRED'));
                }
                pliRating = new PLIRating({
                    menteeId,
                    mentorId,
                    month,
                    year,
                    status: 'Draft',
                    projectRatings: projectRatings, // Use project ratings directly from request
                    createdAt: Date.now(),
                    updatedAt: Date.now()
                });
            } else {
                // If PLI rating exists, completely replace the project ratings
                pliRating.projectRatings = projectRatings;
                pliRating.updatedAt = Date.now();
            }

            // Update the PLI rating
            pliRating.updatedAt = Date.now();

            // Save the updated PLI rating
            await pliRating.save();

            // Ensure _id is included in the response
            const response = pliRating.toObject ? pliRating.toObject() : pliRating;
            if (!response._id && pliRating._id) {
                response._id = pliRating._id;
            }

            // Update the PLI rating
            pliRating.updatedAt = Date.now();

            try {
                // Save the updated PLI rating
                await pliRating.save();
                // Get mentor details
                const mentor = await User.findById(mentorId);
                const mentorName = mentor ? `${mentor.firstName} ${mentor.lastName}` : 'Your Mentor';

                // Get project name (using the first project for simplicity)
                let projectName = 'Your Project';
                if (projectRatings && projectRatings.length > 0) {
                    const project = await Project.findById(projectRatings[0].projectId);
                    if (project) {
                        projectName = project.name;
                    }
                }

                // Get month name
                // const monthNames = [
                //     'January', 'February', 'March', 'April', 'May', 'June',
                //     'July', 'August', 'September', 'October', 'November', 'December'
                // ];
                // const monthName = monthNames[month - 1] || 'Current Month';

                // Use the final score from the saved PLI rating
                const score = pliRating.finalScore || 'N/A';

                // Prepare payload for email
                const emailPayload = {
                    month: month,
                    year,
                    score,
                    mentorName,
                    projectName,
                    companyName: 'Growexx',
                    pliRatingId: response._id.toString()
                };

                // Send notification email
                await sendPliNotificationEmail(menteeId, emailPayload);
                console.log(`Email notification sent to mentee ${menteeId}`);
            } catch (emailError) {
                // Don't fail the entire request if email fails
                console.error('Failed to send email notification:', emailError);
            }

            return response;
        } catch (error) {
            console.error('Error in updateSprintScores service:', error);
            throw error;
        }
    }
}

module.exports = UpdateSprintScoresService;
