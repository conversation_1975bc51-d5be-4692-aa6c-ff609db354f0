const message = require('../../locales/en.json');

module.exports = (swaggerJson) => {
    swaggerJson.paths['/api/pli-rating/sprint-scores'] = {
        post: {
            security: [{
                bearerAuth: []
            }],
            tags: ['PLI Rating'],
            description: 'Update sprint scores in PLI rating',
            summary: 'Update sprint scores in PLI rating',
            parameters: [
                {
                    in: 'body',
                    name: 'body',
                    description: 'Body parameter',
                    required: true,
                    schema: {
                        $ref: '#/definitions/updateSprintScoresInput'
                    }
                }
            ],
            responses: {
                200: {
                    description: 'Sprint scores updated successfully.',
                    schema: {
                        $ref: '#/definitions/successUpdateSprintScores'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                404: {
                    description: 'PLI Rating not found',
                    schema: {
                        $ref: '#/definitions/notFound'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.updateSprintScoresInput = {
        type: 'object',
        properties: {
            menteeId: {
                type: 'string',
                example: '60d5a7f0b6e8a12345678901'
            },
            mentorId: {
                type: 'string',
                example: '60d5a7f0b6e8a12345678900'
            },
            month: {
                type: 'number',
                example: 5
            },
            year: {
                type: 'number',
                example: 2025
            },
            projectRatings: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        projectId: {
                            type: 'string',
                            example: '60d5a7f0b6e8a12345678902'
                        },
                        parameterScores: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    parameterId: {
                                        type: 'string',
                                        description: 'ID of the single PLI parameters document',
                                        example: '682adfab4d34b5d668dd1813'
                                    },
                                    projectType: {
                                        type: 'string',
                                        enum: ['Dedicated', 'Fixed'],
                                        example: 'Dedicated'
                                    },
                                    parentParameter: {
                                        type: 'string',
                                        example: 'Project'
                                    },
                                    comments: {
                                        type: 'string',
                                        example: 'Good performance in this parameter'
                                    },
                                    childScores: {
                                        type: 'array',
                                        items: {
                                            type: 'object',
                                            properties: {
                                                childParameterId: {
                                                    type: 'string',
                                                    example: '60d5a7f0b6e8a12345678904'
                                                },
                                                sprintScores: {
                                                    type: 'array',
                                                    items: {
                                                        type: 'object',
                                                        properties: {
                                                            sprintNumber: {
                                                                type: 'string',
                                                                example: 'Sprint 1'
                                                            },
                                                            score: {
                                                                type: 'number',
                                                                description: 'Sprint score (can be negative)',
                                                                example: -1
                                                            },
                                                            comment: {
                                                                type: 'string',
                                                                example: 'Excellent work in this sprint'
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    };

    swaggerJson.definitions.successUpdateSprintScores = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                properties: {
                    _id: {
                        type: 'string',
                        example: '60d5a7f0b6e8a12345678905'
                    },
                    menteeId: {
                        type: 'string',
                        example: '60d5a7f0b6e8a12345678901'
                    },
                    mentorId: {
                        type: 'string',
                        example: '60d5a7f0b6e8a12345678900'
                    },
                    month: {
                        type: 'number',
                        example: 5
                    },
                    year: {
                        type: 'number',
                        example: 2025
                    },
                    projectRatings: {
                        type: 'array',
                        example: [
                            {
                                projectId: '60d5a7f0b6e8a12345678902',
                                parameterScores: [
                                    {
                                        parameterId: '60d5a7f0b6e8a12345678903',
                                        projectType: 'Dedicated',
                                        parentParameter: 'Project',
                                        score: 4.5,
                                        comments: 'Good performance in this parameter',
                                        childScores: [
                                            {
                                                childParameterId: '60d5a7f0b6e8a12345678904',
                                                sprintScores: [
                                                    {
                                                        sprintNumber: 'Sprint 1',
                                                        score: 4.5,
                                                        comment: 'Excellent work in this sprint'
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    updatedAt: {
                        type: 'string',
                        example: '2025-05-13T13:23:45.678Z'
                    }
                }
            },
            message: {
                type: 'string',
                example: message.SUCCESS
            }
        }
    };

    return swaggerJson;
};
