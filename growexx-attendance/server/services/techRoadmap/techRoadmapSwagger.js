/**
 * Tech Roadmap Swagger Documentation
 * API documentation for tech roadmap related endpoints
 */

const message = require('../../locales/en');

module.exports = {
    // ==================== COURSE ENDPOINTS ====================
    
    addCourse: {
        tags: ['Tech Roadmap - Course Management'],
        description: 'Add a new course to the system',
        summary: 'Add new course',
        requestBody: {
            content: {
                'application/json': {
                    schema: {
                        type: 'object',
                        properties: {
                            name: {
                                type: 'string',
                                example: 'React.js Fundamentals'
                            },
                            description: {
                                type: 'string',
                                example: 'Learn the basics of React.js framework'
                            },
                            duration: {
                                type: 'string',
                                example: '4 weeks'
                            },
                            category: {
                                type: 'string',
                                example: 'Frontend Development'
                            },
                            level: {
                                type: 'string',
                                enum: ['beginner', 'intermediate', 'advanced'],
                                example: 'intermediate'
                            },
                            tags: {
                                type: 'array',
                                items: {
                                    type: 'string'
                                },
                                example: ['react', 'javascript', 'frontend']
                            }
                        },
                        required: ['name']
                    }
                }
            }
        },
        responses: {
            200: {
                description: 'Course added successfully',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                status: {
                                    type: 'number',
                                    example: 200
                                },
                                message: {
                                    type: 'string',
                                    example: 'Course added successfully'
                                },
                                data: {
                                    type: 'object',
                                    properties: {
                                        _id: {
                                            type: 'string',
                                            example: '507f1f77bcf86cd799439011'
                                        },
                                        name: {
                                            type: 'string',
                                            example: 'React.js Fundamentals'
                                        },
                                        description: {
                                            type: 'string',
                                            example: 'Learn the basics of React.js framework'
                                        },
                                        createdAt: {
                                            type: 'string',
                                            format: 'date-time'
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            400: {
                description: 'Bad Request',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                status: {
                                    type: 'number',
                                    example: 0
                                },
                                message: {
                                    type: 'string',
                                    example: 'Validation error'
                                }
                            }
                        }
                    }
                }
            },
            401: {
                description: 'Unauthorized',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                status: {
                                    type: 'number',
                                    example: 0
                                },
                                message: {
                                    type: 'string',
                                    example: message.ACCESS_DENIED
                                }
                            }
                        }
                    }
                }
            }
        }
    },

    getAllCourses: {
        tags: ['Tech Roadmap - Course Management'],
        description: 'Get all available courses',
        summary: 'Get all courses',
        responses: {
            200: {
                description: 'Courses retrieved successfully',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                status: {
                                    type: 'number',
                                    example: 200
                                },
                                data: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            _id: {
                                                type: 'string',
                                                example: '507f1f77bcf86cd799439011'
                                            },
                                            name: {
                                                type: 'string',
                                                example: 'React.js Fundamentals'
                                            },
                                            description: {
                                                type: 'string',
                                                example: 'Learn the basics of React.js framework'
                                            },
                                            duration: {
                                                type: 'string',
                                                example: '4 weeks'
                                            },
                                            category: {
                                                type: 'string',
                                                example: 'Frontend Development'
                                            },
                                            level: {
                                                type: 'string',
                                                example: 'intermediate'
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            401: {
                description: 'Unauthorized',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                status: {
                                    type: 'number',
                                    example: 0
                                },
                                message: {
                                    type: 'string',
                                    example: message.ACCESS_DENIED
                                }
                            }
                        }
                    }
                }
            }
        }
    },

    // ==================== ASSIGNMENT ENDPOINTS ====================

    assignCourse: {
        tags: ['Tech Roadmap - Assignment Management'],
        description: 'Assign a course to a mentee',
        summary: 'Assign course to mentee',
        requestBody: {
            content: {
                'application/json': {
                    schema: {
                        type: 'object',
                        properties: {
                            menteeId: {
                                type: 'string',
                                example: '507f1f77bcf86cd799439011'
                            },
                            mentorId: {
                                type: 'string',
                                example: '507f1f77bcf86cd799439012'
                            },
                            courseId: {
                                type: 'string',
                                example: '507f1f77bcf86cd799439013'
                            },
                            courseName: {
                                type: 'string',
                                example: 'React.js Fundamentals'
                            },
                            targetMonth: {
                                type: 'string',
                                format: 'date',
                                example: '2024-02-01'
                            },
                            description: {
                                type: 'string',
                                example: 'Complete React.js course with practical projects'
                            },
                            learningMedium: {
                                type: 'string',
                                example: 'Online course + hands-on projects'
                            }
                        },
                        required: ['menteeId', 'mentorId']
                    }
                }
            }
        },
        responses: {
            200: {
                description: 'Course assigned successfully',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                status: {
                                    type: 'number',
                                    example: 200
                                },
                                message: {
                                    type: 'string',
                                    example: 'Course assigned successfully'
                                },
                                data: {
                                    type: 'object',
                                    properties: {
                                        _id: {
                                            type: 'string',
                                            example: '507f1f77bcf86cd799439014'
                                        },
                                        menteeId: {
                                            type: 'string',
                                            example: '507f1f77bcf86cd799439011'
                                        },
                                        mentorId: {
                                            type: 'string',
                                            example: '507f1f77bcf86cd799439012'
                                        },
                                        courseName: {
                                            type: 'string',
                                            example: 'React.js Fundamentals'
                                        },
                                        completionStatus: {
                                            type: 'string',
                                            example: 'pending'
                                        },
                                        completionPercentage: {
                                            type: 'number',
                                            example: 0
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            400: {
                description: 'Bad Request',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                status: {
                                    type: 'number',
                                    example: 0
                                },
                                message: {
                                    type: 'string',
                                    example: 'Validation error'
                                }
                            }
                        }
                    }
                }
            },
            404: {
                description: 'Not Found',
                content: {
                    'application/json': {
                        schema: {
                            type: 'object',
                            properties: {
                                status: {
                                    type: 'number',
                                    example: 0
                                },
                                message: {
                                    type: 'string',
                                    example: 'Mentee not found'
                                }
                            }
                        }
                    }
                }
            }
        }
    }
};
