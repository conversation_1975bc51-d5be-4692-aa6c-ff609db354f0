/**
 * Tech Roadmap Controller
 * Handles HTTP requests and responses for tech roadmap related operations
 */

const techRoadmapService = require('./techRoadmapService');

// ==================== COURSE MANAGEMENT ====================

/**
 * Add a new course
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addCourse = async (req, res) => {
    try {
        console.log('=== ADD COURSE CONTROLLER ===');
        console.log('Request reached controller');
        console.log('Request body:', JSON.stringify(req.body, null, 2));

        const result = await techRoadmapService.addCourse(req.body);
        return res.status(result.status).json(result);
    } catch (error) {
        console.log('Controller error:', error.message);
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error adding course'
        });
    }
};

/**
 * Get all courses
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllCourses = async (req, res) => {
    try {
        const result = await techRoadmapService.getAllCourses();
        return res.status(result.status).json(result);
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error retrieving courses'
        });
    }
};

// ==================== ASSIGNMENT MANAGEMENT ====================

/**
 * Assign a course to a mentee
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const assignCourse = async (req, res) => {
    try {
        const result = await techRoadmapService.assignCourse(req.body);
        return res.status(result.status).json(result);
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error assigning course'
        });
    }
};

/**
 * Get all course assignments for a mentee
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMenteeCourses = async (req, res) => {
    try {
        const result = await techRoadmapService.getMenteeCourses(req.params.menteeId);
        return res.status(result.status).json(result);
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error retrieving courses'
        });
    }
};

/**
 * Update course completion status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateCourseStatus = async (req, res) => {
    try {
        const result = await techRoadmapService.updateCourseStatus(req.params.assignmentId, req.body);
        return res.status(result.status).json(result);
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error updating course status'
        });
    }
};

/**
 * Delete course assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteCourseAssignment = async (req, res) => {
    try {
        const result = await techRoadmapService.deleteCourseAssignment(req.params.id);
        return res.status(200).json(result);
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error deleting course assignment'
        });
    }
};

// ==================== ASSIGNMENT RETRIEVAL ====================

/**
 * Get all tech roadmap assignments
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllAssignments = async (req, res) => {
    try {
        const result = await techRoadmapService.getAllAssignments();
        return res.status(result.status).json(result);
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error retrieving all assignments'
        });
    }
};

// ==================== DOCUMENT MANAGEMENT ====================

/**
 * Upload document for a course assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const uploadDocument = async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                status: 0,
                message: 'No file uploaded'
            });
        }

        const assignmentId = req.body.assignmentId;
        if (!assignmentId) {
            return res.status(400).json({
                status: 0,
                message: 'Assignment ID is required'
            });
        }

        const result = await techRoadmapService.uploadDocument(assignmentId, req.file);
        return res.status(result.status).json(result);
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error uploading document'
        });
    }
};

/**
 * Update document approval status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateDocumentApprovalStatus = async (req, res) => {
    try {
        const { assignmentId, documentId, status, comment } = req.body;

        if (!assignmentId || !documentId || !status) {
            return res.status(400).json({
                status: 0,
                message: 'Assignment ID, Document ID, and status are required'
            });
        }

        // Get user ID from request if available, or use a default value
        const userId = req.user && req.user.id ? req.user.id : 'system';
        const result = await techRoadmapService.updateDocumentApprovalStatus(
            assignmentId,
            documentId,
            status,
            userId,
            comment
        );

        return res.status(200).json({
            status: 1,
            message: `Document ${status} successfully`,
            data: result
        });
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error updating document approval status'
        });
    }
};

// ==================== COMMENT MANAGEMENT ====================

/**
 * Update comments for a course assignment
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateComments = async (req, res) => {
    try {
        const { assignmentId, commentType, comment } = req.body;

        if (!assignmentId || !commentType || comment === undefined) {
            return res.status(400).json({
                status: 0,
                message: 'Assignment ID, comment type, and comment are required'
            });
        }

        const result = await techRoadmapService.updateComments(
            assignmentId,
            commentType,
            comment
        );

        return res.status(200).json({
            status: 1,
            message: `${commentType.charAt(0).toUpperCase() + commentType.slice(1)} comment updated successfully`,
            data: result
        });
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error updating comments'
        });
    }
};

// ==================== ASSIGNMENT UPDATE ====================

/**
 * Update assignment details
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateAssignment = async (req, res) => {
    try {
        const result = await techRoadmapService.updateAssignment(req.params.assignmentId, req.body);
        return res.status(200).json({
            status: 1,
            message: 'Assignment updated successfully',
            data: result
        });
    } catch (error) {
        return res.status(error.statusCode || 400).json({
            status: 0,
            message: error.message || 'Error updating assignment'
        });
    }
};

module.exports = {
    // Course management
    addCourse,
    getAllCourses,
    // Assignment management
    assignCourse,
    getMenteeCourses,
    updateCourseStatus,
    deleteCourseAssignment,
    updateAssignment,
    // Assignment retrieval
    getAllAssignments,
    // Document management
    uploadDocument,
    updateDocumentApprovalStatus,
    // Comment management
    updateComments
};


