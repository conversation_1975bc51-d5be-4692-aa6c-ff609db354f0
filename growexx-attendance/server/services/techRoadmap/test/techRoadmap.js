module.exports = {
    // ==================== COURSE MANAGEMENT TEST DATA ====================
    addCourse: [{
        it: 'As an admin/HR, I should validate if courseName is not passed',
        options: {
            description: 'Test course description',
            duration: '4 weeks',
            learningMedium: 'Online'
        },
        status: 0
    },
    {
        it: 'As an admin/HR, I should validate if courseName is passed as blank',
        options: {
            courseName: '',
            description: 'Test course description',
            duration: '4 weeks',
            learningMedium: 'Online'
        },
        status: 0
    },
    {
        it: 'As an admin/HR, I should validate if courseName is too short',
        options: {
            courseName: 'A',
            description: 'Test course description',
            duration: '4 weeks',
            learningMedium: 'Online'
        },
        status: 0
    },
    {
        it: 'As an admin/HR, I should validate if description is too long',
        options: {
            courseName: 'Valid Course Name',
            description: 'A'.repeat(1001), // Exceeds max length
            duration: '4 weeks',
            learningMedium: 'Online'
        },
        status: 0
    }],

    // ==================== ASSIGNMENT MANAGEMENT TEST DATA ====================
    assignCourse: [{
        it: 'As an admin/HR, I should validate if courseName is not passed',
        options: {
            description: 'Test assignment',
            menteeId: '615efb36423cae5909adcbac',
            mentorId: '5f083c352a7908662c334532'
        },
        status: 0
    },
    {
        it: 'As an admin/HR, I should validate if menteeId is not passed',
        options: {
            courseName: 'Test Course',
            description: 'Test assignment',
            mentorId: '5f083c352a7908662c334532'
        },
        status: 0
    },
    {
        it: 'As an admin/HR, I should validate if mentorId is not passed',
        options: {
            courseName: 'Test Course',
            description: 'Test assignment',
            menteeId: '615efb36423cae5909adcbac'
        },
        status: 0
    },
    {
        it: 'As an admin/HR, I should validate if menteeId is invalid ObjectId',
        options: {
            courseName: 'Test Course',
            description: 'Test assignment',
            menteeId: 'invalid-id',
            mentorId: '5f083c352a7908662c334532'
        },
        status: 0
    },
    {
        it: 'As an admin/HR, I should validate if mentorId is invalid ObjectId',
        options: {
            courseName: 'Test Course',
            description: 'Test assignment',
            menteeId: '615efb36423cae5909adcbac',
            mentorId: 'invalid-id'
        },
        status: 0
    },
    {
        it: 'As an admin/HR, I should validate if dueDate is invalid format',
        options: {
            courseName: 'Test Course',
            description: 'Test assignment',
            menteeId: '615efb36423cae5909adcbac',
            mentorId: '5f083c352a7908662c334532',
            dueDate: 'invalid-date'
        },
        status: 0
    }],

    // ==================== UPDATE COURSE STATUS TEST DATA ====================
    updateCourseStatus: [{
        it: 'As a mentee, I should validate if completionStatus is invalid',
        options: {
            completionStatus: 'InvalidStatus'
        },
        status: 0
    },
    {
        it: 'As a mentee, I should validate if rating is below minimum',
        options: {
            completionStatus: 'Completed',
            rating: -1
        },
        status: 0
    },
    {
        it: 'As a mentee, I should validate if rating is above maximum',
        options: {
            completionStatus: 'Completed',
            rating: 6
        },
        status: 0
    },
    {
        it: 'As a mentee, I should validate if completionPercentage is below minimum',
        options: {
            completionStatus: 'Completed',
            completionPercentage: -1
        },
        status: 0
    },
    {
        it: 'As a mentee, I should validate if completionPercentage is above maximum',
        options: {
            completionStatus: 'Completed',
            completionPercentage: 101
        },
        status: 0
    }],

    // ==================== DOCUMENT APPROVAL TEST DATA ====================
    documentApproval: [{
        it: 'As a mentor, I should validate if assignmentId is not passed',
        options: {
            documentId: '5f083c352a7908662c334532',
            status: 'approved',
            comment: 'Good work'
        },
        status: 0
    },
    {
        it: 'As a mentor, I should validate if documentId is not passed',
        options: {
            assignmentId: '615efb36423cae5909adcbac',
            status: 'approved',
            comment: 'Good work'
        },
        status: 0
    },
    {
        it: 'As a mentor, I should validate if status is not passed',
        options: {
            assignmentId: '615efb36423cae5909adcbac',
            documentId: '5f083c352a7908662c334532',
            comment: 'Good work'
        },
        status: 0
    },
    {
        it: 'As a mentor, I should validate if status is invalid',
        options: {
            assignmentId: '615efb36423cae5909adcbac',
            documentId: '5f083c352a7908662c334532',
            status: 'invalid-status',
            comment: 'Good work'
        },
        status: 0
    },
    {
        it: 'As a mentor, I should validate if assignmentId is invalid ObjectId',
        options: {
            assignmentId: 'invalid-id',
            documentId: '5f083c352a7908662c334532',
            status: 'approved',
            comment: 'Good work'
        },
        status: 0
    }],

    // ==================== COMMENT UPDATE TEST DATA ====================
    updateComments: [{
        it: 'As a user, I should validate if assignmentId is not passed',
        options: {
            commentType: 'mentor',
            comment: 'Test comment'
        },
        status: 0
    },
    {
        it: 'As a user, I should validate if commentType is not passed',
        options: {
            assignmentId: '615efb36423cae5909adcbac',
            comment: 'Test comment'
        },
        status: 0
    },
    {
        it: 'As a user, I should validate if commentType is invalid',
        options: {
            assignmentId: '615efb36423cae5909adcbac',
            commentType: 'invalid-type',
            comment: 'Test comment'
        },
        status: 0
    },
    {
        it: 'As a user, I should validate if assignmentId is invalid ObjectId',
        options: {
            assignmentId: 'invalid-id',
            commentType: 'mentor',
            comment: 'Test comment'
        },
        status: 0
    }],

    // ==================== OBJECT ID VALIDATION TEST DATA ====================
    invalidObjectIds: [{
        it: 'Should validate invalid ObjectId format - too short',
        id: '123',
        status: 0
    },
    {
        it: 'Should validate invalid ObjectId format - too long',
        id: '507f1f77bcf86cd799439011123',
        status: 0
    },
    {
        it: 'Should validate invalid ObjectId format - invalid characters',
        id: '507f1f77bcf86cd799439xyz',
        status: 0
    },
    {
        it: 'Should validate empty ObjectId',
        id: '',
        status: 0
    }]
};
