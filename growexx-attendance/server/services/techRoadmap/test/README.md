# TechRoadmap Test Cases

This directory contains comprehensive test cases for the TechRoadmap functionality, covering all authentication scenarios and business logic as specified.

## Test Files

### 1. `techRoadmap.js`
Contains test data for various validation scenarios including:
- Course management validation data
- Assignment management validation data
- Document approval validation data
- Comment update validation data
- ObjectId validation test cases

### 2. `techRoadmap.test.js`
Main test file containing all test suites for the TechRoadmap functionality.

## Authentication Logic Implementation

The tests implement the three authentication conditions as specified:

### 1. Admin/HR Access
- **Condition**: When an admin or HR is logged in
- **Capabilities**: Can see all details and add courses
- **Implementation**: 
  - Admin token (role: 4) and HR token (role: 6) are used
  - Tests verify access to course creation endpoints
  - Tests verify access to all management features

### 2. Mentor Access
- **Condition**: Mentor login detected by seeing mentee ID in URL
- **Capabilities**: Can approve or reject documents
- **Implementation**:
  - Mentor token (role: 1) is used
  - Tests verify document approval/rejection functionality
  - Tests verify access to mentor-specific features

### 3. Mentee Access
- **Condition**: When login is not from admin/HR and no mentee ID in URL
- **Capabilities**: Can access their own courses and update status
- **Implementation**:
  - Mentee token (role: 1) is used
  - Tests verify access to own course data
  - Tests verify course status update functionality

## Test Categories

### Course Management Tests
- Course addition validation (Admin/HR only)
- Course retrieval (All authenticated users)
- Input validation for course data
- Role-based access control

### Assignment Management Tests
- Course assignment to mentees
- Assignment retrieval
- Assignment validation
- ObjectId validation

### Course Status Update Tests
- Completion status updates
- Rating validation
- Percentage validation
- Status enum validation

### Document Management Tests
- Document upload (public endpoint)
- Document approval/rejection (mentor access)
- Document validation
- Authentication requirements

### Comment Management Tests
- Mentor comment updates
- Mentee comment updates
- Comment type validation
- Authentication requirements

### Assignment Deletion Tests
- Assignment deletion validation
- ObjectId validation
- Authentication requirements

### Authentication Logic Tests
- Admin privilege verification
- HR privilege verification
- Mentor access patterns
- Mentee access patterns
- Role-based restrictions

### Edge Cases and Error Handling
- Missing authorization headers
- Invalid JWT tokens
- Expired JWT tokens
- Malformed request bodies

## Token Configuration

The tests use JWT tokens for different user roles:

```javascript
// Admin Token (role: 4)
const adminUser = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>',
    role: 4
};

// HR Token (role: 6)
const hrUser = {
    id: '5f5f2cd2f1472c3303b6b862',
    email: '<EMAIL>',
    role: 6
};

// Mentor Token (role: 1)
const mentorUser = {
    id: '5f5f2cd2f1472c3303b6b863',
    email: '<EMAIL>',
    role: 1
};

// Mentee Token (role: 1)
const menteeUser = {
    id: '5f5f2cd2f1472c3303b6b864',
    email: '<EMAIL>',
    role: 1
};
```

## Running the Tests

To run the TechRoadmap tests specifically:

```bash
# Run all tests (includes TechRoadmap)
npm test

# Or run with mocha directly
npx mocha server/services/techRoadmap/test/techRoadmap.test.js
```

## Test Coverage

The test suite covers:
- ✅ All API endpoints in the TechRoadmap service
- ✅ Input validation for all request parameters
- ✅ Authentication and authorization logic
- ✅ Role-based access control
- ✅ ObjectId validation
- ✅ Error handling scenarios
- ✅ Edge cases and malformed requests
- ✅ JWT token validation (valid, invalid, expired)

## Notes

1. **No Logic Changes**: These tests only validate the existing functionality without modifying any business logic.

2. **Authentication Patterns**: The tests follow the specified authentication patterns:
   - Admin/HR: Full access to course management
   - Mentor: Document approval/rejection capabilities
   - Mentee: Access to own courses and status updates

3. **Validation Coverage**: Comprehensive validation testing for all input parameters, following the patterns established in other service tests.

4. **Error Scenarios**: Tests cover various error scenarios including invalid ObjectIds, missing parameters, unauthorized access, and malformed requests.

5. **Integration**: The tests are integrated into the main test suite via `test/alltests.js`.
