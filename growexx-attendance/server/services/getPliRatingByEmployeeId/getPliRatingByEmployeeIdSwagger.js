/**
 * @name getPliRatingByEmployeeIdSwagger
 * <AUTHOR>
 */
const message = require('../../locales/en.json');

module.exports = (swaggerJson) => {
    swaggerJson.paths['/api/pli-rating/by-employee-id'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: ['PLI Rating'],
            summary: 'Get PLI ratings by employee ID',
            description: 'This API is used to retrieve all PLI ratings for a specific employee using their employee ID',
            parameters: [
                {
                    in: 'query',
                    name: 'employeeId',
                    type: 'integer',
                    required: true,
                    description: 'Employee ID to fetch PLI ratings for'
                }
            ],
            responses: {
                200: {
                    description: 'Success response with PLI ratings data',
                    schema: {
                        $ref: '#/definitions/GetPliRatingByEmployeeIdSuccess'
                    }
                },
                400: {
                    description: 'Invalid input',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                404: {
                    description: 'User not found',
                    schema: {
                        $ref: '#/definitions/notFoundError'
                    }
                },
                500: {
                    description: 'Internal server error',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.GetPliRatingByEmployeeIdSuccess = {
        type: 'object',
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'array',
                items: {
                    $ref: '#/definitions/PliRatingDetail'
                }
            },
            message: {
                type: 'string',
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.PliRatingDetail = {
        type: 'object',
        properties: {
            _id: {
                type: 'string',
                example: '60a3d1b9e5c3a1234567890a'
            },
            mentee: {
                type: 'object',
                properties: {
                    name: {
                        type: 'string',
                        example: 'John Doe'
                    },
                    email: {
                        type: 'string',
                        example: '<EMAIL>'
                    },
                    employeeId: {
                        type: 'number',
                        example: 12345
                    }
                }
            },
            mentor: {
                type: 'object',
                properties: {
                    name: {
                        type: 'string',
                        example: 'Jane Smith'
                    },
                    email: {
                        type: 'string',
                        example: '<EMAIL>'
                    },
                    employeeId: {
                        type: 'number',
                        example: 67890
                    }
                }
            },
            month: {
                type: 'number',
                example: 5
            },
            year: {
                type: 'number',
                example: 2023
            },
            projectRatings: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        project: {
                            type: 'object',
                            properties: {
                                name: {
                                    type: 'string',
                                    example: 'Project Alpha'
                                },
                                code: {
                                    type: 'string',
                                    example: 'ALPHA-001'
                                },
                                client: {
                                    type: 'string',
                                    example: 'Client XYZ'
                                }
                            }
                        },
                        projectWeightage: {
                            type: 'number',
                            example: 70
                        },
                        parameterScores: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    parameter: {
                                        type: 'object',
                                        properties: {
                                            name: {
                                                type: 'string',
                                                example: 'Technical Skills'
                                            },
                                            type: {
                                                type: 'string',
                                                example: 'Dedicated'
                                            },
                                            parentName: {
                                                type: 'string',
                                                example: 'Technical Skills',
                                                description: 'Parent parameter name'
                                            }
                                        }
                                    },
                                    autoFilled: {
                                        type: 'boolean',
                                        example: true
                                    },
                                    score: {
                                        type: 'number',
                                        example: 4.5
                                    },
                                    comments: {
                                        type: 'string',
                                        example: 'Good technical knowledge'
                                    },
                                    childScores: {
                                        type: 'array',
                                        items: {
                                            type: 'object',
                                            properties: {
                                                childParameter: {
                                                    type: 'string',
                                                    example: 'Code Quality'
                                                },
                                                childParameterWeightage: {
                                                    type: 'number',
                                                    example: 25,
                                                    description: 'Weightage of the child parameter based on role and project type'
                                                },
                                                calculation: {
                                                    type: 'number',
                                                    example: 4.2,
                                                    description: 'Average of all sprint scores for this child parameter'
                                                },
                                                weightageAverage: {
                                                    type: 'number',
                                                    example: 1.05,
                                                    description: 'Weighted contribution (calculation * childParameterWeightage)'
                                                },
                                                sprintScores: {
                                                    type: 'array',
                                                    items: {
                                                        type: 'object',
                                                        properties: {
                                                            sprintNumber: {
                                                                type: 'string',
                                                                example: 'Sprint 1'
                                                            },
                                                            score: {
                                                                type: 'number',
                                                                example: 4.2
                                                            },
                                                            comment: {
                                                                type: 'string',
                                                                example: 'Good code quality with proper documentation'
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            proactivenessScores: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        criteria: {
                            type: 'string',
                            example: 'Meeting Deadlines'
                        },
                        score: {
                            type: 'number',
                            example: 4.0
                        }
                    }
                }
            },
            techRoadmapRating: {
                type: 'number',
                example: 4.2
            },
            techRoadmapComments: {
                type: 'string',
                example: 'Good progress on technical roadmap'
            },
            techRoadmapDocumentLink: {
                type: 'string',
                example: 'https://docs.example.com/roadmap/12345'
            },
            status: {
                type: 'string',
                example: 'Finalized'
            },
            menteeQuery: {
                type: 'string',
                example: 'I believe my score for technical skills should be higher'
            },
            mentorResponse: {
                type: 'string',
                example: 'Your technical skills are good but need more consistency'
            },
            superAdminOverride: {
                type: 'boolean',
                example: false
            },
            superAdminComments: {
                type: 'string',
                example: ''
            },
            finalScore: {
                type: 'number',
                example: 4.3
            },
            isFrozen: {
                type: 'boolean',
                example: true
            },
            createdAt: {
                type: 'string',
                format: 'date-time',
                example: '2023-05-15T10:30:00.000Z'
            },
            updatedAt: {
                type: 'string',
                format: 'date-time',
                example: '2023-05-20T14:45:00.000Z'
            }
        }
    };

    // Add validation error definition if it doesn't exist
    if (!swaggerJson.definitions.validationError) {
        swaggerJson.definitions.validationError = {
            properties: {
                status: {
                    type: 'number',
                    example: 0
                },
                message: {
                    type: 'string',
                    example: message.INVALID_REQUEST
                }
            }
        };
    }

    // Add not found error definition if it doesn't exist
    if (!swaggerJson.definitions.notFoundError) {
        swaggerJson.definitions.notFoundError = {
            properties: {
                status: {
                    type: 'number',
                    example: 0
                },
                message: {
                    type: 'string',
                    example: message.USER_NOT_FOUND
                }
            }
        };
    }

    return swaggerJson;
};
