/**
 * @name getPliRatingByEmployeeIdController
 * <AUTHOR>
 */
const getPliRatingByEmployeeIdService = require('./getPliRatingByEmployeeIdService');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for get PLI rating by employee ID
 */
class GetPliRatingByEmployeeIdController {

    /**
     * @desc This function is used to get PLI rating by employee ID
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getPliRatingByEmployeeId (req, res) {
        try {
            const data = await getPliRatingByEmployeeIdService.getPliRatingByEmployeeId(req, res.locals.user, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = GetPliRatingByEmployeeIdController;
