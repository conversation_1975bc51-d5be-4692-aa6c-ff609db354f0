/**
 * @name getPliRatingByEmployeeIdValidator
 * <AUTHOR>
 */
const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');
const locale = require('../../locales/en.json');

/**
 * Class represents validations for get PLI rating by employee ID
 */
class GetPliRatingByEmployeeIdValidator extends validation {
    constructor () {
        super();
        this.validateGetPliRatingByEmployeeId = this.validateGetPliRatingByEmployeeId.bind(this);
    }

    /**
     * @desc This function is used to validate get PLI rating by employee ID
     * @param {Number} employeeId employeeId
     * @param {String} key key
     */
    validateGetPliRatingByEmployeeId (employeeId, key) {
        if (!employeeId) {
            throw new GeneralError(locale.FIELD_REQUIRED.replace('{0}', key), 400);
        }

        if (isNaN(parseInt(employeeId, 10))) {
            throw new GeneralError(locale.INVALID_EMPLOYEE_ID, 400);
        }
    }
}

module.exports = new GetPliRatingByEmployeeIdValidator();
