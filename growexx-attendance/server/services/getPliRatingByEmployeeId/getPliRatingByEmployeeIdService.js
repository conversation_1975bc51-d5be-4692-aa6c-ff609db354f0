/**
 * @name getPliRatingByEmployeeIdService
 * <AUTHOR>
 */
const PLIRating = require('../../models/pliRating.model');
const User = require('../../models/user.model');
const Project = require('../../models/project.model');
const PLIParameters = require('../../models/pliParameters.model');
const GeneralError = require('../../util/GeneralError');
// ProjectSprintDataService is used by GetPliRatingByIdService
const ProjectSprintDataService = require('../getProjectSprintData/projectSprintDataService');
const GetPliRatingByIdService = require('../getPliRatingById/getPliRatingByIdService');
// Locale will be passed as a parameter

class GetPliRatingByEmployeeIdService {
    constructor() {
        this.getPliRatingByEmployeeId = this.getPliRatingByEmployeeId.bind(this);
        this.validateGetPliRatingByEmployeeId = this.validateGetPliRatingByEmployeeId.bind(this);
    }

    /**
     * @desc This function is used to validate the input
     * @param {Number} employeeId employeeId
     * @param {String} key key
     * @param {Object} locale Localization object
     */
    validateGetPliRatingByEmployeeId(employeeId, key, locale) {
        if (!employeeId) {
            throw new GeneralError(locale.FIELD_REQUIRED.replace('{0}', key), 400);
        }
    }

    /**
     * @desc This function is used to get PLI ratings by employee ID
     * @param {Object} req Request
     * @param {Object} user User
     * @param {Object} locale Localization object
     * @return {Object} Response
     */
    async getPliRatingByEmployeeId(req, user, locale) {
        try {
            const { employeeId } = req.query;

            // Validate input
            this.validateGetPliRatingByEmployeeId(employeeId, 'Employee ID', locale);

            // Find user by employee ID
            const employee = await User.findOne({ employeeId: parseInt(employeeId, 10) });
            if (!employee) {
                throw new GeneralError(locale.USER_NOT_FOUND, 404);
            }

            // Get PLI ratings for the user
            const pliRatings = await PLIRating.find({ menteeId: employee._id });
            if (!pliRatings || !pliRatings.length) {
                return { status: 0, message: locale.NO_PLI_RATINGS_FOUND };
            }

            // Create response with enhanced data
            const response = [];

            // Process each PLI rating
            for (let i = 0; i < pliRatings.length; i++) {
                const pliRating = pliRatings[i].toObject();
                
                // Replace mentee ID with mentee details
                if (pliRating.menteeId) {
                    const mentee = await User.findById(pliRating.menteeId);
                    if (mentee) {
                        const menteeName = `${mentee.firstName} ${mentee.lastName}`.trim();
                        const menteeData = {
                            name: menteeName,
                            email: mentee.email,
                            employeeId: mentee.employeeId
                        };
                        pliRating.mentee = menteeData;
                        delete pliRating.menteeId;
                    }
                }

                // Replace mentor ID with mentor details
                if (pliRating.mentorId) {
                    const mentor = await User.findById(pliRating.mentorId);
                    if (mentor) {
                        const mentorName = `${mentor.firstName} ${mentor.lastName}`.trim();
                        const mentorData = {
                            name: mentorName,
                            email: mentor.email,
                            employeeId: mentor.employeeId
                        };
                        pliRating.mentor = mentorData;
                        delete pliRating.mentorId;
                    }
                }

                // Process project ratings
                for (let j = 0; j < pliRating.projectRatings.length; j++) {
                    const projectRating = pliRating.projectRatings[j];

                    // Replace project ID with project details
                    if (projectRating.projectId) {
                        const project = await Project.findById(projectRating.projectId);
                        if (project) {
                            const projectData = {
                                name: project.projectName,
                                code: project.projectCode,
                                client: project.clientName
                            };
                            projectRating.project = projectData;
                            delete projectRating.projectId;
                        }
                    }

                    // Process parameter scores
                    for (let k = 0; k < projectRating.parameterScores.length; k++) {
                        const parameterScore = projectRating.parameterScores[k];

                        // With the new structure, we use the projectType and parentParameter fields directly
                        // instead of looking up the parameter details from the PLI parameters collection
                        if (parameterScore.parameterId) {
                            try {
                                // Get the single PLI parameters document
                                const pliParametersDoc = await PLIParameters.findOne({ isActive: 1 });

                                if (pliParametersDoc) {
                                    // Replace parameterId with parameter details from the parameterScore itself
                                    parameterScore.parameter = {
                                    name: parameterScore.parentParameter,
                                    type: parameterScore.projectType
                                    };

                                    // Remove fields we've extracted into the parameter object
                                    delete parameterScore.parameterId;
                                    delete parameterScore.projectType;
                                    delete parameterScore.parentParameter;

                                    // For child scores, rename childParameterId to childParameter and add the three new fields
                                    for (let l = 0; l < parameterScore.childScores.length; l++) {
                                    const childScore = parameterScore.childScores[l];
                                    childScore.childParameter = childScore.childParameterId;
                                    delete childScore.childParameterId;
                                    
                                    // Add the three new fields:
                                    try {
                                        // 1. childParameterWeightage - Get from PLI parameters based on role
                                        // Get the mentee details from the database if needed
                                        let userDesignation = null;
                                        if (employee && employee.designation) {
                                            userDesignation = employee.designation;
                                        } else {
                                            userDesignation = 'Software Engineer'; // Default designation
                                        }
                                        
                                        // Call the getChildParameterWeighatge method from GetPliRatingByIdService
                                        childScore.childParameterWeightage = await GetPliRatingByIdService.getChildParameterWeighatge(
                                            userDesignation,
                                            parameterScore.parameter.type,
                                            parameterScore.parameter.name,
                                            childScore.childParameter
                                        );
                                        
                                        // 2. calculation - average of all sprint scores
                                        if (childScore.sprintScores && childScore.sprintScores.length > 0) {
                                            // Filter out any non-numeric scores
                                            const scores = childScore.sprintScores
                                                .map(sprint => Number(sprint.score))
                                                .filter(score => !isNaN(score));
                                                
                                            if (scores.length > 0) {
                                                const totalScore = scores.reduce((sum, score) => sum + score, 0);
                                                childScore.calculation = Number((totalScore / scores.length).toFixed(2));
                                            } else {
                                                childScore.calculation = 0;
                                            }
                                        } else {
                                            childScore.calculation = 0;
                                        }
                                        
                                        // 3. weightageAverage - calculation * childParameterWeightage
                                        childScore.weightageAverage = Number(
                                            ((childScore.calculation * childScore.childParameterWeightage)).toFixed(2)
                                        );
                                    } catch (error) {
                                        // Set default values if there's an error
                                        childScore.childParameterWeightage = 0;
                                        childScore.calculation = 0;
                                        childScore.weightageAverage = 0;
                                    }
                                    }
                                } else {
                                    // If no active PLI parameters document is found, still create the parameter object
                                    // with available data to maintain consistency
                                    parameterScore.parameter = {
                                    name: parameterScore.parentParameter,
                                    type: parameterScore.projectType
                                    };
                                    delete parameterScore.parameterId;
                                    delete parameterScore.projectType;
                                    delete parameterScore.parentParameter;
                                }
                            } catch (paramError) {
                                // Handle the error silently but continue processing
                                // We don't want to break the entire response if one parameter fails
                                // Still create the parameter object with available data
                                parameterScore.parameter = {
                                    name: parameterScore.parentParameter,
                                    type: parameterScore.projectType
                                };
                                delete parameterScore.parameterId;
                                delete parameterScore.projectType;
                                delete parameterScore.parentParameter;
                            }
                        }
                    }
                }

                response.push(pliRating);
            }

            return { status: 1, data: response };
        } catch (error) {
            return { status: 0, message: error.message };
        }
    }
}

module.exports = new GetPliRatingByEmployeeIdService();
