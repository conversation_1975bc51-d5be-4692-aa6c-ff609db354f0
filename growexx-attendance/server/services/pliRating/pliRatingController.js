const PLIRatingService = require("./pliRatingService");
const Utils = require("../../util/utilFunctions");

/**
 * Class represents controller for PLI Rating
 */
class PLIRatingController {
  /**
   * @desc This function is being used to get PLI rating for mentee
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {Object} req.query RequestQuery
   * @param {function} res Response
   */
  static async getPLIRating(req, res) {
    try {
      const data = await PLIRatingService.getPLIRating(req.query, res.__);
      Utils.sendResponse(null, data, res, res.__("SUCCESS"));
    } catch (error) {
      Utils.sendResponse(error, null, res, error.message);
    }
  }

  /**
   * @desc This function is being used to update PLI rating
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */
  static async updatePLIRating(req, res) {
    try {
      const data = await PLIRatingService.updatePLIRating(req, res.__);
      Utils.sendResponse(null, data, res, res.__("SUCCESS"));
    } catch (error) {
      Utils.sendResponse(error, null, res, error.message);
    }
  }

  /**
   * @desc This function is being used to submit PLI rating
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {Object} req.params RequestParams
   * @param {function} res Response
   */
  static async submitPLIRating(req, res) {
    try {
      const data = await PLIRatingService.submitPLIRating(req, res.__);
      Utils.sendResponse(null, data, res, res.__("SUCCESS"));
    } catch (error) {
      Utils.sendResponse(error, null, res, error.message);
    }
  }

  /**
   * @desc This function is being used to review PLI rating
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {Object} req.params RequestParams
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */
  static async reviewPLIRating(req, res) {
    try {
      const data = await PLIRatingService.reviewPLIRating(req, res.__);
      Utils.sendResponse(null, data, res, res.__("SUCCESS"));
    } catch (error) {
      Utils.sendResponse(error, null, res, error.message);
    }
  }

  /**
   * @desc This function is being used to auto-fill PLI rating scores from RAG reports
   * <AUTHOR>
   * @since 05/01/2025
   * @param {Object} req Request
   * @param {Object} req.query RequestQuery
   * @param {string} req.query.menteeId Mentee ID
   * @param {number} req.query.month Month (1-12)
   * @param {number} req.query.year Year
   * @param {function} res Response
   */
  static async autoFillFromRAG(req, res) {
    try {
      const data = await PLIRatingService.autoFillFromRAG(req.query, res.__);
      Utils.sendResponse(null, data, res, res.__("PLI_RATING_AUTO_FILLED"));
    } catch (error) {
      Utils.sendResponse(error, null, res, error.message);
    }
  }
}

module.exports = PLIRatingController;
