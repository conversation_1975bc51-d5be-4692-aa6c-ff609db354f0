const message = require("../../locales/en.json");

module.exports = (swaggerJson) => {
  swaggerJson.paths["/api/pli-rating"] = {
    get: {
      tags: ["PLI Rating"],
      description: "Get PLI rating for mentee",
      summary: "Get PLI rating for mentee",
      parameters: [
        {
          in: "query",
          name: "menteeId",
          description: "Mentee ID",
          required: true,
          type: "string",
        },
        {
          in: "query",
          name: "month",
          description: "Month (1-12)",
          required: true,
          type: "number",
        },
        {
          in: "query",
          name: "year",
          description: "Year",
          required: true,
          type: "number",
        },
      ],
      responses: {
        200: {
          description: "PLI rating fetched successfully.",
          schema: {
            $ref: "#/definitions/successPLIRating",
          },
        },
        400: {
          description: "Invalid request",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
      },
    },
  };

  swaggerJson.paths["/api/pli-rating/{id}"] = {
    put: {
      tags: ["PLI Rating"],
      description: "Update PLI rating",
      summary: "Update PLI rating",
      parameters: [
        {
          in: "header",
          name: "Authorization",
          description: "Authorization token",
          required: true,
          type: "string",
        },
        {
          in: "path",
          name: "id",
          description: "PLI rating ID",
          required: true,
          type: "string",
        },
        {
          in: "body",
          name: "body",
          description: "Body parameter",
          required: true,
          schema: {
            $ref: "#/definitions/pliRatingInput",
          },
        },
      ],
      responses: {
        200: {
          description: "PLI rating updated successfully.",
          schema: {
            $ref: "#/definitions/successPLIRating",
          },
        },
        400: {
          description: "Invalid request",
          schema: {
            $ref: "#/definitions/validationError",
          },
        },
        401: {
          description: "Unauthorized Access",
          schema: {
            $ref: "#/definitions/unauthorisedAccess",
          },
        },
      },
    },
  };

  // Define PLI Rating schema
  swaggerJson.definitions.pliRatingInput = {
    type: "object",
    properties: {
      projectRatings: {
        type: "array",
        items: {
          type: "object",
          properties: {
            projectId: {
              type: "string",
              example: "507f1f77bcf86cd799439011",
            },
            projectWeightage: {
              type: "number",
              example: 40,
            },
            parameterScores: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  parameterId: {
                    type: "string",
                    example: "507f1f77bcf86cd799439012",
                  },
                  score: {
                    type: "number",
                    example: 4,
                  },
                  comments: {
                    type: "string",
                    example: "Good performance",
                  },
                },
              },
            },
          },
        },
      },
    },
  };

  swaggerJson.definitions.successPLIRating = {
    properties: {
      status: {
        type: "number",
        example: 1,
      },
      data: {
        type: "object",
        $ref: "#/definitions/pliRatingInput",
      },
      message: {
        example: message.SUCCESS,
      },
    },
  };

  return swaggerJson;
};
