/**
 * Service to get or create PLI rating for a mentee
 * @param {Object} params - Query parameters
 * @param {string} params.menteeId - ID of the mentee
 * @param {number} params.month - Month (1-12)
 * @param {number} params.year - Year
 */
const PLIRating = require("../../models/pliRating.model");
const Project = require("../../models/project.model");
const PLIParameters = require("../../models/pliParameters.model");
const GeneralError = require("../../util/GeneralError");
const RAG = require("../../models/rag.model");

class PLIRatingService {
  static async getPLIRating(params, locale) {
    const { menteeId, month, year } = params;

    if (!menteeId || !month || !year) {
      throw new GeneralError(locale("FIELD_REQUIRED", "All fields"), 400);
    }

    // Check if PLI rating exists
    const existingPLIRating = await PLIRating.findOne({
      menteeId,
      month,
      year,
    }).populate({
      path: "projectRatings.projectId",
      populate: {
        path: "sprints",
      },
    });

    if (existingPLIRating) {
      return existingPLIRating;
    }

    // If not exists, create new PLI rating
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);

    const menteeProjects = await Project.find({
      "users.empId": menteeId,
      "users.startDate": { $lte: endDate },
      $or: [
        { "users.endDate": { $gte: startDate } },
        { "users.endDate": null },
      ],
    });

    const pliParameters = await PLIParameters.find({ isActive: 1 });

    const newPLIRating = new PLIRating({
      menteeId,
      month,
      year,
      status: "Draft",
      projectRatings: menteeProjects.map((project) => ({
        projectId: project._id,
        projectWeightage: 0,
        parameterScores: pliParameters.map((param) => ({
          parameterId: param._id,
          autoFilled: true,
          score: 0,
          comments: "",
          childScores: param.childParameters.map((child) => ({
            childParameterId: child._id,
            score: 0,
          })),
        })),
      })),
    });

    await newPLIRating.save();

    return PLIRating.findById(newPLIRating._id).populate({
      path: "projectRatings.projectId",
      populate: {
        path: "sprints",
      },
    });
  }

  /**
   * Auto-fill PLI rating scores from RAG reports
   * @param {Object} params - Query parameters
   * @param {string} params.menteeId - ID of the mentee
   * @param {number} params.month - Month (1-12)
   * @param {number} params.year - Year
   * @param {Object} locale - Localization function
   * @returns {Object} Updated PLI rating
   */
  static async autoFillFromRAG(params, locale) {
    const { menteeId, month, year } = params;

    if (!menteeId || !month || !year) {
      throw new GeneralError(locale("FIELD_REQUIRED", "All fields"), 400);
    }

    // 1. Fetch the existing PLI rating data for the given mentee
    const pliRating = await PLIRating.findOne({
      menteeId,
      month,
      year,
    });

    if (!pliRating) {
      throw new GeneralError(locale("PLI_RATING_NOT_FOUND"), 404);
    }

    // Create mapping for RAG metrics to PLI parameters
    // This mapping should be customized based on your specific PLI parameters
    const metricToParameterMapping = {
      effortVariance: "Effort Variance",
      openCloseRatio: "Open/Close Ratio",
      resolvedBugs: "Bug Resolution",
      totalBugs: "Bug Count",
      bugsCreatedDuringSprint: "Bugs Created",
      spillOverBugs: "Spillover Bugs",
      completedStoriesCount: "Completed Stories",
      totalPlannedStories: "Planned Stories",
      totalPlannedEfforts: "Planned Efforts",
      totalSpentEfforts: "Spent Efforts",
    };

    // Define start and end date for the month
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0);

    // 2. Process each project in the PLI rating
    for (const projectRating of pliRating.projectRatings) {
      // Get project details
      const project = await Project.findById(projectRating.projectId);

      if (!project) {
        continue; // Skip if project not found
      }

      // 3. Query RAG reports for this project in the given month
      const ragReports = await RAG.find({
        project: { $regex: new RegExp(project.projectName, "i") }, // Case-insensitive match
        sprintStart: { $lte: endDate },
        sprintEnd: { $gte: startDate },
      });

      if (ragReports.length === 0) {
        continue; // Skip if no RAG reports found
      }

      // 4. Extract and aggregate metrics from RAG reports
      const aggregatedMetrics = this.aggregateRAGMetrics(ragReports);

      // 5. Map RAG metrics to PLI parameters and update scores
      for (const parameterScore of projectRating.parameterScores) {
        // Get parameter details
        const parameter = await PLIParameters.findById(
          parameterScore.parameterId
        );

        if (!parameter) {
          continue; // Skip if parameter not found
        }

        // Check if this parameter matches any RAG metric
        let matched = false;
        for (const [metricKey, parameterName] of Object.entries(
          metricToParameterMapping
        )) {
          if (
            parameter.parentParameter.includes(parameterName) ||
            parameter.childParameters.some((child) =>
              child.name.includes(parameterName)
            )
          ) {
            // If there's a match, set the score based on the RAG metric
            if (aggregatedMetrics[metricKey] !== undefined) {
              // Convert the RAG metric to a score between 0-5
              // This conversion logic should be customized based on your scoring criteria
              const score = this.convertMetricToScore(
                metricKey,
                aggregatedMetrics[metricKey]
              );

              parameterScore.score = score;
              parameterScore.autoFilled = true;
              matched = true;

              // Also update child scores if applicable
              if (
                parameter.childParameters &&
                parameter.childParameters.length > 0
              ) {
                for (const childParam of parameter.childParameters) {
                  if (childParam.name.includes(parameterName)) {
                    const childScoreIndex =
                      parameterScore.childScores.findIndex(
                        (cs) =>
                          cs.childParameterId.toString() ===
                          childParam._id.toString()
                      );

                    if (childScoreIndex !== -1) {
                      parameterScore.childScores[childScoreIndex].score = score;
                    }
                  }
                }
              }
            }
          }
        }

        // If no match found, set score to 0 and mark as auto-filled
        if (!matched) {
          parameterScore.score = 0;
          parameterScore.autoFilled = true;
        }
      }
    }

    // 7. Save the updated PLI rating
    await pliRating.save();

    return pliRating;
  }

  /**
   * Aggregate metrics from multiple RAG reports
   * @param {Array} ragReports - Array of RAG reports
   * @returns {Object} Aggregated metrics
   */
  static aggregateRAGMetrics(ragReports) {
    // Initialize aggregated metrics
    const aggregatedMetrics = {
      effortVariance: 0,
      openCloseRatio: 0,
      resolvedBugs: 0,
      totalBugs: 0,
      bugsCreatedDuringSprint: 0,
      spillOverBugs: 0,
      completedStoriesCount: 0,
      totalPlannedStories: 0,
      totalPlannedEfforts: 0,
      totalSpentEfforts: 0,
    };

    // If no reports, return empty metrics
    if (ragReports.length === 0) {
      return aggregatedMetrics;
    }

    // Sum up all metrics
    for (const report of ragReports) {
      for (const key in aggregatedMetrics) {
        if (report[key] !== undefined && !isNaN(report[key])) {
          aggregatedMetrics[key] += report[key];
        }
      }
    }

    // Calculate averages for some metrics
    const metricsToAverage = ["effortVariance", "openCloseRatio"];
    for (const key of metricsToAverage) {
      aggregatedMetrics[key] = aggregatedMetrics[key] / ragReports.length;
    }

    return aggregatedMetrics;
  }

  /**
   * Convert RAG metric to PLI score (0-5)
   * @param {string} metricKey - The key of the metric
   * @param {number} metricValue - The value of the metric
   * @returns {number} Score between 0-5
   */
  static convertMetricToScore(metricKey, metricValue) {
    // This conversion logic should be customized based on your scoring criteria
    // Here's a simple example:
    switch (metricKey) {
      case "effortVariance":
        // Lower variance is better (0% variance = 5, 100% variance = 0)
        return Math.max(0, Math.min(5, 5 - Math.abs(metricValue) / 20));

      case "openCloseRatio":
        // Lower ratio is better (ratio of 0 = 5, ratio of 1 = 2.5, ratio of 2+ = 0)
        return Math.max(0, Math.min(5, 5 - metricValue * 2.5));

      case "resolvedBugs":
        // More resolved bugs is better (scale based on typical values)
        return Math.min(5, metricValue / 2);

      case "totalBugs":
        // Fewer bugs is better (inverse scale)
        return Math.max(0, 5 - metricValue / 4);

      case "bugsCreatedDuringSprint":
        // Fewer bugs created is better (inverse scale)
        return Math.max(0, 5 - metricValue / 4);

      case "spillOverBugs":
        // Fewer spillover bugs is better (inverse scale)
        return Math.max(0, 5 - metricValue / 2);

      case "completedStoriesCount":
        // More completed stories is better
        return Math.min(5, metricValue / 3);

      case "totalPlannedStories":
        // This is a reference metric, not scored directly
        return 0;

      case "totalPlannedEfforts":
        // This is a reference metric, not scored directly
        return 0;

      case "totalSpentEfforts":
        // This is a reference metric, not scored directly
        return 0;

      default:
        return 0;
    }
  }
}

module.exports = PLIRatingService;
