const chai = require("chai");
const chaiHttp = require("chai-http");
const sinon = require("sinon");
const mongoose = require("mongoose");
const expect = chai.expect;
const PliRating = require("../../../models/pliRating.model");
const Project = require("../../../models/project.model");
const PliParameter = require("../../../models/pliParameters.model");
const Rag = require("../../../models/rag.model");
const app = require("../../../server");

chai.use(chaiHttp);

describe("Auto-Fill PLI Ratings", () => {
  const mockMenteeId = new mongoose.Types.ObjectId();
  const mockMentorId = new mongoose.Types.ObjectId();
  const mockProjectId = new mongoose.Types.ObjectId();
  const mockParameterId = new mongoose.Types.ObjectId();

  const mockPliRating = {
    _id: new mongoose.Types.ObjectId(),
    menteeId: mockMenteeId,
    mentorId: mockMentorId,
    month: 7,
    year: 2022,
    projectRatings: [
      {
        projectId: mockProjectId,
        projectWeightage: 100,
        parameterScores: [
          {
            parameterId: mockParameterId,
            autoFilled: false,
            score: 0,
            comments: "",
            childScores: [],
          },
        ],
      },
    ],
    status: "Draft",
    save: sinon.stub().resolves(true),
  };

  const mockProject = {
    _id: mockProjectId,
    projectName: "Project A",
  };

  const mockParameter = {
    _id: mockParameterId,
    parentParameter: "Bug Resolution Efficiency",
  };

  const mockRagReports = [
    {
      _id: new mongoose.Types.ObjectId(),
      project: "Project A",
      sprintNumber: "Sprint 1",
      sprintStart: new Date("2022-07-01"),
      sprintEnd: new Date("2022-07-15"),
      openCloseRatio: 0.4,
      effortVariance: 0.1,
      totalBugs: 8,
      bugsCreatedDuringSprint: 5,
      resolvedBugs: 12,
      spillOverBugs: 2,
    },
  ];

  beforeEach(() => {
    sinon.restore();
  });

  describe("POST /api/pli-ratings/auto-fill", () => {
    it("should return 400 if required fields are missing", async () => {
      const res = await chai
        .request(app)
        .post("/api/pli-ratings/auto-fill")
        .send({ month: 7, year: 2022 }); // Missing menteeId

      expect(res).to.have.status(400);
      expect(res.body).to.have.property("success", false);
      expect(res.body.message).to.include("required fields");
    });

    it("should return 404 if PLI rating not found", async () => {
      sinon.stub(PliRating, "findOne").resolves(null);

      const res = await chai
        .request(app)
        .post("/api/pli-ratings/auto-fill")
        .send({
          menteeId: mockMenteeId.toString(),
          month: 7,
          year: 2022,
        });

      expect(res).to.have.status(404);
      expect(res.body).to.have.property("success", false);
      expect(res.body.message).to.include("not found");
    });

    it("should auto-fill PLI ratings from RAG data", async () => {
      // Mock PliRating.findOne
      sinon.stub(PliRating, "findOne").resolves(mockPliRating);

      // Mock Project.findById
      sinon.stub(Project, "findById").resolves(mockProject);

      // Mock PliParameter.findById
      sinon.stub(PliParameter, "findById").resolves(mockParameter);

      // Mock Rag.find
      sinon.stub(Rag, "find").resolves(mockRagReports);

      // Mock PliRating.findById (for final populated response)
      sinon.stub(PliRating, "findById").returns({
        populate: () => ({
          populate: () => ({
            populate: () => ({
              populate: () => ({
                lean: () => ({
                  ...mockPliRating,
                  projectRatings: [
                    {
                      ...mockPliRating.projectRatings[0],
                      projectId: mockProject,
                      parameterScores: [
                        {
                          ...mockPliRating.projectRatings[0].parameterScores[0],
                          parameterId: mockParameter,
                          autoFilled: true,
                          score: 5, // Expected score based on openCloseRatio of 0.4
                        },
                      ],
                    },
                  ],
                }),
              }),
            }),
          }),
        }),
      });

      const res = await chai
        .request(app)
        .post("/api/pli-ratings/auto-fill")
        .send({
          menteeId: mockMenteeId.toString(),
          month: 7,
          year: 2022,
        });

      expect(res).to.have.status(200);
      expect(res.body).to.have.property("success", true);
      expect(res.body)
        .to.have.property("message")
        .that.includes("auto-filled successfully");
      expect(res.body).to.have.property("data");

      // Verify specific parameter was auto-filled
      const paramScore = res.body.data.projectRatings[0].parameterScores[0];
      expect(paramScore).to.have.property("autoFilled", true);
      expect(paramScore).to.have.property("score", 5);
    });
  });
});
