const PLIParameters = require("../../models/pliParameters.model");
const PLIParametersValidator = require("./pliParametersValidator");
const GeneralError = require("../../util/GeneralError");

/**
 * Class represents services for PLI Parameters management
 */
class PLIParametersService {
  /**
   * @desc This function is being used to create PLI parameters
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req RequestpliParameters
   * @param {function} locale Translation function
   * @return {Array} Created PLI parameters
   */
  static async createPLIParameter(req, locale) {
    const Validator = new PLIParametersValidator(req.body, locale);
    await Validator.validateCreatePLIParameter();

    // Check if we have the new schema format with roleParameters
    if (req.body && req.body.roleParameters && Array.isArray(req.body.roleParameters)) {
      // Handle new schema format
      console.log("Processing new schema format with roleParameters");

      // Find existing parameters with the new schema
      const existingParameters = await PLIParameters.find({
        isActive: 1
      });

      // If we have an existing document, update it
      if (existingParameters.length > 0) {
        const existingParam = existingParameters[0];
        console.log("Found existing document to update");

        // Process each role parameter in the request
        for (const roleParam of req.body.roleParameters) {
          const { applicableRole, parameters } = roleParam;

          // Check if this role already exists in the document
          const existingRoleIndex = existingParam.roleParameters.findIndex(
            rp => rp.applicableRole === applicableRole
          );

          if (existingRoleIndex >= 0) {
            // Update existing role parameters
            console.log(`Updating existing role: ${applicableRole}`);
            existingParam.roleParameters[existingRoleIndex].parameters = parameters;
          } else {
            // Add new role parameters
            console.log(`Adding new role: ${applicableRole}`);
            existingParam.roleParameters.push({
              applicableRole,
              parameters
            });
          }
        }

        existingParam.updatedAt = Date.now();
        const savedParameter = await existingParam.save();
        return [savedParameter];
      } else {
        // Create a new document with the roleParameters structure
        console.log("Creating new document with roleParameters");
        const pliParameter = new PLIParameters({
          roleParameters: req.body.roleParameters,
          isActive: 1
        });

        const savedParameter = await pliParameter.save();
        return [savedParameter];
      }
    } else {
      // Handle old schema format for backward compatibility
      console.log("Processing old schema format");
      const parameters = Array.isArray(req.body) ? req.body : [req.body];
      const createdParameters = [];

      // First find any existing active parameters that match our categories
      const existingParameters = await PLIParameters.find({ isActive: 1 });

      // Process each parameter
      for (const parameter of parameters) {
        // Try to find a matching existing parameter
        const existingParam = existingParameters.find(
          (p) =>
            (parameter.key && p.parentParameter === parameter.parentParameter) ||
            (p.parentParameter === parameter.parentParameter &&
              p.projectType === parameter.projectType)
        );

        if (existingParam) {
          // Update existing parameter
          existingParam.parentWeightage = parameter.parentWeightage;
          existingParam.projectType = parameter.projectType || "Dedicated";
          existingParam.applicableRoles = parameter.applicableRoles;
          existingParam.childParameters = parameter.childParameters;
          existingParam.updatedAt = Date.now();

          const savedParameter = await existingParam.save();
          createdParameters.push(savedParameter);
        } else {
          // Create new parameter
          const pliParameter = new PLIParameters({
            parentParameter: parameter.parentParameter,
            parentWeightage: parameter.parentWeightage,
            projectType: parameter.projectType || "Dedicated",
            applicableRoles: parameter.applicableRoles,
            childParameters: parameter.childParameters,
          });

          const savedParameter = await pliParameter.save();
          createdParameters.push(savedParameter);
        }
      }

      return createdParameters;
    }
  }

  /**
   * @desc This function is being used to get PLI parameters
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {function} locale Translation function
   * @return {Array} List of PLI parameters
   */
  static async getPLIParameters(req, locale) {
    // First check for documents with the new schema format
    const newSchemaParams = await PLIParameters.find({
      isActive: 1,
      roleParameters: { $exists: true }
    });

    if (newSchemaParams.length > 0) {
      console.log("Found documents with new schema format");

      // If role is specified, filter the roleParameters array
      if (req.query.role) {
        const role = req.query.role;
        console.log(`Filtering for role: ${role}`);

        // Create filtered copies of the documents
        const filteredResults = newSchemaParams.map(doc => {
          const docObj = doc.toObject();
          docObj.roleParameters = docObj.roleParameters.filter(
            rp => rp.applicableRole === role
          );
          return docObj;
        }).filter(doc => doc.roleParameters.length > 0);

        return filteredResults;
      }

      return newSchemaParams;
    } else {
      // Fall back to old schema format
      console.log("Falling back to old schema format");
      const query = { isActive: 1 };

      if (req.query.projectType) {
        query.projectType = req.query.projectType;
      }
      if (req.query.role) {
        query.applicableRoles = req.query.role;
      }

      return await PLIParameters.find(query);
    }
  }

  /**
   * @desc This function is being used to update PLI parameter
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {function} locale Translation function
   * @return {Object} Updated PLI parameter
   */
  static async updatePLIParameter(req, locale) {
    const Validator = new PLIParametersValidator(req.body, locale);
    await Validator.validateUpdatePLIParameter();

    const pliParameter = await PLIParameters.findById(req.params.id);
    if (!pliParameter) {
      throw new GeneralError(locale("PARAMETER_NOT_FOUND"), 404);
    }

    pliParameter.parentParameter = req.body.parentParameter;
    pliParameter.parentWeightage = req.body.parentWeightage;
    pliParameter.projectType = req.body.projectType;
    pliParameter.applicableRoles = req.body.applicableRoles;
    pliParameter.childParameters = req.body.childParameters;
    pliParameter.updatedAt = Date.now();

    return await pliParameter.save();
  }

  /**
   * @desc This function is being used to delete PLI parameter
   * <AUTHOR>
   * @since 30/04/2025
   * @param {Object} req Request
   * @param {function} locale Translation function
   * @return {Object} Deleted PLI parameter
   */
  static async deletePLIParameter(req, locale) {
    const pliParameter = await PLIParameters.findById(req.params.id);
    if (!pliParameter) {
      throw new GeneralError(locale("PARAMETER_NOT_FOUND"), 404);
    }

    pliParameter.isActive = 0;
    pliParameter.updatedAt = Date.now();

    return await pliParameter.save();
  }
}

module.exports = PLIParametersService;
