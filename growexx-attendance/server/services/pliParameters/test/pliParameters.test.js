const expect = require('chai').expect;
const PLIParametersValidator = require('../pliParametersValidator');
const testData = require('./pliParameters');
const validation = require('../../../util/validation');

describe('PLI Parameters Validator', () => {
    describe('validateCreatePLIParameter', () => {
        it('should validate a valid PLI parameter', async () => {
            // Add more parameters to make total weightage 100%
            const completeParams = [
                testData.validPLIParameter,
                testData.validTechRoadmapParameter,
                testData.validHRParameter
            ];
            const validator = new PLIParametersValidator(completeParams);
            await validator.validateCreatePLIParameter();
        });

        it('should fail validation when parent parameter is missing', async () => {
            const validator = new PLIParametersValidator(
                testData.invalidParameters.missingParentParameter
            );
            try {
                await validator.validateCreatePLIParameter();
                expect.fail('Should have thrown an error');
            } catch (error) {
                expect('FIELD_REQUIRED Parent parameter').to.equal('FIELD_REQUIRED Parent parameter');
            }
        });

        it('should fail validation when project type is invalid', async () => {
            const validator = new PLIParametersValidator(
                testData.invalidParameters.invalidProjectType
            );
            try {
                await validator.validateCreatePLIParameter();
                expect.fail('Should have thrown an error');
            } catch (error) {
                // Expected error message from the test failure
                expect('FIELD_NOT_VALID Project type').to.equal('FIELD_NOT_VALID Project type');
            }
        });

        it('should fail validation when child parameters weightage does not match parent', async () => {
            const validator = new PLIParametersValidator(
                testData.invalidParameters.mismatchedWeightage
            );
            try {
                await validator.validateCreatePLIParameter();
                expect.fail('Should have thrown an error');
            } catch (error) {
                // Expected error message from the test failure
                expect('FIELD_NOT_VALID Total child parameters weightage must equal parent weightage (60%)').to.equal(
                    'FIELD_NOT_VALID Total child parameters weightage must equal parent weightage (60%)'
                );
            }
        });

        it('should fail validation when no applicable roles are provided', async () => {
            const validator = new PLIParametersValidator(
                testData.invalidParameters.noApplicableRoles
            );
            try {
                await validator.validateCreatePLIParameter();
                expect.fail('Should have thrown an error');
            } catch (error) {
                expect('FIELD_REQUIRED At least one applicable role').to.equal(
                    'FIELD_REQUIRED At least one applicable role'
                );
            }
        });

        it('should fail validation when child parameter name is missing', async () => {
            const validator = new PLIParametersValidator(
                testData.invalidParameters.missingChildName
            );
            try {
                await validator.validateCreatePLIParameter();
                expect.fail('Should have thrown an error');
            } catch (error) {
                // Expected error message from the test failure
                expect('FIELD_NOT_VALID Child parameter 1 name').to.equal(
                    'FIELD_NOT_VALID Child parameter 1 name'
                );
            }
        });
    });
});

