module.exports = {
    validPLIParameter: {
        parentParameter: 'Project 1',
        parentWeightage: 60,
        projectType: 'Dedicated',
        applicableRoles: ['Developer', 'Senior Developer'],
        childParameters: [
            {
                name: 'Code Quality',
                weightage: 30,
                description: 'Code quality metrics'
            },
            {
                name: 'Delivery',
                weightage: 30,
                description: 'Delivery metrics'
            }
        ],
        isActive: 1
    },

    validZeroWeightageParameter: {
        parentParameter: 'Project Zero',
        parentWeightage: 0,
        projectType: 'Dedicated',
        applicableRoles: ['Developer'],
        childParameters: [
            {
                name: 'Zero Param',
                weightage: 0,
                description: 'Parameter with zero weightage'
            }
        ],
        isActive: 1
    },

    validTechRoadmapParameter: {
        parentParameter: 'Tech Roadmap',
        parentWeightage: 30,
        key: 'techRoadmap',
        applicableRoles: ['Developer'],
        childParameters: [
            {
                name: 'Learning',
                weightage: 15,
                description: 'Learning new technologies'
            },
            {
                name: 'Implementation',
                weightage: 15,
                description: 'Implementing new technologies'
            }
        ],
        isActive: 1
    },

    validHRParameter: {
        parentParameter: 'HR',
        parentWeightage: 10,
        key: 'hr',
        applicableRoles: ['Developer'],
        childParameters: [
            {
                name: 'Attendance',
                weightage: 5,
                description: 'Attendance metrics'
            },
            {
                name: 'Discipline',
                weightage: 5,
                description: 'Discipline metrics'
            }
        ],
        isActive: 1
    },

    invalidParameters: {
        missingParentParameter: {
            parentWeightage: 60,
            projectType: 'Dedicated',
            applicableRoles: ['Developer'],
            childParameters: [{ name: 'Test', weightage: 60 }]
        },

        invalidProjectType: {
            parentParameter: 'Project 1',
            parentWeightage: 60,
            projectType: 'Invalid',
            applicableRoles: ['Developer'],
            childParameters: [{ name: 'Test', weightage: 60 }]
        },

        mismatchedWeightage: {
            parentParameter: 'Project 1',
            parentWeightage: 60,
            projectType: 'Dedicated',
            applicableRoles: ['Developer'],
            childParameters: [
                { name: 'Test 1', weightage: 20 },
                { name: 'Test 2', weightage: 20 }
            ]
        },

        noApplicableRoles: {
            parentParameter: 'Project 1',
            parentWeightage: 60,
            projectType: 'Dedicated',
            applicableRoles: [],
            childParameters: [{ name: 'Test', weightage: 60 }]
        },

        missingChildName: {
            parentParameter: 'Project 1',
            parentWeightage: 60,
            projectType: 'Dedicated',
            applicableRoles: ['Developer'],
            childParameters: [{ weightage: 60 }]
        }
    }
};

