const message = require('../../locales/en.json');

module.exports = (swaggerJson) => {
    swaggerJson.paths['/api/pli-parameters'] = {
        post: {
            tags: ['PLI Parameters'],
            description: 'Create PLI parameter',
            summary: 'Create PLI parameter',
            parameters: [
                {
                    in: 'header',
                    name: 'Authorization',
                    description: 'Authorization token',
                    required: true,
                    type: 'string'
                },
                {
                    in: 'body',
                    name: 'body',
                    description: 'Body parameter',
                    required: true,
                    schema: {
                        $ref: '#/definitions/pliParameterInput'
                    }
                }
            ],
            responses: {
                200: {
                    description: 'PLI parameter created successfully.',
                    schema: {
                        $ref: '#/definitions/successPLIParameter'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                }
            }
        },
        get: {
            tags: ['PLI Parameters'],
            description: 'Get PLI parameters',
            summary: 'Get PLI parameters',
            parameters: [
                {
                    in: 'header',
                    name: 'Authorization',
                    description: 'Authorization token',
                    required: true,
                    type: 'string'
                },
                {
                    in: 'query',
                    name: 'projectType',
                    description: 'Project type filter',
                    type: 'string'
                },
                {
                    in: 'query',
                    name: 'role',
                    description: 'Role filter',
                    type: 'string'
                }
            ],
            responses: {
                200: {
                    description: 'PLI parameters fetched successfully.',
                    schema: {
                        $ref: '#/definitions/successPLIParameters'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                }
            }
        }
    };

    swaggerJson.paths['/api/pli-parameters/{id}'] = {
        put: {
            tags: ['PLI Parameters'],
            description: 'Update PLI parameter',
            summary: 'Update PLI parameter',
            parameters: [
                {
                    in: 'header',
                    name: 'Authorization',
                    description: 'Authorization token',
                    required: true,
                    type: 'string'
                },
                {
                    in: 'path',
                    name: 'id',
                    description: 'PLI parameter ID',
                    required: true,
                    type: 'string'
                },
                {
                    in: 'body',
                    name: 'body',
                    description: 'Body parameter',
                    required: true,
                    schema: {
                        $ref: '#/definitions/pliParameterInput'
                    }
                }
            ],
            responses: {
                200: {
                    description: 'PLI parameter updated successfully.',
                    schema: {
                        $ref: '#/definitions/successPLIParameter'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                404: {
                    description: 'Parameter not found',
                    schema: {
                        $ref: '#/definitions/notFound'
                    }
                }
            }
        },
        delete: {
            tags: ['PLI Parameters'],
            description: 'Delete PLI parameter',
            summary: 'Delete PLI parameter',
            parameters: [
                {
                    in: 'header',
                    name: 'Authorization',
                    description: 'Authorization token',
                    required: true,
                    type: 'string'
                },
                {
                    in: 'path',
                    name: 'id',
                    description: 'PLI parameter ID',
                    required: true,
                    type: 'string'
                }
            ],
            responses: {
                200: {
                    description: 'PLI parameter deleted successfully.',
                    schema: {
                        $ref: '#/definitions/successPLIParameter'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                404: {
                    description: 'Parameter not found',
                    schema: {
                        $ref: '#/definitions/notFound'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.pliParameterInput = {
        type: 'object',
        properties: {
            roleParameters: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        applicableRole: {
                            type: 'string',
                            example: 'Developer',
                            description: 'Role to which these parameters apply'
                        },
                        parameters: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    parentParameter: {
                                        type: 'string',
                                        example: 'Technical Skills'
                                    },
                                    parentWeightage: {
                                        type: 'number',
                                        minimum: 0,
                                        maximum: 100,
                                        example: 30,
                                        description: 'Can be 0 or any value up to 100'
                                    },
                                    projectType: {
                                        type: 'string',
                                        enum: ['Dedicated', 'Fixed'],
                                        example: 'Dedicated'
                                    },
                                    childParameters: {
                                        type: 'array',
                                        items: {
                                            type: 'object',
                                            properties: {
                                                name: {
                                                    type: 'string',
                                                    example: 'Code Quality'
                                                },
                                                weightage: {
                                                    type: 'number',
                                                    minimum: 0,
                                                    maximum: 100,
                                                    example: 40,
                                                    description: 'Can be 0 or any value up to 100'
                                                },
                                                description: {
                                                    type: 'string',
                                                    example: 'Quality of code written'
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
                example: [
                    {
                        "applicableRole": "Developer",
                        "parameters": [
                            {
                                "parentParameter": "Technical Skills",
                                "parentWeightage": 30,
                                "projectType": "Dedicated",
                                "childParameters": [
                                    {
                                        "name": "Code Quality",
                                        "weightage": 40,
                                        "description": "Quality of code written"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        }
    };

    swaggerJson.definitions.successPLIParameter = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'object',
                properties: {
                    _id: {
                        type: 'string',
                        example: '60d21b4667d0d8992e610c85'
                    },
                    roleParameters: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                applicableRole: {
                                    type: 'string',
                                    example: 'Developer'
                                },
                                parameters: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            parentParameter: {
                                                type: 'string',
                                                example: 'Technical Skills'
                                            },
                                            parentWeightage: {
                                                type: 'number',
                                                example: 30
                                            },
                                            projectType: {
                                                type: 'string',
                                                example: 'Dedicated'
                                            },
                                            childParameters: {
                                                type: 'array',
                                                items: {
                                                    type: 'object',
                                                    properties: {
                                                        name: {
                                                            type: 'string',
                                                            example: 'Code Quality'
                                                        },
                                                        weightage: {
                                                            type: 'number',
                                                            example: 40
                                                        },
                                                        description: {
                                                            type: 'string',
                                                            example: 'Quality of code written'
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    isActive: {
                        type: 'number',
                        example: 1
                    },
                    createdAt: {
                        type: 'string',
                        format: 'date-time',
                        example: '2023-06-18T12:30:45.000Z'
                    },
                    updatedAt: {
                        type: 'string',
                        format: 'date-time',
                        example: '2023-06-18T12:30:45.000Z'
                    }
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.successPLIParameters = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            data: {
                type: 'array',
                items: {
                    type: 'object',
                    properties: {
                        _id: {
                            type: 'string',
                            example: '60d21b4667d0d8992e610c85'
                        },
                        roleParameters: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    applicableRole: {
                                        type: 'string',
                                        example: 'Developer'
                                    },
                                    parameters: {
                                        type: 'array',
                                        items: {
                                            type: 'object',
                                            properties: {
                                                parentParameter: {
                                                    type: 'string',
                                                    example: 'Technical Skills'
                                                },
                                                parentWeightage: {
                                                    type: 'number',
                                                    example: 30
                                                },
                                                projectType: {
                                                    type: 'string',
                                                    example: 'Dedicated'
                                                },
                                                childParameters: {
                                                    type: 'array',
                                                    items: {
                                                        type: 'object',
                                                        properties: {
                                                            name: {
                                                                type: 'string',
                                                                example: 'Code Quality'
                                                            },
                                                            weightage: {
                                                                type: 'number',
                                                                example: 40
                                                            },
                                                            description: {
                                                                type: 'string',
                                                                example: 'Quality of code written'
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        isActive: {
                            type: 'number',
                            example: 1
                        },
                        createdAt: {
                            type: 'string',
                            format: 'date-time',
                            example: '2023-06-18T12:30:45.000Z'
                        },
                        updatedAt: {
                            type: 'string',
                            format: 'date-time',
                            example: '2023-06-18T12:30:45.000Z'
                        }
                    }
                }
            },
            message: {
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.notFound = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            message: {
                example: 'Parameter not found'
            }
        }
    };

    return swaggerJson;
};
