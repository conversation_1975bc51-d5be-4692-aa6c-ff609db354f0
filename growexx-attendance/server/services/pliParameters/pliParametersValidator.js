const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
* Class represents validations for new PLI Parameters schema.
*/
class PLIParametersValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
   * @desc This function is being used to validate create PLI parameter
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateCreatePLIParameter () {
        console.log('Validating PLI parameter creation request');

        // Check if we have the new schema format with roleParameters
        if (this.body && this.body.roleParameters && Array.isArray(this.body.roleParameters)) {
            console.log('Validating new schema format with roleParameters');
            await this.validateRoleParameters();
        } else {
            console.log('Validating old schema format');
            // For backward compatibility, allow old schema format
            // This is a temporary solution until all clients are updated
            return true;
        }
    }

    /**
   * @desc This function is being used to validate role parameters
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateRoleParameters () {
        console.log('Validating role parameters');

        if (!this.body.roleParameters || !Array.isArray(this.body.roleParameters)) {
            console.error('Role parameters is not an array');
            throw new GeneralError(this.__(this.NOT_VALID, 'Role parameters'), 400);
        }

        if (this.body.roleParameters.length === 0) {
            console.error('Role parameters array is empty');
            throw new GeneralError(
                this.__(this.REQUIRED, 'At least one role parameter'),
                400
            );
        }

        console.log(`Found ${this.body.roleParameters.length} role parameters`);

        for (const roleParam of this.body.roleParameters) {
            try {
                await this.validateRoleParameter(roleParam);
            } catch (error) {
                console.error('Error validating role parameter:', error);
                throw error;
            }
        }
    }

    /**
   * @desc This function is being used to validate a single role parameter
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateRoleParameter (roleParam) {
        console.log('Validating role parameter:', JSON.stringify(roleParam, null, 2));

        if (!roleParam.applicableRole) {
            console.error('Applicable role is missing');
            throw new GeneralError(this.__(this.REQUIRED, 'Applicable role'), 400);
        }

        if (typeof roleParam.applicableRole !== 'string') {
            console.error('Applicable role is not a string');
            throw new GeneralError(this.__(this.NOT_VALID, 'Applicable role'), 400);
        }

        console.log(`Validating parameters for role: ${roleParam.applicableRole}`);

        if (!roleParam.parameters || !Array.isArray(roleParam.parameters)) {
            console.error('Parameters is not an array');
            throw new GeneralError(this.__(this.NOT_VALID, 'Parameters'), 400);
        }

        if (roleParam.parameters.length === 0) {
            console.error('Parameters array is empty');
            throw new GeneralError(
                this.__(this.REQUIRED, 'At least one parameter'),
                400
            );
        }

        console.log(`Found ${roleParam.parameters.length} parameters`);

        for (const parameter of roleParam.parameters) {
            try {
                await this.validateParameter(parameter);
            } catch (error) {
                console.error('Error validating parameter:', error);
                throw error;
            }
        }

        try {
            await this.validateTotalWeightage(roleParam.parameters);
        } catch (error) {
            console.error('Error validating total weightage:', error);
            throw error;
        }
    }

    /**
   * @desc This function is being used to validate a single parameter
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateParameter (parameter) {
        console.log('Validating parameter:', JSON.stringify(parameter, null, 2));

        try {
            await this.validateParentParameter(parameter);
            await this.validateParentWeightage(parameter);
            await this.validateProjectType(parameter);
            await this.validateChildParameters(parameter);
        } catch (error) {
            console.error('Error in parameter validation:', error);
            throw error;
        }
    }

    /**
   * @desc This function is being used to validate parent parameter
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateParentParameter (parameter) {
        if (!parameter.parentParameter) {
            throw new GeneralError(this.__(this.REQUIRED, 'Parent parameter'), 400);
        }
        if (typeof parameter.parentParameter !== 'string') {
            throw new GeneralError(this.__(this.NOT_VALID, 'Parent parameter'), 400);
        }
    }

    /**
   * @desc This function is being used to validate parent weightage
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateParentWeightage (parameter) {
        if (!parameter.parentWeightage && parameter.parentWeightage !== 0) {
            throw new GeneralError(this.__(this.REQUIRED, 'Parent weightage'), 400);
        }
        if (
            typeof parameter.parentWeightage !== 'number' ||
      parameter.parentWeightage < 0
        ) {
            throw new GeneralError(this.__(this.NOT_VALID, 'Parent weightage'), 400);
        }
    }

    /**
   * @desc This function is being used to validate project type
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateProjectType (parameter) {
        console.log('Validating project type');

        // Skip validation for techRoadmap and hr
        if (parameter.key === 'techRoadmap' || parameter.key === 'hr' ||
        parameter.parentParameter === 'Tech Roadmap' || parameter.parentParameter === 'HR') {
            console.log('Skipping project type validation for special parameter');
            return;
        }

        if (!parameter.projectType) {
            console.error('Project type is missing');
            throw new GeneralError(this.__(this.REQUIRED, 'Project type'), 400);
        }

        if (!['Dedicated', 'Fixed'].includes(parameter.projectType)) {
            console.error(`Invalid project type: ${parameter.projectType}`);
            throw new GeneralError(this.__(this.NOT_VALID, 'Project type'), 400);
        }

        console.log(`Project type is valid: ${parameter.projectType}`);
    }

    /**
   * @desc This function is being used to validate child parameters
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateChildParameters (parameter) {
        if (
            !parameter.childParameters ||
      !Array.isArray(parameter.childParameters)
        ) {
            throw new GeneralError(this.__(this.NOT_VALID, 'Child parameters'), 400);
        }

        // Allow empty child parameters when parent weightage is 0
        if (parameter.parentWeightage === 0) {
            console.log('Parent weightage is 0, allowing empty child parameters');
            return;
        }

        // Only require child parameters when parent weightage is greater than 0
        if (!parameter.childParameters.length) {
            throw new GeneralError(
                this.__(this.REQUIRED, 'At least one child parameter'),
                400
            );
        }

        parameter.childParameters.forEach((child, index) => {
            if (!child.name || typeof child.name !== 'string') {
                throw new GeneralError(
                    this.__(this.NOT_VALID, `Child parameter ${index + 1} name`),
                    400
                );
            }
            if (
                !child.weightage && child.weightage !== 0 ||
        typeof child.weightage !== 'number' ||
        child.weightage < 0
            ) {
                throw new GeneralError(
                    this.__(this.NOT_VALID, `Child parameter ${index + 1} weightage`),
                    400
                );
            }
        });

        // Skip validation of child parameters weightage if parent weightage is 0
        if (parameter.parentWeightage === 0) {
            return;
        }

        const totalChildWeightage = parameter.childParameters.reduce(
            (sum, child) => sum + child.weightage,
            0
        );
        if (Math.abs(totalChildWeightage - parameter.parentWeightage) > 0.01) {
            throw new GeneralError(
                this.__(
                    this.NOT_VALID,
                    `Total child parameters weightage must equal parent weightage (${parameter.parentWeightage}%)`
                ),
                400
            );
        }
    }

    /**
   * @desc This function is being used to validate total weightage
   * <AUTHOR>
   * @since 30/04/2025
   */
    async validateTotalWeightage (parameters) {
        console.log('Validating total weightage');

        const totalParentWeightage = parameters.reduce(
            (sum, parameter) => sum + parameter.parentWeightage,
            0
        );

        console.log(`Total parent weightage: ${totalParentWeightage}%`);

        // Allow a small margin of error (0.5%)
        if (Math.abs(totalParentWeightage - 100) > 0.5) {
            console.error(`Total weightage (${totalParentWeightage}%) is not close to 100%`);

            // For now, just log a warning instead of throwing an error
            console.warn(
                `WARNING: Total parent parameters weightage should be 100%, but is ${totalParentWeightage}%`
            );

            // Uncomment to enforce strict validation
            /*
      throw new GeneralError(
        this.__(
          this.NOT_VALID,
          `Total parent parameters weightage must be 100%, but is ${totalParentWeightage}%`
        ),
        400
      );
      */
        } else {
            console.log('Total weightage is valid (close to 100%)');
        }
    }
}

module.exports = PLIParametersValidator;

