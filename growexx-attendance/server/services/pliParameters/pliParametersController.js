const PLIParametersService = require('./pliParametersService');
const Utils = require('../../util/utilFunctions');

/**
* Class represents controller for new PLI Parameters
*/
class PLIParametersController {
    /**
   * @desc This function is being used to create PLI parameter
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */
    static async createPLIParameter (req, res) {
        try {
            console.log('Received createPLIParameter request with body:', JSON.stringify(req.body, null, 2));
            const data = await PLIParametersService.createPLIParameter(req, res.__);
            console.log('Successfully created PLI parameters:', JSON.stringify(data, null, 2));
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            console.error('Error in createPLIParameter:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to get PLI parameters
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {Object} req.query RequestQuery
   * @param {function} res Response
   */
    static async getPLIParameters (req, res) {
        try {
            console.log('Received getPLIParameters request with query:', JSON.stringify(req.query, null, 2));
            const data = await PLIParametersService.getPLIParameters(req, res.__);
            console.log('Found PLI parameters count:', data.length);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            console.error('Error in getPLIParameters:', error);
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to update PLI parameter
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {Object} req.body RequestBody
   * @param {function} res Response
   */
    static async updatePLIParameter (req, res) {
        try {
            const data = await PLIParametersService.updatePLIParameter(req, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
   * @desc This function is being used to delete PLI parameter
   * <AUTHOR>
   * @since 02/05/2024
   * @param {Object} req Request
   * @param {Object} req.params RequestParams
   * @param {function} res Response
   */
    static async deletePLIParameter (req, res) {
        try {
            const data = await PLIParametersService.deletePLIParameter(req, res.__);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = PLIParametersController;
