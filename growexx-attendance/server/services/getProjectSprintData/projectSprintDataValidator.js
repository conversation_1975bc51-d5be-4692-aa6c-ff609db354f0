const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for Project Sprint Data
 */
class ProjectSprintDataValidator extends validation {
    constructor(query, locale) {
        super(locale);
        this.query = query;
    }

    /**
     * @desc This function is being used to validate the project sprint data request
     * <AUTHOR>
     * @since 08/21/2024
     * @param {Object} query The query parameters
     */
    validateProjectSprintDataParams(query) {
        const { project, month, year, employeeName, projectType } = query;

        if (!project) {
            throw new GeneralError(this.__(this.REQUIRED, 'Project name'), 400);
        }

        if (!month) {
            throw new GeneralError(this.__(this.REQUIRED, 'Month'), 400);
        }

        if (!year) {
            throw new GeneralError(this.__(this.REQUIRED, 'Year'), 400);
        }

        if (!employeeName) {
            throw new GeneralError(this.__(this.REQUIRED, 'Employee name'), 400);
        }

        if (!projectType) {
            throw new GeneralError(this.__(this.REQUIRED, 'Project type'), 400);
        }

        // Validate month is between 1-12
        const monthNum = parseInt(month, 10);
        if (isNaN(monthNum) || monthNum < 1 || monthNum > 12) {
            throw new GeneralError(this.__('INVALID_MONTH'), 400);
        }

        // Validate year format
        const yearNum = parseInt(year, 10);
        if (isNaN(yearNum) || yearNum < 2000 || yearNum > 2100) {
            throw new GeneralError(this.__('INVALID_YEAR'), 400);
        }

        // Validate projectType
        if (!['Dedicated', 'Fixed'].includes(projectType)) {
            throw new GeneralError(this.__('INVALID_PROJECT_TYPE'), 400);
        }
    }
}

module.exports = ProjectSprintDataValidator;