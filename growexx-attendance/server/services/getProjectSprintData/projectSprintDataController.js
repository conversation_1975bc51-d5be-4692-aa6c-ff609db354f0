const Utils = require('../../util/utilFunctions');
const ProjectSprintDataService = require('./projectSprintDataService');
const ProjectSprintDataValidator = require('./projectSprintDataValidator');

/**
 * Class represents controllers for Project Sprint Data
 */
class ProjectSprintDataController {
    /**
     * @desc Controller to get project sprint data
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getProjectSprintData(req, res) {
        try {
            // Validate request parameters
            const validator = new ProjectSprintDataValidator(req.query, res.__);
            validator.validateProjectSprintDataParams(req.query);
            
            // Call service
            const data = await ProjectSprintDataService.getProjectSprintData(req);
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, '');
        }
    }
}

module.exports = ProjectSprintDataController; 