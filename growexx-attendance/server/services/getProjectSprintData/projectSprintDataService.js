const RagModel = require('../../models/rag.model');
const SprintMetricsModel = require('../../models/sprintMetrics.model');
const PLIParametersModel = require('../../models/pliParameters.model');
const PLIRatingModel = require('../../models/pliRating.model');
const RagService = require('../getRag/ragService');
const UserModel = require('../../models/user.model');

/**
 * Class represents services for Project Sprint Data
 */
class ProjectSprintDataService {
    /**
   * @desc This function maps specific designations to general role categories
   * <AUTHOR>
   * @since 05/04/2025
   * @param {String} designation The specific designation from constants
   * @return {String} The general role category for PLI parameters
   */
    static mapDesignationToRole (designation) {
    // Default role if no mapping is found
        let role = 'Developers';

        if (!designation) {
            return role;
        }

        // Convert designation to lowercase for case-insensitive matching
        const lowerDesignation = designation.toLowerCase();

        // Developer roles
        if (
            lowerDesignation.includes('software engineer') ||
      lowerDesignation.includes('data engineer') ||
      lowerDesignation.includes('data scientist') ||
      lowerDesignation.includes('database administrator') ||
      lowerDesignation.includes('web analytics engineer') ||
      lowerDesignation.includes('data analyst')
        ) {
            role = 'Developers';
        }
        // QA roles
        else if (
            lowerDesignation.includes('qa') ||
      lowerDesignation.includes('quality assurance')
        ) {
            role = 'QA';
        }
        // Technical Lead roles
        else if (
            lowerDesignation.includes('technical lead') ||
      lowerDesignation.includes('lead engineer') ||
      lowerDesignation.includes('lead - data') ||
      lowerDesignation.includes('sr. technical lead')
        ) {
            role = 'TL';
        }
        // Technical Project Manager roles
        else if (
            lowerDesignation.includes('technical project manager') ||
      lowerDesignation.includes('scrum master')
        ) {
            role = 'TPM';
        }
        // Project Manager roles
        else if (
            lowerDesignation.includes('project manager') ||
      lowerDesignation.includes('product manager') ||
      lowerDesignation.includes('product owner') ||
      lowerDesignation.includes('service delivery manager')
        ) {
            role = 'PM';
        }
        // HR roles
        else if (
            lowerDesignation.includes('hr') ||
      lowerDesignation.includes('talent acquisition') ||
      lowerDesignation.includes('human resource')
        ) {
            role = 'HR';
        }
        // Business Unit Head / Manager roles - map to PM for PLI purposes
        else if (
            lowerDesignation.includes('manager') ||
      lowerDesignation.includes('head') ||
      lowerDesignation.includes('director') ||
      lowerDesignation.includes('co-founder')
        ) {
            role = 'PM';
        }
        // Business Analyst roles - map to Developer for PLI purposes
        else if (
            lowerDesignation.includes('business analyst') ||
      lowerDesignation.includes('analyst')
        ) {
            role = 'Developers';
        }

        return role;
    }

    /**
   * @desc This function fetches project sprint data based on the provided parameters
   * <AUTHOR>
   * @since 08/21/2024
   * @param {Object} req Request object
   */
    static async getProjectSprintData (req) {
        const { project, month, year, employeeName, projectType, weightage } = req.query;

        if (!employeeName) {
            return {
                success: false,
                error: 'Employee name is required'
            };
        }

        try {

            // Employee name may have a dot (like hiten.khuman) which needs to be preserved
            const baseUsername = employeeName;

            // First, try to find the user by label to get their ID
            let menteeId = null;
            try {
                const user = await UserModel.findOne({ label: { $in: [baseUsername] } }).lean();
                if (user && user._id) {
                    menteeId = user._id;
                }
            } catch (error) {
                // Error handled silently, will continue with the old implementation
                console.log('Error finding user:', error.message);
            }

            // Check if we have existing PLI Rating data for this mentee, month, and year
            let existingPLIRating = null;
            let existingProjectRating = null;

            if (menteeId) {
                console.log(`[PLI Rating Check] Checking PLI Rating for menteeId: ${menteeId}, month: ${month}, year: ${year}`);
                try {
                    // First, find the project ID for the project name
                    const ProjectModel = require('../../models/project.model');
                    let projectId = null;

                    try {
                        const projectDoc = await ProjectModel.findOne({
                            projectName: project,
                            isActive: 1,
                            isDelete: 0
                        }).lean();

                        if (projectDoc && projectDoc._id) {
                            projectId = projectDoc._id;
                        }
                    } catch (error) {
                        console.log('Error finding project:', error.message);
                        // Continue with the search even if we can't find the project
                    }

                    // Find PLI Rating for this mentee, month, and year
                    existingPLIRating = await PLIRatingModel.findOne({
                        menteeId,
                        month: parseInt(month),
                        year: parseInt(year)
                    }).lean();
                    console.log(`[PLI Rating Query] menteeId: ${menteeId}, month: ${parseInt(month)}, year: ${parseInt(year)}`);
                    console.log(existingPLIRating);
                    // If PLI Rating exists, find the project rating for this project

                    if (existingPLIRating && existingPLIRating.projectRatings && existingPLIRating.projectRatings.length > 0) {
                        // Find the project by ID
                        if (projectId) {
                            console.log(`[PLI Rating Check] Looking for project with ID: ${projectId}`);
                            existingProjectRating = existingPLIRating.projectRatings.find(pr =>
                                pr.projectId && pr.projectId.toString() === projectId.toString()
                            );

                            if (existingProjectRating) {
                                console.log('[PLI Rating Check] Found matching project rating in PLI Rating model');
                            }
                        }

                        // If we couldn't find by ID (maybe because project ID changed), try a fallback
                        // This is less reliable but might help in some cases
                        if (!existingProjectRating && existingPLIRating.projectRatings.length === 1) {
                            // If there's only one project rating, use it as a fallback
                            existingProjectRating = existingPLIRating.projectRatings[0];
                            console.log('Using fallback project rating match');
                        }
                    }
                } catch (error) {
                    console.log('Error finding existing PLI Rating:', error.message);
                    // Continue with the old implementation if there's an error
                }
            }

            // Get RAG data for the project in the specified month
            const sprints = await RagModel.find({
                project,
                sprintEnd: {
                    $gte: new Date(parseInt(year), parseInt(month) - 1, 1), // First day of the month
                    $lte: new Date(parseInt(year), parseInt(month), 0, 23, 59, 59, 999) // Last day of the month (inclusive)
                },
                'team.member': baseUsername // Only include sprints where this employee is a team member
            }).lean();



            // If no sprints found for the month
            if (!sprints || sprints.length === 0) {
                return {
                    success: true,
                    data: []
                };
            }

            // Get user's role based on designation
            let role = 'Developers';
            // Use the existing menteeId if available, otherwise set it to null
            if (!menteeId) {
                menteeId = null;
            }
            let mentorId = null;

            try {
                const user = await UserModel.findOne({ label: { $in: [baseUsername] } }).lean();
                if (user) {
                    if (user.designation) {
                        role = this.mapDesignationToRole(user.designation);
                    }
                    menteeId = user._id;

                    // Get mentorId from the user model
                    if (user.mentorId) {
                        mentorId = user.mentorId;
                    }
                }
            } catch (error) {
                // Error handled silently
                console.log('Error finding user details:', error.message);
            }



            // Get project ID for the project name
            let projectId = null;
            try {
                const ProjectModel = require('../../models/project.model');
                const projectDoc = await ProjectModel.findOne({
                    projectName: project,
                    isActive: 1,
                    isDelete: 0
                }).lean();

                if (projectDoc && projectDoc._id) {
                    projectId = projectDoc._id;
                }
            } catch (error) {
                console.log('Error finding project ID:', error.message);
            }

            // Get the single PLI parameters document
            const pliParametersDoc = await PLIParametersModel.findOne({ isActive: 1 }).lean();

            // Find the role-specific parameters for this user's role
            let roleParameters = null;
            if (pliParametersDoc && pliParametersDoc.roleParameters) {
                roleParameters = pliParametersDoc.roleParameters.find(rp =>
                    rp.applicableRole === role
                );
            }

            // Find the parameter set for this project type
            let pliParameter = null;
            if (roleParameters && roleParameters.parameters) {
                pliParameter = roleParameters.parameters.find(p =>
                    p.projectType === (projectType || 'Dedicated')
                );
            }

            // Fetch sprint metrics data for this user where repo is 'Overall' (with capital O)
            const sprintMetrics = await SprintMetricsModel.find({
                projectName: project,
                resource: `${baseUsername.split('.')[0]}-growexxer`,
                repo: 'Overall'
            }).lean();

            // Map sprint metrics by sprint number
            const sprintMetricsMap = {};
            sprintMetrics.forEach(metric => {
                if (metric.sprintNumber) {
                    sprintMetricsMap[metric.sprintNumber] = metric;
                }
            });

            const result = {
                data: []
            };

            // Process PLI parameters
            if (pliParameter && pliParameter.childParameters) {
                // Use child parameters from the parent parameter
                pliParameter.childParameters.forEach(param => {
                    const pliParam = {
                        pliParameter: {
                            name: param.name,
                            sprintwiseScores: [],
                            weightage: param.weightage || 0
                        }
                    };

                    // Check if we have existing PLI Rating data for this parameter
                    let existingParameterScores = null;
                    if (existingProjectRating && existingProjectRating.parameterScores) {
                        // Find parameter scores by name
                        existingParameterScores = existingProjectRating.parameterScores.find(ps => {
                            // Match by parameter name instead of ID
                            // In PLI Rating model, childScores uses childParameterId which matches param.name
                            // So we need to check if any childScore has a childParameterId matching this param.name
                            return ps.childScores && ps.childScores.some(cs => cs.childParameterId === param.name);
                        });
                    }

                    // Process each sprint to get scores for this parameter
                    sprints.forEach(sprint => {
                        // First check if we have existing child scores for this sprint
                        let existingSprintScore = null;
                        if (existingParameterScores && existingParameterScores.childScores) {
                            // Find the child parameter that matches this parameter
                            const childScore = existingParameterScores.childScores.find(cs =>
                                // Compare the child parameter ID with the parameter name
                            // In PLI Rating model, childParameterId is stored as a String
                                cs.childParameterId === param.name
                            );

                            if (childScore && childScore.sprintScores) {
                                // Find the sprint score for this sprint number
                                existingSprintScore = childScore.sprintScores.find(ss =>
                                    ss.sprintNumber === sprint.sprintNumber
                                );
                            }
                        }

                        // If we have an existing sprint score, use it
                        if (existingSprintScore && existingSprintScore.score !== undefined) {
                            pliParam.pliParameter.sprintwiseScores.push({
                                sprintname: sprint.sprintNumber,
                                score: existingSprintScore.score,
                                comment: existingSprintScore.comment || '',
                                fromPLIRating: true // Mark that this came from PLI Rating
                            });
                            return; // Skip the rest of this iteration
                        }

                        // If no existing score, calculate it using the original method
                        // Find the team member data for this employee
                        const memberData =
              (sprint.team && sprint.team.find((m) => m.member === baseUsername)) ||
              {};

                        // Get the score based on parameter name
                        let score = 0;

                        // Declare variables outside switch to avoid lexical declaration issues
                        let ragColor;
                        let sprintAvgRating;
                        let sonarRating;

                        switch (param.name) {
                            // RAG parameters
                            case 'rag':
                            case 'RAG':
                            case 'RAG (Budget + Open/Close + B2D + Escalations)':
                                ragColor = RagService.getOverallRag({
                                    ...sprint,
                                    memberData,
                                    memberClientEscalation: memberData.escalationCount || 0
                                });
                                score = this.ragColorToScore(ragColor);
                                break;

                                // Sprint Average Rating parameters
                            case 'sprintAverageRating':
                            case 'Sprint Average RATING (PR) + (SONAR)':
                                const sprintMetricsData = sprintMetricsMap[sprint.sprintNumber] || {};
                                sprintAvgRating = sprintMetricsData.sprintAverage || 0;
                                sonarRating = sprintMetricsData.sonarRating || 0;
                                score = Number(sprintAvgRating) + Number(sonarRating);
                                break;

                                // B2D Score parameter
                            case 'b2dScore':
                            case 'B2D (NONE then best)':
                            case 'B2D Reported (more the better)':
                                score = memberData.b2dCount || 0;
                                break;

                                // Tech Audit Rating parameter
                            case 'techAuditRating':
                            case 'Tech Audit/PR Audit':
                            case 'Tech Audit':
                                score = sprint.techAudit ? parseFloat(sprint.techAudit) || 0 : 0;
                                break;

                                // Effort Variance Rating parameter
                            case 'effortVarianceRating':
                            case 'Velocity / Effort Variance':
                                // Get effort variance directly from team.effortVariance for this member
                                score = memberData.effortVariance || 0;
                                break;

                                // Customer Feedback / Escalation parameters
                            case 'Customer Feedback / Escalation - (Individual)':
                            case 'Customer Feedback / Escalation':
                                score = memberData.escalationCount ? -memberData.escalationCount : 0; // Negative score for escalations
                                break;

                                // Unplanned Leave parameters
                            case 'Least Leave (Un-Planned) - (Individual) - Billing Loss':
                            case 'Least Leave (Un-Planned)':
                                score = memberData.unplannedLeaves ? -memberData.unplannedLeaves : 0; // Negative score for unplanned leaves
                                break;

                                // Self-managing project parameter
                            case 'Self managing the project, proactiveness - 1/0 ( Project Specific) - Individual':
                                score = memberData.selfManaging || 0;
                                break;

                                // B2D from Customer parameter
                            case 'B2D from Customer (NONE then best)':
                                score = memberData.customerB2D || 0;
                                break;

                                // B2D Team parameter
                            case 'B2D (Team wide less the better)':
                                score = sprint.teamB2D ? -sprint.teamB2D : 0; // Negative score for team B2Ds
                                break;

                                // Billing Sheet parameter
                            case 'Accurate timely Billing Sheet':
                                score = memberData.billingAccuracy || 0;
                                break;

                                // Process Audit parameter
                            case 'Process Audit':
                                score = sprint.processAudit ? parseFloat(sprint.processAudit) || 0 : 0;
                                break;

                            default:
                                // For any other parameters, try to get the value from sprint or memberData
                                score = sprint[param.name] || memberData[param.name] || 0;
                        }

                        // Add the score to sprintwiseScores
                        pliParam.pliParameter.sprintwiseScores.push({
                            sprintname: sprint.sprintNumber,
                            score: score
                        });
                    });

                    // Add this parameter to the result
                    result.data.push(pliParam);
                });
            } else {
                result.data = [];
            }

            // Add necessary fields directly to the result
            // Add mentee and mentor IDs
            result.menteeId = menteeId;
            result.mentorId = mentorId;
            result.month = parseInt(month);
            result.year = parseInt(year);

            // Add project ID and project weightage
            if (projectId) {
                result.projectId = projectId;
                // Use weightage from request query if available, otherwise default to 100%
                result.projectWeightage = weightage ? parseInt(weightage) : 100;
            }

            // Add parameter ID if available - use the ID of the single large PLI parameters document
            if (pliParametersDoc && pliParametersDoc._id) {
                result.parameterId = pliParametersDoc._id;
            }

            // Add empty comments field that can be filled by frontend
            result.comments = '';

            // Enhance the data array to include necessary fields for child parameters
            if (result.data && result.data.length > 0) {
                result.data.forEach(item => {
                    // Add childParameterId directly to each parameter
                    item.childParameterId = item.pliParameter.name;

                    // Calculate weightage and calculation fields
                    const scores = item.pliParameter.sprintwiseScores
                        .map(sprint => sprint.score)
                        .filter(score => score !== undefined && score !== null);

                    let averageScore = 0;
                    if (scores.length > 0) {
                        const totalScore = scores.reduce(
                            (sum, score) => sum + Number(score),
                            0
                        );
                        averageScore = totalScore / scores.length;
                    }

                    const calculatedAverage = Number(averageScore.toFixed(2));
                    const weightage = item.pliParameter.weightage || 0;
                    const weightageAverage = (calculatedAverage * weightage / 100).toFixed(2);

                    // Add calculation and weightageAverage to the parameter
                    item.pliParameter.calculation = calculatedAverage;
                    item.pliParameter.weightageAverage = weightageAverage;

                    // Convert sprintwiseScores to the format needed for POST API
                    if (item.pliParameter.sprintwiseScores && item.pliParameter.sprintwiseScores.length > 0) {
                        item.sprintScores = item.pliParameter.sprintwiseScores.map(sprintScore => ({
                            sprintNumber: sprintScore.sprintname,
                            score: sprintScore.score,
                            comment: ''
                        }));
                    }
                });
            }
            console.log('result ', result);

            return {
                success: true,
                ...result
            };
        } catch (error) {
            console.error('Error in getProjectSprintData:', error);
            return {
                success: false,
                error:
          error.message ||
          'An error occurred while fetching project sprint data'
            };
        }
    }

    /**
   * Convert RAG color to numeric score
   * @param {string} color The RAG color: 'green', 'amber', or 'red'
   * @return {number} The numeric score: Green=1, Amber=0, Red=-1
   */
    static ragColorToScore (color) {
        switch (color) {
            case 'green':
                return 1;
            case 'amber':
                return 0;
            case 'red':
                return -1;
            default:
                return 0;
        }
    }
}

module.exports = ProjectSprintDataService;
