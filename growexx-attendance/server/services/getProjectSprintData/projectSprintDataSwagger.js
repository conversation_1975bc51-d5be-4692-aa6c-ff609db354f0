const message = require('../../locales/en.json');

module.exports = (swaggerJson) => {
    swaggerJson.paths['/project-sprint-data'] = {
        get: {
            security: [{
                bearerAuth: []
            }],
            tags: ['PLI - Project Sprint Data'],
            description: 'Get project sprint performance data for an employee for PLI evaluation',
            summary: 'Get project sprint data for PLI',
            parameters: [
                {
                    in: 'query',
                    name: 'project',
                    description: 'Project name',
                    required: true,
                    type: 'string'
                },
                {
                    in: 'query',
                    name: 'month',
                    description: 'Month number (1-12)',
                    required: true,
                    type: 'number'
                },
                {
                    in: 'query',
                    name: 'year',
                    description: 'Year (4-digit format)',
                    required: true,
                    type: 'number'
                },
                {
                    in: 'query',
                    name: 'employeeName',
                    description: 'Employee username (e.g., firstname.lastname)',
                    required: true,
                    type: 'string'
                },
                {
                    in: 'query',
                    name: 'projectType',
                    description: 'Project type (e.g., Dedicated, Fixed)',
                    required: false,
                    type: 'string',
                    default: 'Dedicated'
                },
                {
                    in: 'query',
                    name: 'weightage',
                    description: 'Project weightage for PLI calculation (percentage)',
                    required: false,
                    type: 'number',
                    default: 100
                }
            ],
            responses: {
                200: {
                    description: 'Project sprint data fetched successfully.',
                    schema: {
                        $ref: '#/definitions/successProjectSprintData'
                    }
                },
                400: {
                    description: 'Invalid request',
                    schema: {
                        $ref: '#/definitions/validationError'
                    }
                },
                401: {
                    description: 'Unauthorized Access',
                    schema: {
                        $ref: '#/definitions/unauthorisedAccess'
                    }
                },
                500: {
                    description: 'Internal Server Error',
                    schema: {
                        $ref: '#/definitions/serverError'
                    }
                }
            }
        }
    };

    swaggerJson.definitions.successProjectSprintData = {
        properties: {
            status: {
                type: 'number',
                example: 1
            },
            success: {
                type: 'boolean',
                example: true
            },
            data: {
                type: 'array',
                description: 'Array of PLI parameters with their sprint-wise scores',
                items: {
                    type: 'object',
                    properties: {
                        pliParameter: {
                            type: 'object',
                            properties: {
                                name: {
                                    type: 'string',
                                    example: 'RAG (Budget + Open/Close + B2D + Escalations)'
                                },
                                sprintwiseScores: {
                                    type: 'array',
                                    items: {
                                        type: 'object',
                                        properties: {
                                            sprintname: {
                                                type: 'string',
                                                example: 'Sprint 1'
                                            },
                                            score: {
                                                type: 'number',
                                                example: 1
                                            },
                                            comment: {
                                                type: 'string',
                                                example: 'Good performance'
                                            },
                                            fromPLIRating: {
                                                type: 'boolean',
                                                example: true,
                                                description: 'Indicates if score is from existing PLI rating'
                                            }
                                        }
                                    }
                                },
                                weightage: {
                                    type: 'number',
                                    example: 50
                                }
                            }
                        },
                        childParameterId: {
                            type: 'string',
                            example: 'RAG (Budget + Open/Close + B2D + Escalations)'
                        },
                        sprintScores: {
                            type: 'array',
                            items: {
                                type: 'object',
                                properties: {
                                    sprintNumber: {
                                        type: 'string',
                                        example: 'Sprint 1'
                                    },
                                    score: {
                                        type: 'number',
                                        example: 1
                                    },
                                    comment: {
                                        type: 'string',
                                        example: ''
                                    }
                                }
                            }
                        }
                    }
                }
            },
            menteeId: {
                type: 'string',
                example: '60d5a7f0b6e8a12345678901',
                description: 'MongoDB ObjectId of the mentee'
            },
            mentorId: {
                type: 'string',
                example: '60d5a7f0b6e8a12345678902',
                description: 'MongoDB ObjectId of the mentor'
            },
            month: {
                type: 'number',
                example: 5,
                description: 'Month number (1-12)'
            },
            year: {
                type: 'number',
                example: 2025,
                description: 'Year in 4-digit format'
            },
            projectId: {
                type: 'string',
                example: '60d5a7f0b6e8a12345678903',
                description: 'MongoDB ObjectId of the project'
            },
            projectWeightage: {
                type: 'number',
                example: 100,
                description: 'Weightage of this project in PLI calculation'
            },
            parameterId: {
                type: 'string',
                example: '682adfab4d34b5d668dd1813',
                description: 'MongoDB ObjectId of the single PLI parameters document'
            },
            comments: {
                type: 'string',
                example: '',
                description: 'Additional comments for the PLI evaluation'
            },
            message: {
                type: 'string',
                example: message.SUCCESS
            }
        }
    };

    swaggerJson.definitions.errorProjectSprintData = {
        properties: {
            status: {
                type: 'number',
                example: 0
            },
            success: {
                type: 'boolean',
                example: false
            },
            error: {
                type: 'string',
                example: 'Employee name is required'
            }
        }
    };

    // Add missing schema definitions
    if (!swaggerJson.definitions.validationError) {
        swaggerJson.definitions.validationError = {
            properties: {
                status: {
                    type: 'number',
                    example: 0
                },
                message: {
                    type: 'string',
                    example: 'Invalid request parameters'
                }
            }
        };
    }

    if (!swaggerJson.definitions.unauthorisedAccess) {
        swaggerJson.definitions.unauthorisedAccess = {
            properties: {
                status: {
                    type: 'number',
                    example: 0
                },
                message: {
                    type: 'string',
                    example: 'Unauthorized access'
                }
            }
        };
    }

    if (!swaggerJson.definitions.serverError) {
        swaggerJson.definitions.serverError = {
            properties: {
                status: {
                    type: 'number',
                    example: 0
                },
                message: {
                    type: 'string',
                    example: 'Internal server error'
                }
            }
        };
    }

    return swaggerJson;
};
