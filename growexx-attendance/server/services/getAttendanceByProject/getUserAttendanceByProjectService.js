const mongoose = require("mongoose");
const Project = require("../../models/project.model");
const User = require("../../models/user.model");

class FetchJiraLogsService {
  /**
   * @desc This function fetches logs grouped by user under each project
   *       thatjoins the project details with additional project information.
   * @since 18/12/2024
   * @param {Object} req Request object containing query parameters
   * @param {Object} user Logged-in user details
   * @param {Object} locale Locale for response messages
   * @return {Object} Response object with success status, message, and data
   */
  static async getLogsGroupedByWeek(req, user) {
    const requiredParams = ["projectId", "startDate", "endDate"];
    for (const param of requiredParams) {
      if (!req.query[param]) {
        return res.status(400).json({ error: `${param} is required` });
      }
    }

    const whereCondition = {};

    // Add projectId filter
    whereCondition._id = mongoose.Types.ObjectId(req.query.projectId);

    if (user.role !== CONSTANTS.ROLE.ADMIN && user.role !== CONSTANTS.ROLE.BU) {
      whereCondition.$or = [
        {
          users: {
            $elemMatch: {
              empId: mongoose.Types.ObjectId(user._id),
            },
          },
        },
        { pmUser: { $in: [mongoose.Types.ObjectId(user._id)] } },
        { reviewManager: mongoose.Types.ObjectId(user._id) },
      ];
    }

    const startDate = req.query.startDate
      ? MOMENT(req.query.startDate).format("YYYY-MM-DD")
      : getQuarterDates.firstDate;
    const endDate = req.query.endDate
      ? MOMENT(req.query.endDate).format("YYYY-MM-DD")
      : getQuarterDates.lastDate;

    const aggregateParams = this.buildAggregatePipeline(
      whereCondition,
      startDate,
      endDate
    );

    const aggregate = Project.aggregate(aggregateParams);
    return aggregate;
  }

  /**
   * @desc Constructs the aggregation pipeline
   * @param {Object} whereCondition Filters for project matching
   * @param {String} startDate Start date for logs
   * @param {String} endDate End date for logs
   * @return {Array} Aggregation pipeline
   */
  static buildAggregatePipeline(whereCondition, startDate, endDate) {
    return [
      { $match: whereCondition },
      {
        $lookup: {
          from: "logs",
          let: { projectName: "$projectName" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jiraProjectName", "$$projectName"] },
                    {
                      $gte: [
                        {
                          $dateToString: {
                            format: "%Y-%m-%d",
                            date: "$logDate",
                            timezone: "Asia/Calcutta",
                          },
                        },
                        startDate,
                      ],
                    },
                    {
                      $lte: [
                        {
                          $dateToString: {
                            format: "%Y-%m-%d",
                            date: "$logDate",
                            timezone: "Asia/Calcutta",
                          },
                        },
                        endDate,
                      ],
                    },
                  ],
                },
              },
            },
          ],
          as: "logDetails",
        },
      },
      { $unwind: { path: "$logDetails", preserveNullAndEmptyArrays: true } },
      {
        $group: {
          _id: { userId: "$logDetails.userId", projectName: "$projectName" },
          userId: { $first: "$logDetails.userId" },
          manDays: { $sum: "$logDetails.timeSpentHours" },
          projectId: { $first: "$_id" },
          pmUser: { $first: "$pmUser" },
          reviewManager: { $first: "$reviewManager" },
          isActive: { $first: "$isActive" },
          startDate: { $first: "$startDate" },
          endDate: { $first: "$endDate" },
          projectState: { $first: "$projectState" },
          projectName: { $first: "$projectName" },
          businessUnitId: { $first: "$businessUnitId" },
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "userId",
          foreignField: "_id",
          as: "userDetails",
        },
      },
      {
        $match: {
          "userDetails.isActive": 1,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "pmUser",
          foreignField: "_id",
          as: "reportingManager",
        },
      },
      { $unwind: { path: "$userDetails", preserveNullAndEmptyArrays: true } },
      {
        $unwind: {
          path: "$reportingManager",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "reviewManager",
          foreignField: "_id",
          as: "reviewManagerDetails",
        },
      },
      {
        $unwind: {
          path: "$reviewManagerDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "logs",
          let: { userId: "$userDetails._id", projectName: "$projectName" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$jiraProjectName", "$$projectName"] },
                    { $eq: ["$userId", "$$userId"] },
                    { $eq: ["$isActive", 1] },
                    {
                      $gte: [
                        {
                          $dateToString: {
                            format: "%Y-%m-%d",
                            date: "$logDate",
                            timezone: "Asia/Calcutta",
                          },
                        },
                        startDate,
                      ],
                    },
                    {
                      $lte: [
                        {
                          $dateToString: {
                            format: "%Y-%m-%d",
                            date: "$logDate",
                            timezone: "Asia/Calcutta",
                          },
                        },
                        endDate,
                      ],
                    },
                  ],
                },
              },
            },
            { $sort: { logDate: 1 } },
            {
              $group: {
                _id: {
                  logDate: {
                    $dateToString: {
                      format: "%Y-%m-%d",
                      date: "$logDate",
                      timezone: "Asia/Calcutta",
                    },
                  },
                },
                logs: {
                  $push: {
                    _id: "$_id",
                    jiraTitle: "$jiraTitle",
                    logStatus: "$logStatus",
                    userId: "$userId",
                    projectId: "$projectId",
                    projectName: "$projectName",
                    jiraUserEmail: "$jiraUserEmail",
                    label: "$label",
                    logDate: "$logDate",
                    timeSpentSeconds: "$timeSpentSeconds",
                    timeSpentHours: "$timeSpentHours",
                    daysDeviation: "$daysDeviation",
                    deviations: "$deviations",
                    jiraProjectName: "$jiraProjectName",
                    jiraIssueUrl: "$jiraIssueUrl",
                    isActive: "$isActive",
                  },
                },
              },
            },
            { $sort: { "_id.logDate": 1 } },
          ],
          as: "userLogs",
        },
      },
      {
        $group: {
          _id: "$projectId",
          projectId: { $first: "$projectId" },
          projectName: { $first: "$projectName" },
          projectNameLower: { $first: { $toLower: "$projectName" } },
          isActive: { $first: "$isActive" },
          startDate: { $first: "$startDate" },
          endDate: { $first: "$endDate" },
          projectState: { $first: "$projectState" },
          businessUnitId: { $first: "$businessUnitId" },
          reviewManager: {
            $first: {
              _id: "$reviewManagerDetails._id",
              firstName: "$reviewManagerDetails.firstName",
              lastName: "$reviewManagerDetails.lastName",
            },
          },
          reportingManager: {
            $first: {
              _id: "$reportingManager._id",
              firstName: "$reportingManager.firstName",
              lastName: "$reportingManager.lastName",
            },
          },
          team: {
            $push: {
              empId: "$userDetails._id",
              manDays: { $divide: ["$manDays", 8] },
              firstName: "$userDetails.firstName",
              lastName: "$userDetails.lastName",
              empRole: "$userDetails.designation",
              logsByDate: {
                $map: {
                  input: "$userLogs",
                  as: "logGroup",
                  in: {
                    date: "$$logGroup._id.logDate",
                    logs: "$$logGroup.logs",
                  },
                },
              },
            },
          },
        },
      },
      { $unwind: { path: "$team" } },
      { $sort: { "team.firstName": 1, "team.lastName": 1 } },
      {
        $group: {
          _id: "$_id",
          projectId: { $first: "$projectId" },
          projectName: { $first: "$projectName" },
          projectNameLower: { $first: "$projectNameLower" },
          isActive: { $first: "$isActive" },
          startDate: { $first: "$startDate" },
          endDate: { $first: "$endDate" },
          projectState: { $first: "$projectState" },
          businessUnitId: { $first: "$businessUnitId" },
          reviewManager: { $first: "$reviewManager" },
          reportingManager: { $first: "$reportingManager" },
          team: { $push: "$team" },
        },
      },
    ];
  }
}

module.exports = FetchJiraLogsService;
