/**
 * SuperadminCourse model
 */
const appMongoose = require('mongoose');

const superadminCourseSchema = new appMongoose.Schema({
    courseName: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    duration: {
        type: String,
        trim: true
    },
    learningMedium: {
        type: String,
        trim: true
    },
    role: {
        type: String,
        required: true,
        trim: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = appMongoose.model('SuperadminCourse', superadminCourseSchema);
