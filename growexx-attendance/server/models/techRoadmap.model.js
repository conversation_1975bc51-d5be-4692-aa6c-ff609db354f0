/**
 * @name Tech Roadmap model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    courseName: {
        type: String,
        required: true,
        trim: true
    },
    description: {
        type: String,
        trim: true
    },
    duration: {
        type: String,
        trim: true
    },
    dueDate: {
        type: Date
    },
    learningMedium: {
        type: String,
        trim: true
    },
    link: {
        type: String,
        trim: true
    },
    menteeId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user',
        required: true
    },
    mentorId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user',
        required: true
    },
    documents: [{
        documentLink: {
            type: String,
            trim: true
        },
        documentKey: {
            type: String,
            trim: true
        },
        documentName: {
            type: String,
            trim: true
        },
        uploadedAt: {
            type: Date,
            default: Date.now
        },
        approvalStatus: {
            type: String,
            enum: ['pending', 'approved', 'rejected'],
            default: 'pending'
        },
        approvedBy: {
            type: appMongoose.Schema.Types.ObjectId,
            ref: 'user'
        },
        approvalDate: {
            type: Date
        },
        approvalComment: {
            type: String,
            trim: true
        }
    }],
    completionStatus: {
        type: String,
        enum: ['Assigned', 'Completed', 'Rated'],
        default: 'Assigned'
    },
    completionPercentage: {
        type: Number,
        min: 0,
        max: 100,
        default: 0
    },
    rating: {
        type: Number,
        min: 0,
        max: 5
    },
    mentorComments: {
        type: String,
        trim: true,
        default: ''
    },
    menteeComments: {
        type: String,
        trim: true,
        default: ''
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('techRoadmap', schema);