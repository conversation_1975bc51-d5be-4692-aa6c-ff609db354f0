const dotenv = require('dotenv');
const env = process.env.NODE_ENV || 'local';
dotenv.config({ path: __dirname + '/../' + env + '.env' });
const jwt = require('jsonwebtoken');

global.DB_CONNECTION = require('mongoose');
global.CONSTANTS = require('../server/util/constants');
global.CONSOLE_LOGGER = require('../server/util/logger');
global.MOMENT = require('moment');
global._ = require('lodash');
const mongoose = require('mongoose');
mongoose.set('strictQuery', false);

const Logs = require('../server/models/logs.model');
const User = require('../server/models/user.model');
const Project = require('../server/models/project.model');
const Email = require('../server/util/sendEmail');

let dbUrl = `mongodb://${process.env.DB_HOST}/${process.env.DB_NAME}`;
if (process.env.DB_USERNAME) {
    const cred = `${process.env.DB_USERNAME}:${encodeURIComponent(process.env.DB_PASSWORD)}`;
    dbUrl = `mongodb+srv://${cred}@${process.env.DB_HOST}/${process.env.DB_NAME}`;
}

const options = {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    bufferCommands: true,
    maxPoolSize: 10
};

const connectDB = async () => {
    try {
        return await DB_CONNECTION.connect(dbUrl, options);
    } catch (err) {
        CONSOLE_LOGGER.info('MongoDB connection error.', err);
        return false;
    }
};

const fetchMissingLogDatesWithUserDetails = async () => {
    try {
        const startDate = new Date(new Date().getFullYear(), new Date().getMonth(), 2);
        const today = new Date();
        const timezone = "Asia/Kolkata";

        const pipeline = [
            {
                $match: {
                    // remove below line in production to get data for all the users
                    _id: new mongoose.Types.ObjectId('6149891f3a2ea95a8d390c0f'),
                    isActive: 1
                }
            },
            // Lookup logs
            {
                $lookup: {
                    from: "logs",
                    let: { userId: "$_id" },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ["$userId", "$$userId"] },
                                logDate: { $gte: startDate, $lte: today },
                                isActive: 1
                            }
                        },
                        {
                            $addFields: {
                                formattedDate: { $dateToString: { format: "%Y-%m-%d", date: "$logDate", timezone: timezone } }
                            }
                        },
                        {
                            $group: {
                                _id: "$formattedDate",
                                totalLoggedHours: { $sum: "$timeSpentHours" }
                            }
                        }
                    ],
                    as: "userLogs"
                }
            },
            // Lookup leaves
            {
                $lookup: {
                    from: "leaves",
                    let: { userId: "$_id" },
                    pipeline: [
                        {
                            $match: {
                                $expr: { $eq: ["$userId", "$$userId"] },
                                isDelete: 0,
                                // status: "approved",
                                leaveDate: { $gte: startDate, $lte: today }
                            }
                        },
                        {
                            $addFields: {
                                formattedLeaveDate: { $dateToString: { format: "%Y-%m-%d", date: "$leaveDate", timezone: timezone } }
                            }
                        },
                        {
                            $group: {
                                _id: "$formattedLeaveDate",
                                totalLeaveHours: { $sum: "$timeSpentHours" }
                            }
                        }
                    ],
                    as: "userLeaves"
                }
            },
            // Calculate total days between startDate and today
            {
                $addFields: {
                    totalDays: {
                        $toInt: {
                            $add: [
                                { $divide: [{ $subtract: [today, startDate] }, 86400000] },
                                1
                            ]
                        }
                    }
                }
            },
            // Generate all working dates
            {
                $addFields: {
                    allDates: {
                        $filter: {
                            input: {
                                $map: {
                                    input: { $range: [0, "$totalDays"] },
                                    as: "dayOffset",
                                    in: {
                                        $dateToString: {
                                            format: "%Y-%m-%d",
                                            date: { $add: [startDate, { $multiply: ["$$dayOffset", 86400000] }] },
                                            timezone: "Asia/Kolkata"
                                        }
                                    }
                                }
                            },
                            as: "date",
                            cond: {
                                $not: {
                                    $in: [
                                        { $dayOfWeek: { $dateFromString: { dateString: "$$date" } } },
                                        [1, 7]
                                    ]
                                }
                            }
                        }
                    }
                }
            },
            // Identify missing logs and hours
            {
                $addFields: {
                    missingLogsWithHours: {
                        $map: {
                            input: "$allDates",
                            as: "date",
                            in: {
                                $let: {
                                    vars: {
                                        log: {
                                            $first: {
                                                $filter: {
                                                    input: "$userLogs",
                                                    as: "log",
                                                    cond: { $eq: ["$$log._id", "$$date"] }
                                                }
                                            }
                                        },
                                        leave: {
                                            $first: {
                                                $filter: {
                                                    input: "$userLeaves",
                                                    as: "leave",
                                                    cond: { $eq: ["$$leave._id", "$$date"] }
                                                }
                                            }
                                        }
                                    },
                                    in: {
                                        date: "$$date",
                                        missingHours: {
                                            $let: {
                                                vars: {
                                                    totalLogged: { $ifNull: ["$$log.totalLoggedHours", 0] },
                                                    totalLeave: { $ifNull: ["$$leave.totalLeaveHours", 0] }
                                                },
                                                in: {
                                                    $cond: [
                                                        { $gte: [{ $add: ["$$totalLogged", "$$totalLeave"] }, 8] },
                                                        0,
                                                        { $subtract: [8, { $add: ["$$totalLogged", "$$totalLeave"] }] }
                                                    ]
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            // Filter only dates where missingHours > 0
            {
                $addFields: {
                    missingLogsWithHours: {
                        $filter: {
                            input: "$missingLogsWithHours",
                            as: "item",
                            cond: { $gt: ["$$item.missingHours", 0] }
                        }
                    }
                }
            },
            // Final projection
            {
                $project: {
                    _id: 0,
                    userId: "$_id",
                    name: { $concat: ["$firstName", " ", "$lastName"] },
                    email: "$email",
                    missingLogsWithHours: 1
                }
            }
        ];

        const result = await User.aggregate(pipeline);
        return result;
    } catch (error) {
        console.error("Error fetching missing log dates with user details:", error);
        throw error;
    }
};

const notifyPendingLogs = async () => {
    try {
        await connectDB();
        const fetchMissingLogDates = await fetchMissingLogDatesWithUserDetails();
        //console.log(JSON.stringify(fetchMissingLogDates, null, 2));

        await Promise.all(
            fetchMissingLogDates.map(async (user) => {
                const tableData = user.missingLogsWithHours
                    .map(({ date, missingHours }) => {
                        const formattedDate = new Date(date).toLocaleDateString("en-US", {
                            weekday: "long",
                            day: "2-digit",
                            month: "short",
                            year: "numeric",
                        });
                        const [weekday, month, day, year] = formattedDate.replace(/,/g, "").split(" ");
                        const finalDate = `${weekday} - ${day} ${month} ${year}`;

                        return `<tr>
                            <td style="padding:10px;border:1px solid #eeeeee;font-size:14px;color:#000;">
                                ${finalDate}
                            </td>
                            <td style="padding:10px;border:1px solid #eeeeee;font-size:14px;color:#000;text-align:center;">
                                ${missingHours} ${missingHours > 1 ? "hours" : "hour"}
                            </td>
                        </tr>`;
                    })
                    .join("");

                /** @note check if there is any missing logs for particular date or not */
                if (tableData.length) {
                    const templateVariables = {
                        USERNAME: user.name,
                        ACTIONURL: `${process.env.FRONTEND_URL}/logs`,
                        TABLEDATA: tableData,
                        YEAR: new Date().getFullYear(),
                        APPURL: process.env.FRONTEND_URL,
                    };

                    const subject = 'Reminder: Complete Your Missing Timesheet Logs';
                    const templatePath = 'emailTemplates/missedAddingWorkLogs.html';

                    try {
                        await Email.prepareAndSendEmail(
                            //[user.email],  // Uncomment for production and remove hardcoded values
                            ['<EMAIL>', '<EMAIL>'],
                            subject,
                            templatePath,
                            templateVariables
                        );
                        console.log(`✅ Email sent successfully to ${user.email}`);
                    } catch (error) {
                        console.error(`❌ Failed to send email to ${user.email}:`, error);
                    }
                }
            })
        );
        process.exit(0);
    } catch (error) {
        console.error('Error sending email notifications for pending logs:', error);
        process.exit(1);
    }
};


notifyPendingLogs();